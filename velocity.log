2025-07-28 14:18:05,168 - Initializing Velocity, Calling init()...
2025-07-28 14:18:05,168 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-28 14:18:05,168 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-28 14:18:05,168 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-28 14:18:05,168 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/Priority).  Falling back to next log system...
2025-07-28 14:18:05,168 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-28 14:18:05,168 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-28 14:18:05,176 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-28 14:18:05,178 - Do unicode file recognition:  false
2025-07-28 14:18:05,179 - FileResourceLoader : adding path '.'
2025-07-28 14:18:05,194 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-28 14:18:05,196 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-28 14:18:05,197 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-28 14:18:05,197 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-28 14:18:05,198 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-28 14:18:05,198 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-28 14:18:05,199 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-28 14:18:05,200 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-28 14:18:05,200 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-28 14:18:05,201 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-28 14:18:05,214 - Created '20' parsers.
2025-07-28 14:18:05,218 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-28 14:18:05,218 - Velocimacro : Default library not found.
2025-07-28 14:18:05,218 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-28 14:18:05,218 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-28 14:18:05,218 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-28 14:18:05,218 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-28 14:45:04,511 - Log4JLogChute initialized using file 'velocity.log'
2025-07-28 14:45:04,511 - Initializing Velocity, Calling init()...
2025-07-28 14:45:04,512 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-28 14:45:04,512 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-28 14:45:04,512 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-28 14:45:04,512 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/Priority).  Falling back to next log system...
2025-07-28 14:45:04,512 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-28 14:45:04,512 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-28 14:45:04,518 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-28 14:45:04,521 - Do unicode file recognition:  false
2025-07-28 14:45:04,521 - FileResourceLoader : adding path '.'
2025-07-28 14:45:04,533 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-28 14:45:04,538 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-28 14:45:04,540 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-28 14:45:04,542 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-28 14:45:04,543 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-28 14:45:04,544 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-28 14:45:04,545 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-28 14:45:04,548 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-28 14:45:04,549 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-28 14:45:04,550 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-28 14:45:04,571 - Created '20' parsers.
2025-07-28 14:45:04,574 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-28 14:45:04,574 - Velocimacro : Default library not found.
2025-07-28 14:45:04,574 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-28 14:45:04,574 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-28 14:45:04,574 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-28 14:45:04,575 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-28 15:43:26,834 - Log4JLogChute initialized using file 'velocity.log'
2025-07-28 15:43:26,834 - Initializing Velocity, Calling init()...
2025-07-28 15:43:26,835 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-28 15:43:26,835 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-28 15:43:26,835 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-28 15:43:26,835 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/Priority).  Falling back to next log system...
2025-07-28 15:43:26,835 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-28 15:43:26,835 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-28 15:43:26,838 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-28 15:43:26,840 - Do unicode file recognition:  false
2025-07-28 15:43:26,840 - FileResourceLoader : adding path '.'
2025-07-28 15:43:26,849 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-28 15:43:26,851 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-28 15:43:26,852 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-28 15:43:26,853 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-28 15:43:26,854 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-28 15:43:26,854 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-28 15:43:26,855 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-28 15:43:26,856 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-28 15:43:26,856 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-28 15:43:26,857 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-28 15:43:26,870 - Created '20' parsers.
2025-07-28 15:43:26,872 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-28 15:43:26,873 - Velocimacro : Default library not found.
2025-07-28 15:43:26,873 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-28 15:43:26,873 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-28 15:43:26,873 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-28 15:43:26,873 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-28 15:53:05,777 - Log4JLogChute initialized using file 'velocity.log'
2025-07-28 15:53:05,777 - Initializing Velocity, Calling init()...
2025-07-28 15:53:05,777 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-28 15:53:05,777 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-28 15:53:05,777 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-28 15:53:05,777 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/Priority).  Falling back to next log system...
2025-07-28 15:53:05,777 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-28 15:53:05,777 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-28 15:53:05,781 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-28 15:53:05,781 - Do unicode file recognition:  false
2025-07-28 15:53:05,781 - FileResourceLoader : adding path '.'
2025-07-28 15:53:05,788 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-28 15:53:05,790 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-28 15:53:05,792 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-28 15:53:05,792 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-28 15:53:05,793 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-28 15:53:05,793 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-28 15:53:05,794 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-28 15:53:05,795 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-28 15:53:05,795 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-28 15:53:05,796 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-28 15:53:05,807 - Created '20' parsers.
2025-07-28 15:53:05,809 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-28 15:53:05,809 - Velocimacro : Default library not found.
2025-07-28 15:53:05,809 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-28 15:53:05,809 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-28 15:53:05,809 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-28 15:53:05,809 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-28 16:24:45,724 - Log4JLogChute initialized using file 'velocity.log'
2025-07-28 16:24:45,724 - Initializing Velocity, Calling init()...
2025-07-28 16:24:45,724 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-28 16:24:45,724 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-28 16:24:45,724 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-28 16:24:45,724 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/Priority).  Falling back to next log system...
2025-07-28 16:24:45,724 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-28 16:24:45,724 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-28 16:24:45,728 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-28 16:24:45,729 - Do unicode file recognition:  false
2025-07-28 16:24:45,729 - FileResourceLoader : adding path '.'
2025-07-28 16:24:45,736 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-28 16:24:45,738 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-28 16:24:45,739 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-28 16:24:45,739 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-28 16:24:45,740 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-28 16:24:45,740 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-28 16:24:45,741 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-28 16:24:45,742 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-28 16:24:45,742 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-28 16:24:45,743 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-28 16:24:45,754 - Created '20' parsers.
2025-07-28 16:24:45,755 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-28 16:24:45,756 - Velocimacro : Default library not found.
2025-07-28 16:24:45,756 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-28 16:24:45,756 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-28 16:24:45,756 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-28 16:24:45,756 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-28 16:26:09,520 - Log4JLogChute initialized using file 'velocity.log'
2025-07-28 16:26:09,520 - Initializing Velocity, Calling init()...
2025-07-28 16:26:09,520 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-28 16:26:09,520 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-28 16:26:09,520 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-28 16:26:09,521 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/Priority).  Falling back to next log system...
2025-07-28 16:26:09,521 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-28 16:26:09,521 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-28 16:26:09,524 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-28 16:26:09,526 - Do unicode file recognition:  false
2025-07-28 16:26:09,526 - FileResourceLoader : adding path '.'
2025-07-28 16:26:09,535 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-28 16:26:09,537 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-28 16:26:09,538 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-28 16:26:09,539 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-28 16:26:09,539 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-28 16:26:09,540 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-28 16:26:09,540 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-28 16:26:09,541 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-28 16:26:09,541 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-28 16:26:09,542 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-28 16:26:09,553 - Created '20' parsers.
2025-07-28 16:26:09,554 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-28 16:26:09,555 - Velocimacro : Default library not found.
2025-07-28 16:26:09,555 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-28 16:26:09,555 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-28 16:26:09,555 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-28 16:26:09,555 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-28 16:31:54,703 - Log4JLogChute initialized using file 'velocity.log'
2025-07-28 16:31:54,703 - Initializing Velocity, Calling init()...
2025-07-28 16:31:54,703 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-28 16:31:54,703 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-28 16:31:54,703 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-28 16:31:54,703 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/Priority).  Falling back to next log system...
2025-07-28 16:31:54,703 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-28 16:31:54,703 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-28 16:31:54,707 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-28 16:31:54,708 - Do unicode file recognition:  false
2025-07-28 16:31:54,708 - FileResourceLoader : adding path '.'
2025-07-28 16:31:54,715 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-28 16:31:54,717 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-28 16:31:54,718 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-28 16:31:54,719 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-28 16:31:54,719 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-28 16:31:54,720 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-28 16:31:54,720 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-28 16:31:54,721 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-28 16:31:54,722 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-28 16:31:54,722 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-28 16:31:54,733 - Created '20' parsers.
2025-07-28 16:31:54,735 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-28 16:31:54,735 - Velocimacro : Default library not found.
2025-07-28 16:31:54,735 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-28 16:31:54,735 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-28 16:31:54,735 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-28 16:31:54,735 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-28 16:34:50,281 - Log4JLogChute initialized using file 'velocity.log'
2025-07-28 16:34:50,282 - Initializing Velocity, Calling init()...
2025-07-28 16:34:50,282 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-28 16:34:50,282 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-28 16:34:50,282 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-28 16:34:50,282 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/Priority).  Falling back to next log system...
2025-07-28 16:34:50,282 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-28 16:34:50,282 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-28 16:34:50,285 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-28 16:34:50,286 - Do unicode file recognition:  false
2025-07-28 16:34:50,286 - FileResourceLoader : adding path '.'
2025-07-28 16:34:50,294 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-28 16:34:50,297 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-28 16:34:50,298 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-28 16:34:50,298 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-28 16:34:50,299 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-28 16:34:50,299 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-28 16:34:50,300 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-28 16:34:50,301 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-28 16:34:50,301 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-28 16:34:50,302 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-28 16:34:50,315 - Created '20' parsers.
2025-07-28 16:34:50,316 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-28 16:34:50,316 - Velocimacro : Default library not found.
2025-07-28 16:34:50,316 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-28 16:34:50,316 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-28 16:34:50,317 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-28 16:34:50,317 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-28 16:38:14,430 - Log4JLogChute initialized using file 'velocity.log'
2025-07-28 16:38:14,430 - Initializing Velocity, Calling init()...
2025-07-28 16:38:14,430 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-28 16:38:14,430 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-28 16:38:14,430 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-28 16:38:14,430 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/Priority).  Falling back to next log system...
2025-07-28 16:38:14,430 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-28 16:38:14,430 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-28 16:38:14,433 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-28 16:38:14,434 - Do unicode file recognition:  false
2025-07-28 16:38:14,434 - FileResourceLoader : adding path '.'
2025-07-28 16:38:14,441 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-28 16:38:14,444 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-28 16:38:14,445 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-28 16:38:14,445 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-28 16:38:14,446 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-28 16:38:14,446 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-28 16:38:14,447 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-28 16:38:14,448 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-28 16:38:14,448 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-28 16:38:14,449 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-28 16:38:14,460 - Created '20' parsers.
2025-07-28 16:38:14,462 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-28 16:38:14,462 - Velocimacro : Default library not found.
2025-07-28 16:38:14,462 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-28 16:38:14,462 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-28 16:38:14,462 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-28 16:38:14,462 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 10:35:47,220 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 10:35:47,221 - Initializing Velocity, Calling init()...
2025-07-29 10:35:47,221 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 10:35:47,221 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 10:35:47,221 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 10:35:47,221 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/Priority).  Falling back to next log system...
2025-07-29 10:35:47,221 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 10:35:47,221 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 10:35:47,227 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 10:35:47,228 - Do unicode file recognition:  false
2025-07-29 10:35:47,228 - FileResourceLoader : adding path '.'
2025-07-29 10:35:47,236 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 10:35:47,238 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 10:35:47,239 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 10:35:47,240 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 10:35:47,241 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 10:35:47,241 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 10:35:47,242 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 10:35:47,243 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 10:35:47,243 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 10:35:47,244 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 10:35:47,256 - Created '20' parsers.
2025-07-29 10:35:47,258 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 10:35:47,258 - Velocimacro : Default library not found.
2025-07-29 10:35:47,258 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 10:35:47,258 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 10:35:47,258 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 10:35:47,258 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-30 15:25:48,105 - Log4JLogChute initialized using file 'velocity.log'
2025-07-30 15:25:48,106 - Initializing Velocity, Calling init()...
2025-07-30 15:25:48,106 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-30 15:25:48,106 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-30 15:25:48,106 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-30 15:25:48,106 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/Priority).  Falling back to next log system...
2025-07-30 15:25:48,106 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-30 15:25:48,106 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-30 15:25:48,109 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-30 15:25:48,110 - Do unicode file recognition:  false
2025-07-30 15:25:48,110 - FileResourceLoader : adding path '.'
2025-07-30 15:25:48,118 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-30 15:25:48,120 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-30 15:25:48,121 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-30 15:25:48,122 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-30 15:25:48,122 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-30 15:25:48,123 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-30 15:25:48,123 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-30 15:25:48,124 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-30 15:25:48,125 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-30 15:25:48,125 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-30 15:25:48,136 - Created '20' parsers.
2025-07-30 15:25:48,137 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-30 15:25:48,138 - Velocimacro : Default library not found.
2025-07-30 15:25:48,138 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-30 15:25:48,138 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-30 15:25:48,138 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-30 15:25:48,138 - Velocimacro : autoload off : VM system will not automatically reload global library macros
