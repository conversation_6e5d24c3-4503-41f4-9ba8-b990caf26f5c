package inks.service.sa.crm.domain;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * 样品发货(SaSmpdeli)实体类
 *
 * <AUTHOR>
 * @since 2025-06-30 16:36:27
 */
@Data
public class SaSmpdeliEntity implements Serializable {
    private static final long serialVersionUID = -20839079076947419L;
     // id
    private String id;
     // 编码
    private String refno;
     // 单据类型
    private String billtype;
     // 单据标题
    private String billtitle;
     // 单据日期
    private Date billdate;
     // 客户id
    private String groupid;
     // 商机id
    private String businessid;
     // 联系电话
    private String telephone;
     // 联系人
    private String linkman;
     // 送货地址
    private String deliadd;
     // 税率(备注)
    private Integer taxrate;
     // 运输方式
    private String transport;
     // 业务员
    private String salesman;
     // 业务员id
    private String salesmanid;
     // 经办人
    private String operator;
     // 经办人id
    private String operatorid;
     // 摘要
    private String summary;
     // 创建者
    private String createby;
     // 创建者id
    private String createbyid;
     // 新建日期
    private Date createdate;
     // 制表
    private String lister;
     // 制表id
    private String listerid;
     // 修改日期
    private Date modifydate;
     // 审核员
    private String assessor;
     // 审核员id
    private String assessorid;
     // 审核日期
    private Date assessdate;
     // 单据状态
    private String billstatecode;
     // 状态日期
    private Date billstatedate;
     // 含税金额
    private Double billtaxamount;
     // 税额
    private Double billtaxtotal;
     // 未税金额
    private Double billamount;
     // 已收款
    private Double billreceived;
     // item行数
    private Integer itemcount;
     // 完成行数
    private Integer finishcount;
     // 作废行数
    private Integer disannulcount;
     // 打印次数
    private Integer printcount;
     // 自定义1
    private String custom1;
     // 自定义2
    private String custom2;
     // 自定义3
    private String custom3;
     // 自定义4
    private String custom4;
     // 自定义5
    private String custom5;
     // 自定义6
    private String custom6;
     // 自定义7
    private String custom7;
     // 自定义8
    private String custom8;
     // 自定义9
    private String custom9;
     // 自定义10
    private String custom10;
     // 部门id
    private String deptid;
     // 租户id
    private String tenantid;
     // 租户名称
    private String tenantname;
     // 乐观锁
    private Integer revision;


}

