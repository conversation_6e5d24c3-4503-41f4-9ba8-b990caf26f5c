package inks.service.sa.crm.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;
import java.util.List;
import lombok.Data;

/**
 * 需求调研(SaDemandresearch)实体类
 *
 * <AUTHOR>
 * @since 2024-10-11 10:15:06
 */
@Data
public class SaDemandresearchPojo implements Serializable {
    private static final long serialVersionUID = 568350333512878148L;
     // ID
     @Excel(name = "ID")
    private String id;
     // 单号
     @Excel(name = "单号")
    private String refno;
     // 单据类型
     @Excel(name = "单据类型")
    private String billtype;
     // 标题
     @Excel(name = "标题")
    private String billtitle;
     // 日期
     @Excel(name = "日期")
    private Date billdate;
     // 客户ID
     @Excel(name = "客户ID")
    private String groupid;
     // 客户名称
     @Excel(name = "客户名称")
    private String groupname;
     // 需求描述
     @Excel(name = "需求描述")
    private String description;
     // 关联的产品或服务
     @Excel(name = "关联的产品或服务")
    private String relatedservice;
     // 期望完成日期
     @Excel(name = "期望完成日期")
    private Date expecteddate;
     // 审核员
     @Excel(name = "审核员")
    private String assessor;
     // 审核员id
     @Excel(name = "审核员id")
    private String assessorid;
     // 审核日期
     @Excel(name = "审核日期")
    private Date assessdate;
     // 行号
     @Excel(name = "行号")
    private Integer rownum;
     // 备注
     @Excel(name = "备注")
    private String remark;
     // 创建者
     @Excel(name = "创建者")
    private String createby;
     // 创建者id
     @Excel(name = "创建者id")
    private String createbyid;
     // 新建日期
     @Excel(name = "新建日期")
    private Date createdate;
     // 制表
     @Excel(name = "制表")
    private String lister;
     // 制表id
     @Excel(name = "制表id")
    private String listerid;
     // 修改日期
     @Excel(name = "修改日期")
    private Date modifydate;
     // 租户id
     @Excel(name = "租户id")
    private String tenantid;
     // 租户名称
     @Excel(name = "租户名称")
    private String tenantname;
     // 乐观锁
     @Excel(name = "乐观锁")
    private Integer revision;
    // 子表
    private List<SaDemandresearchitemPojo> item;
    

}

