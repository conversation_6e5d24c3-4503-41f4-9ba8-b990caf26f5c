package inks.service.sa.crm.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;
import java.util.List;
import lombok.Data;

/**
 * 样品发货(SaSmpdeli)实体类
 *
 * <AUTHOR>
 * @since 2024-09-03 16:20:28
 */
@Data
public class SaSmpdeliPojo implements Serializable {
    private static final long serialVersionUID = -90743338056104637L;
     // id
     @Excel(name = "id")
    private String id;
     // 编码
     @Excel(name = "编码")
    private String refno;
     // 单据类型
     @Excel(name = "单据类型")
    private String billtype;
     // 单据标题
     @Excel(name = "单据标题")
    private String billtitle;
     // 单据日期
     @Excel(name = "单据日期")
    private Date billdate;
     // 客户id
     @Excel(name = "客户id")
    private String groupid;
     // 商机id
     @Excel(name = "商机id")
    private String businessid;
     // 联系电话
     @Excel(name = "联系电话")
    private String telephone;
     // 联系人
     @Excel(name = "联系人")
    private String linkman;
     // 送货地址
     @Excel(name = "送货地址")
    private String deliadd;
     // 税率(备注)
     @Excel(name = "税率(备注)")
    private Integer taxrate;
     // 运输方式
     @Excel(name = "运输方式")
    private String transport;
     // 业务员
     @Excel(name = "业务员")
    private String salesman;
     // 业务员id
     @Excel(name = "业务员id")
    private String salesmanid;
     // 经办人
     @Excel(name = "经办人")
    private String operator;
     // 经办人id
     @Excel(name = "经办人id")
    private String operatorid;
     // 摘要
     @Excel(name = "摘要")
    private String summary;
     // 创建者
     @Excel(name = "创建者")
    private String createby;
     // 创建者id
     @Excel(name = "创建者id")
    private String createbyid;
     // 新建日期
     @Excel(name = "新建日期")
    private Date createdate;
     // 制表
     @Excel(name = "制表")
    private String lister;
     // 制表id
     @Excel(name = "制表id")
    private String listerid;
     // 修改日期
     @Excel(name = "修改日期")
    private Date modifydate;
     // 审核员
     @Excel(name = "审核员")
    private String assessor;
     // 审核员id
     @Excel(name = "审核员id")
    private String assessorid;
     // 审核日期
     @Excel(name = "审核日期")
    private Date assessdate;
     // 单据状态
     @Excel(name = "单据状态")
    private String billstatecode;
     // 状态日期
     @Excel(name = "状态日期")
    private Date billstatedate;
     // 含税金额
     @Excel(name = "含税金额")
    private Double billtaxamount;
     // 税额
     @Excel(name = "税额")
    private Double billtaxtotal;
     // 未税金额
     @Excel(name = "未税金额")
    private Double billamount;
     // 已收款
     @Excel(name = "已收款")
    private Double billreceived;
     // item行数
     @Excel(name = "item行数")
    private Integer itemcount;
     // 完成行数
     @Excel(name = "完成行数")
    private Integer finishcount;
     // 作废行数
     @Excel(name = "作废行数")
    private Integer disannulcount;
     // 打印次数
     @Excel(name = "打印次数")
    private Integer printcount;
     // 自定义1
     @Excel(name = "自定义1")
    private String custom1;
     // 自定义2
     @Excel(name = "自定义2")
    private String custom2;
     // 自定义3
     @Excel(name = "自定义3")
    private String custom3;
     // 自定义4
     @Excel(name = "自定义4")
    private String custom4;
     // 自定义5
     @Excel(name = "自定义5")
    private String custom5;
     // 自定义6
     @Excel(name = "自定义6")
    private String custom6;
     // 自定义7
     @Excel(name = "自定义7")
    private String custom7;
     // 自定义8
     @Excel(name = "自定义8")
    private String custom8;
     // 自定义9
     @Excel(name = "自定义9")
    private String custom9;
     // 自定义10
     @Excel(name = "自定义10")
    private String custom10;
     // 部门id
     @Excel(name = "部门id")
    private String deptid;
     // 租户id
     @Excel(name = "租户id")
    private String tenantid;
     // 租户名称
     @Excel(name = "租户名称")
    private String tenantname;
     // 乐观锁
     @Excel(name = "乐观锁")
    private Integer revision;
    // 子表
    private List<SaSmpdeliitemPojo> item;


    private String custname;
    private String businesstitle;
}

