package inks.service.sa.crm.domain;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * 需求调研(SaDemandresearch)实体类
 *
 * <AUTHOR>
 * @since 2024-10-11 10:15:06
 */
@Data
public class SaDemandresearchEntity implements Serializable {
    private static final long serialVersionUID = 574963033800742782L;
     // ID
    private String id;
     // 单号
    private String refno;
     // 单据类型
    private String billtype;
     // 标题
    private String billtitle;
     // 日期
    private Date billdate;
     // 客户ID
    private String groupid;
     // 客户名称
    private String groupname;
     // 需求描述
    private String description;
     // 关联的产品或服务
    private String relatedservice;
     // 期望完成日期
    private Date expecteddate;
     // 审核员
    private String assessor;
     // 审核员id
    private String assessorid;
     // 审核日期
    private Date assessdate;
     // 行号
    private Integer rownum;
     // 备注
    private String remark;
     // 创建者
    private String createby;
     // 创建者id
    private String createbyid;
     // 新建日期
    private Date createdate;
     // 制表
    private String lister;
     // 制表id
    private String listerid;
     // 修改日期
    private Date modifydate;
     // 租户id
    private String tenantid;
     // 租户名称
    private String tenantname;
     // 乐观锁
    private Integer revision;


}

