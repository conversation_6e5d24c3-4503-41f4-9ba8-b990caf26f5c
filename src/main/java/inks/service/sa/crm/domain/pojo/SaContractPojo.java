package inks.service.sa.crm.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;
import java.util.List;
import lombok.Data;

/**
 * 合同管理(SaContract)实体类
 *
 * <AUTHOR>
 * @since 2025-01-23 11:04:31
 */
@Data
public class SaContractPojo implements Serializable {
    private static final long serialVersionUID = 600951063707006246L;
    // ID
    @Excel(name = "ID")
    private String id;
    // 分组
    @Excel(name = "分组")
    private String gengroupid;
    // 合同编号
    @Excel(name = "合同编号")
    private String refno;
    // 单据类型
    @Excel(name = "单据类型")
    private String billtype;
    // 标题
    @Excel(name = "标题")
    private String billtitle;
    // 日期
    @Excel(name = "日期")
    private Date billdate;
    // 含税金额
    @Excel(name = "含税金额")
    private Double billtaxamount;
    // 客户id
    @Excel(name = "客户id")
    private String groupid;
    // 商机id
    @Excel(name = "商机id")
    private String businessid;
    // 客户签约人
    @Excel(name = "客户签约人")
    private String custcontractor;
    // 公司签约人
    @Excel(name = "公司签约人")
    private String compcontractor;
    // 合同金额
    @Excel(name = "合同金额")
    private Double amount;
    // 合同签订时间
    @Excel(name = "合同签订时间")
    private Date contractsigndate;
    // 是否为续约合同
    @Excel(name = "是否为续约合同")
    private Integer renewalmark;
    // 续约结果0无结果预警 123有结果不预警
    @Excel(name = "续约结果0无结果预警 123有结果不预警")
    private Integer renewaltype;
    // 合同过期时间
    @Excel(name = "合同过期时间")
    private Date contractexpiredate;
    // 引用单类型 商机or报价单
    @Excel(name = "引用单类型 商机or报价单")
    private String citetype;
    // 引用单refno
    @Excel(name = "引用单refno")
    private String citeuid;
    // 引用单主表id
    @Excel(name = "引用单主表id")
    private String citeid;
    // 联系人
    @Excel(name = "联系人")
    private String linkman;
    // 客户地址
    @Excel(name = "客户地址")
    private String custadd;
    // 下单时间
    @Excel(name = "下单时间")
    private Date ordertime;
    // 交货日期
    @Excel(name = "交货日期")
    private Date deliverydate;
    // 负责人
    @Excel(name = "负责人")
    private String charge;
    // 负责人部门
    @Excel(name = "负责人部门")
    private String chargedept;
    // 经办人id
    @Excel(name = "经办人id")
    private String operatorid;
    // 经办人
    @Excel(name = "经办人")
    private String operator;
    // 商品状态
    @Excel(name = "商品状态")
    private Integer enabledmark;
    // 已付款
    @Excel(name = "已付款")
    private Double billpaid;
    // 行号
    @Excel(name = "行号")
    private Integer rownum;
    // 摘要
    @Excel(name = "摘要")
    private String summary;
    // 创建者
    @Excel(name = "创建者")
    private String createby;
    // 创建者id
    @Excel(name = "创建者id")
    private String createbyid;
    // 新建日期
    @Excel(name = "新建日期")
    private Date createdate;
    // 制表
    @Excel(name = "制表")
    private String lister;
    // 制表id
    @Excel(name = "制表id")
    private String listerid;
    // 修改日期
    @Excel(name = "修改日期")
    private Date modifydate;
    // 审核员
    @Excel(name = "审核员")
    private String assessor;
    // 审核员id
    @Excel(name = "审核员id")
    private String assessorid;
    // 审核日期
    @Excel(name = "审核日期")
    private Date assessdate;
    // 负责人id
    @Excel(name = "负责人id")
    private String principalid;
    // 负责人
    @Excel(name = "负责人")
    private String principal;
    // 最后跟进时间
    @Excel(name = "最后跟进时间")
    private Date lastfollowdate;
    // 自定义1
    @Excel(name = "自定义1")
    private String custom1;
    // 自定义2
    @Excel(name = "自定义2")
    private String custom2;
    // 自定义3
    @Excel(name = "自定义3")
    private String custom3;
    // 自定义4
    @Excel(name = "自定义4")
    private String custom4;
    // 自定义5
    @Excel(name = "自定义5")
    private String custom5;
    // 自定义6
    @Excel(name = "自定义6")
    private String custom6;
    // 自定义7
    @Excel(name = "自定义7")
    private String custom7;
    // 自定义8
    @Excel(name = "自定义8")
    private String custom8;
    // 自定义9
    @Excel(name = "自定义9")
    private String custom9;
    // 自定义10
    @Excel(name = "自定义10")
    private String custom10;
    // 部门id
    @Excel(name = "部门id")
    private String deptid;
    // 部门
    @Excel(name = "部门")
    private String deptname;
    // 租户id
    @Excel(name = "租户id")
    private String tenantid;
    // 租户名称
    @Excel(name = "租户名称")
    private String tenantname;
    // 乐观锁
    @Excel(name = "乐观锁")
    private Integer revision;
    // 子表
    private List<SaContractitemPojo> item;

    //CustName Groupid关联的客户名称
    private String custname;
}

