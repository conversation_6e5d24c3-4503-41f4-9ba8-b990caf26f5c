package inks.service.sa.crm.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * 跟进标签(SaFollowlabel)实体类
 *
 * <AUTHOR>
 * @since 2025-04-24 09:39:51
 */
@Data
public class SaFollowlabelPojo implements Serializable {
    private static final long serialVersionUID = -99327875974839509L;
     // id
    @Excel(name = "id") 
    private String id;
     // 标签名称
    @Excel(name = "标签名称") 
    private String labelname;
     // 标签颜色
    @Excel(name = "标签颜色") 
    private String labelcolor;
     // 权重
    @Excel(name = "权重") 
    private Integer weight;
     // 行号
    @Excel(name = "行号") 
    private Integer rownum;
     // 备注
    @Excel(name = "备注") 
    private String remark;
     // 创建者
    @Excel(name = "创建者") 
    private String createby;
     // 创建者id
    @Excel(name = "创建者id") 
    private String createbyid;
     // 新建日期
    @Excel(name = "新建日期") 
    private Date createdate;
     // 制表
    @Excel(name = "制表") 
    private String lister;
     // 制表id
    @Excel(name = "制表id") 
    private String listerid;
     // 修改日期
    @Excel(name = "修改日期") 
    private Date modifydate;
     // 自定义1
    @Excel(name = "自定义1") 
    private String custom1;
     // 自定义2
    @Excel(name = "自定义2") 
    private String custom2;
     // 自定义3
    @Excel(name = "自定义3") 
    private String custom3;
     // 自定义4
    @Excel(name = "自定义4") 
    private String custom4;
     // 自定义5
    @Excel(name = "自定义5") 
    private String custom5;
     // 部门id
    @Excel(name = "部门id") 
    private String deptid;
     // 租户id
    @Excel(name = "租户id") 
    private String tenantid;
     // 租户名称
    @Excel(name = "租户名称") 
    private String tenantname;
     // 乐观锁
    @Excel(name = "乐观锁") 
    private Integer revision;



}

