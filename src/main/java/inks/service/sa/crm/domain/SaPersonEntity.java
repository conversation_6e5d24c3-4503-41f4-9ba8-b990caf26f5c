package inks.service.sa.crm.domain;

import java.io.Serializable;
import java.util.Date;

/**
 * 联系人(SaPerson)实体类
 *
 * <AUTHOR>
 * @since 2023-03-06 12:57:00
 */
public class SaPersonEntity implements Serializable {
    private static final long serialVersionUID = 145638911860842583L;
    // id
    private String id;
    // 客户id
    private String groupid;
    // 联系人
    private String personname;
    // 电话
    private String telephone;
    // 手机
    private String mobile;
    // 职位
    private String position;
    // 微信号
    private String wechat;
    // 邮箱
    private String email;
    // 是否是关键决策人
    private Integer decisionmark;
    // 经办人id
    private String operatorid;
    // 经办人
    private String operator;
    // 部门
    private String departname;
    // 有效性
    private Integer enabledmark;
    // 状态号
    private Integer statenum;
    // 新建日期
    private Date statedate;
    // 行号
    private Integer rownum;
    // 备注
    private String remark;
    // 创建者
    private String createby;
    // 创建者id
    private String createbyid;
    // 新建日期
    private Date createdate;
    // 制表
    private String lister;
    // 制表id
    private String listerid;
    // 修改日期
    private Date modifydate;
    // 自定义1
    private String custom1;
    // 自定义2
    private String custom2;
    // 自定义3
    private String custom3;
    // 自定义4
    private String custom4;
    // 自定义5
    private String custom5;
    // 自定义6
    private String custom6;
    // 自定义7
    private String custom7;
    // 自定义8
    private String custom8;
    // 部门id
    private String deptid;
    // 租户id
    private String tenantid;
    // 租户名称
    private String tenantname;
    // 乐观锁
    private Integer revision;

    // id
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    // 客户id
    public String getGroupid() {
        return groupid;
    }

    public void setGroupid(String groupid) {
        this.groupid = groupid;
    }

    // 联系人
    public String getPersonname() {
        return personname;
    }

    public void setPersonname(String personname) {
        this.personname = personname;
    }

    // 电话
    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    // 手机
    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    // 职位
    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    // 微信号
    public String getWechat() {
        return wechat;
    }

    public void setWechat(String wechat) {
        this.wechat = wechat;
    }

    // 邮箱
    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    // 是否是关键决策人
    public Integer getDecisionmark() {
        return decisionmark;
    }

    public void setDecisionmark(Integer decisionmark) {
        this.decisionmark = decisionmark;
    }

    // 经办人id
    public String getOperatorid() {
        return operatorid;
    }

    public void setOperatorid(String operatorid) {
        this.operatorid = operatorid;
    }

    // 经办人
    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    // 部门
    public String getDepartname() {
        return departname;
    }

    public void setDepartname(String departname) {
        this.departname = departname;
    }

    // 有效性
    public Integer getEnabledmark() {
        return enabledmark;
    }

    public void setEnabledmark(Integer enabledmark) {
        this.enabledmark = enabledmark;
    }

    // 状态号
    public Integer getStatenum() {
        return statenum;
    }

    public void setStatenum(Integer statenum) {
        this.statenum = statenum;
    }

    // 新建日期
    public Date getStatedate() {
        return statedate;
    }

    public void setStatedate(Date statedate) {
        this.statedate = statedate;
    }

    // 行号
    public Integer getRownum() {
        return rownum;
    }

    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }

    // 备注
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    // 创建者
    public String getCreateby() {
        return createby;
    }

    public void setCreateby(String createby) {
        this.createby = createby;
    }

    // 创建者id
    public String getCreatebyid() {
        return createbyid;
    }

    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }

    // 新建日期
    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }

    // 制表
    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }

    // 制表id
    public String getListerid() {
        return listerid;
    }

    public void setListerid(String listerid) {
        this.listerid = listerid;
    }

    // 修改日期
    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }

    // 自定义1
    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }

    // 自定义2
    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }

    // 自定义3
    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }

    // 自定义4
    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }

    // 自定义5
    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }

    // 自定义6
    public String getCustom6() {
        return custom6;
    }

    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }

    // 自定义7
    public String getCustom7() {
        return custom7;
    }

    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }

    // 自定义8
    public String getCustom8() {
        return custom8;
    }

    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }

    // 部门id
    public String getDeptid() {
        return deptid;
    }

    public void setDeptid(String deptid) {
        this.deptid = deptid;
    }

    // 租户id
    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }

    // 租户名称
    public String getTenantname() {
        return tenantname;
    }

    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }

    // 乐观锁
    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }


}

