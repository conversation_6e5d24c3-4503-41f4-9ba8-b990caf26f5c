package inks.service.sa.crm.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 报价单(SaQuotation)实体类
 *
 * <AUTHOR>
 * @since 2023-10-31 16:33:42
 */
public class SaQuotationPojo implements Serializable {
    private static final long serialVersionUID = 923313299345352799L;
    // id
    @Excel(name = "id")
    private String id;
    // 报价单号
    @Excel(name = "报价单号")
    private String refno;
    // 单据类型
    @Excel(name = "单据类型")
    private String billtype;
    // 报价标题
    @Excel(name = "报价标题")
    private String billtitle;
    // 报价日期
    @Excel(name = "报价日期")
    private Date billdate;
    // 机会率
    @Excel(name = "机会率")
    private String probability;
    // 客户ID
    @Excel(name = "客户ID")
    private String groupid;
     // 商机id
     @Excel(name = "商机id")
    private String businessid;
    // 客户
    @Excel(name = "客户")
    private String customer;
    // 地址
    @Excel(name = "地址")
    private String custaddress;
    // 联系人
    @Excel(name = "联系人")
    private String custlinkman;
    // 电话
    @Excel(name = "电话")
    private String custtel;
    // 传真
    @Excel(name = "传真")
    private String custfax;
    // 是否含税
    @Excel(name = "是否含税")
    private Integer taxmark;
    // 项目周期
    @Excel(name = "项目周期")
    private String periods;
    // 报价有效期
    @Excel(name = "报价有效期")
    private String validitydate;
    // 货币
    @Excel(name = "货币")
    private String currency;
    // 交货方式
    @Excel(name = "交货方式")
    private String delivery;
    // 结款方式
    @Excel(name = "结款方式")
    private String payment;
    // 工作阶段
    @Excel(name = "工作阶段")
    private String workstage;
    // 业务员id
    @Excel(name = "业务员id")
    private String operatorid;
    // 业务员
    @Excel(name = "业务员")
    private String operator;
    // 附加条款
    @Excel(name = "附加条款")
    private String billclause;
    // 含税金额
    @Excel(name = "含税金额")
    private Double billtaxamount;
    // 未税金额
    @Excel(name = "未税金额")
    private Double billamount;
    // 税额
    @Excel(name = "税额")
    private Double billtaxtotal;
    // 摘要
    @Excel(name = "摘要")
    private String summary;
    // 创建者
    @Excel(name = "创建者")
    private String createby;
    // 创建者id
    @Excel(name = "创建者id")
    private String createbyid;
    // 新建日期
    @Excel(name = "新建日期")
    private Date createdate;
    // 制表
    @Excel(name = "制表")
    private String lister;
    // 制表id
    @Excel(name = "制表id")
    private String listerid;
    // 修改日期
    @Excel(name = "修改日期")
    private Date modifydate;
    // 审核员
    @Excel(name = "审核员")
    private String assessor;
    // 审核员id
    @Excel(name = "审核员id")
    private String assessorid;
    // 审核日期
    @Excel(name = "审核日期")
    private Date assessdate;
     // 正在进行OA
     @Excel(name = "正在进行OA")
    private Integer oaflowmark;
    // 状态
    @Excel(name = "状态")
    private String statecode;
    // 状态日期
    @Excel(name = "状态日期")
    private Date statedate;
    // 负责人id
    @Excel(name = "负责人id")
    private String principalid;
    // 负责人
    @Excel(name = "负责人")
    private String principal;
    // 最后跟进时间
    @Excel(name = "最后跟进时间")
    private Date lastfollowdate;
    // 自定义1
    @Excel(name = "自定义1")
    private String custom1;
    // 自定义2
    @Excel(name = "自定义2")
    private String custom2;
    // 自定义3
    @Excel(name = "自定义3")
    private String custom3;
    // 自定义4
    @Excel(name = "自定义4")
    private String custom4;
    // 自定义5
    @Excel(name = "自定义5")
    private String custom5;
    // 自定义6
    @Excel(name = "自定义6")
    private String custom6;
    // 自定义7
    @Excel(name = "自定义7")
    private String custom7;
    // 自定义8
    @Excel(name = "自定义8")
    private String custom8;
    // 自定义9
    @Excel(name = "自定义9")
    private String custom9;
    // 自定义10
    @Excel(name = "自定义10")
    private String custom10;
    // 部门id
    @Excel(name = "部门id")
    private String deptid;
    // 租户id
    @Excel(name = "租户id")
    private String tenantid;
    // 租户名称
    @Excel(name = "租户名称")
    private String tenantname;
    // 乐观锁
    @Excel(name = "乐观锁")
    private Integer revision;
    // 子表
    private List<SaQuotationitemPojo> item;

    //Sa_Business.BillTitle as BusinessTitle
    private String businesstitle;

    // id

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Integer getOaflowmark() {
        return oaflowmark;
    }

    public void setOaflowmark(Integer oaflowmark) {
        this.oaflowmark = oaflowmark;
    }

    public String getBusinesstitle() {
        return businesstitle;
    }


    public void setBusinesstitle(String businesstitle) {
        this.businesstitle = businesstitle;
    }
    // 报价单号

    public String getRefno() {
        return refno;
    }

    public void setRefno(String refno) {
        this.refno = refno;
    }
    // 单据类型

    public String getBilltype() {
        return billtype;
    }

    public void setBilltype(String billtype) {
        this.billtype = billtype;
    }
    // 报价标题

    public String getBilltitle() {
        return billtitle;
    }

    public void setBilltitle(String billtitle) {
        this.billtitle = billtitle;
    }
    // 报价日期

    public Date getBilldate() {
        return billdate;
    }

    public void setBilldate(Date billdate) {
        this.billdate = billdate;
    }
    // 机会率

    public String getProbability() {
        return probability;
    }

    public void setProbability(String probability) {
        this.probability = probability;
    }
    // 客户ID

    public String getGroupid() {
        return groupid;
    }

    public void setGroupid(String groupid) {
        this.groupid = groupid;
    }

  // 商机id
    public String getBusinessid() {
        return businessid;
    }

    public void setBusinessid(String businessid) {
        this.businessid = businessid;
    }

    // 客户

    public String getCustomer() {
        return customer;
    }

    public void setCustomer(String customer) {
        this.customer = customer;
    }
    // 地址

    public String getCustaddress() {
        return custaddress;
    }

    public void setCustaddress(String custaddress) {
        this.custaddress = custaddress;
    }
    // 联系人

    public String getCustlinkman() {
        return custlinkman;
    }

    public void setCustlinkman(String custlinkman) {
        this.custlinkman = custlinkman;
    }
    // 电话

    public String getCusttel() {
        return custtel;
    }

    public void setCusttel(String custtel) {
        this.custtel = custtel;
    }
    // 传真

    public String getCustfax() {
        return custfax;
    }

    public void setCustfax(String custfax) {
        this.custfax = custfax;
    }
    // 是否含税

    public Integer getTaxmark() {
        return taxmark;
    }

    public void setTaxmark(Integer taxmark) {
        this.taxmark = taxmark;
    }
    // 项目周期

    public String getPeriods() {
        return periods;
    }

    public void setPeriods(String periods) {
        this.periods = periods;
    }
    // 报价有效期

    public String getValiditydate() {
        return validitydate;
    }

    public void setValiditydate(String validitydate) {
        this.validitydate = validitydate;
    }
    // 货币

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }
    // 交货方式

    public String getDelivery() {
        return delivery;
    }

    public void setDelivery(String delivery) {
        this.delivery = delivery;
    }
    // 结款方式

    public String getPayment() {
        return payment;
    }

    public void setPayment(String payment) {
        this.payment = payment;
    }
    // 工作阶段

    public String getWorkstage() {
        return workstage;
    }

    public void setWorkstage(String workstage) {
        this.workstage = workstage;
    }
    // 业务员id

    public String getOperatorid() {
        return operatorid;
    }

    public void setOperatorid(String operatorid) {
        this.operatorid = operatorid;
    }
    // 业务员

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }
    // 附加条款

    public String getBillclause() {
        return billclause;
    }

    public void setBillclause(String billclause) {
        this.billclause = billclause;
    }
    // 含税金额

    public Double getBilltaxamount() {
        return billtaxamount;
    }

    public void setBilltaxamount(Double billtaxamount) {
        this.billtaxamount = billtaxamount;
    }
    // 未税金额

    public Double getBillamount() {
        return billamount;
    }

    public void setBillamount(Double billamount) {
        this.billamount = billamount;
    }
    // 税额

    public Double getBilltaxtotal() {
        return billtaxtotal;
    }

    public void setBilltaxtotal(Double billtaxtotal) {
        this.billtaxtotal = billtaxtotal;
    }
    // 摘要

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }
    // 创建者

    public String getCreateby() {
        return createby;
    }

    public void setCreateby(String createby) {
        this.createby = createby;
    }
    // 创建者id

    public String getCreatebyid() {
        return createbyid;
    }

    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
    // 新建日期

    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
    // 制表

    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }
    // 制表id

    public String getListerid() {
        return listerid;
    }

    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
    // 修改日期

    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
    // 审核员

    public String getAssessor() {
        return assessor;
    }

    public void setAssessor(String assessor) {
        this.assessor = assessor;
    }
    // 审核员id

    public String getAssessorid() {
        return assessorid;
    }

    public void setAssessorid(String assessorid) {
        this.assessorid = assessorid;
    }
    // 审核日期

    public Date getAssessdate() {
        return assessdate;
    }

    public void setAssessdate(Date assessdate) {
        this.assessdate = assessdate;
    }
    // 状态

    public String getStatecode() {
        return statecode;
    }

    public void setStatecode(String statecode) {
        this.statecode = statecode;
    }
    // 状态日期

    public Date getStatedate() {
        return statedate;
    }

    public void setStatedate(Date statedate) {
        this.statedate = statedate;
    }
    // 负责人id

    public String getPrincipalid() {
        return principalid;
    }

    public void setPrincipalid(String principalid) {
        this.principalid = principalid;
    }
    // 负责人

    public String getPrincipal() {
        return principal;
    }

    public void setPrincipal(String principal) {
        this.principal = principal;
    }
    // 最后跟进时间

    public Date getLastfollowdate() {
        return lastfollowdate;
    }

    public void setLastfollowdate(Date lastfollowdate) {
        this.lastfollowdate = lastfollowdate;
    }
    // 自定义1

    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
    // 自定义2

    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
    // 自定义3

    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
    // 自定义4

    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
    // 自定义5

    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
    // 自定义6

    public String getCustom6() {
        return custom6;
    }

    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }
    // 自定义7

    public String getCustom7() {
        return custom7;
    }

    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }
    // 自定义8

    public String getCustom8() {
        return custom8;
    }

    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }
    // 自定义9

    public String getCustom9() {
        return custom9;
    }

    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }
    // 自定义10

    public String getCustom10() {
        return custom10;
    }

    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }
    // 部门id

    public String getDeptid() {
        return deptid;
    }

    public void setDeptid(String deptid) {
        this.deptid = deptid;
    }
    // 租户id

    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
    // 租户名称

    public String getTenantname() {
        return tenantname;
    }

    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }
    // 乐观锁

    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }

    public List<SaQuotationitemPojo> getItem() {
        return item;
    }

    public void setItem(List<SaQuotationitemPojo> item) {
        this.item = item;
    }


}

