package inks.service.sa.crm.domain;

import java.util.Date;
import java.io.Serializable;

/**
 * 线索管理(SaLeads)实体类
 *
 * <AUTHOR>
 * @since 2024-06-20 17:22:55
 */
public class SaLeadsEntity implements Serializable {
    private static final long serialVersionUID = 511950803514945066L;
     // id
    private String id;
     // 用户id
    private String userid;
     // 部门id
    private String deptid;
     // 线索泳道状态
    private String leadsstate;
     // 分组Guid
    private String gengroupid;
     // 客户ID
    private String groupid;
     // 客户名称
    private String custname;
     // 客户类型
    private String custtype;
     // 客户等级
    private String custclass;
     // 电话
    private String telephone;
     // 联系人
    private String linkman;
     // 联系人职务
    private String position;
     // 手机
    private String mobile;
     // Email
    private String email;
     // 行业
    private String custindustry;
     // 来源
    private String custsource;
     // 地址
    private String custadd;
     // 经度
    private Double longitude;
     // 纬度
    private Double latitude;
     // 线索详情
    private String cluedetail;
     // 下次联系
    private Date nextdate;
     // 经办人id
    private String operatorid;
     // 经办人
    private String operator;
     // 部门
    private String departname;
     // 跟踪进度
    private String workstage;
     // 是否转为客户
    private Integer custmark;
     // 领取时间
    private Date receivetime;
     // 有效性
    private Integer enabledmark;
     // 现场照片
    private String photo1;
     // 现场照片
    private String photo2;
     // 现场照片
    private String photo3;
     // 状态号
    private Integer statenum;
     // 新建日期
    private Date statedate;
     // 是否关闭
    private Integer closed;
     // 关闭人id
    private String closedid;
     // 关闭人
    private String closedname;
     // 关闭时间
    private Date closeddate;
     // 行号
    private Integer rownum;
     // 摘要
    private String remark;
     // 创建者
    private String createby;
     // 创建者id
    private String createbyid;
     // 新建日期
    private Date createdate;
     // 制表
    private String lister;
     // 制表id
    private String listerid;
     // 修改日期
    private Date modifydate;
     // 负责人id
    private String principalid;
     // 负责人
    private String principal;
     // 最后跟进时间
    private Date lastfollowdate;
     // 自定义1
    private String custom1;
     // 自定义2
    private String custom2;
     // 自定义3
    private String custom3;
     // 自定义4
    private String custom4;
     // 自定义5
    private String custom5;
     // 自定义6
    private String custom6;
     // 自定义7
    private String custom7;
     // 自定义8
    private String custom8;
     // 自定义9
    private String custom9;
     // 自定义10
    private String custom10;
     // 租户id
    private String tenantid;
     // 租户名称
    private String tenantname;
     // 乐观锁
    private Integer revision;

// id
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
// 用户id
    public String getUserid() {
        return userid;
    }
    
    public void setUserid(String userid) {
        this.userid = userid;
    }
        
// 部门id
    public String getDeptid() {
        return deptid;
    }
    
    public void setDeptid(String deptid) {
        this.deptid = deptid;
    }
        
// 线索泳道状态
    public String getLeadsstate() {
        return leadsstate;
    }
    
    public void setLeadsstate(String leadsstate) {
        this.leadsstate = leadsstate;
    }
        
// 分组Guid
    public String getGengroupid() {
        return gengroupid;
    }
    
    public void setGengroupid(String gengroupid) {
        this.gengroupid = gengroupid;
    }
        
// 客户ID
    public String getGroupid() {
        return groupid;
    }
    
    public void setGroupid(String groupid) {
        this.groupid = groupid;
    }
        
// 客户名称
    public String getCustname() {
        return custname;
    }
    
    public void setCustname(String custname) {
        this.custname = custname;
    }
        
// 客户类型
    public String getCusttype() {
        return custtype;
    }
    
    public void setCusttype(String custtype) {
        this.custtype = custtype;
    }
        
// 客户等级
    public String getCustclass() {
        return custclass;
    }
    
    public void setCustclass(String custclass) {
        this.custclass = custclass;
    }
        
// 电话
    public String getTelephone() {
        return telephone;
    }
    
    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }
        
// 联系人
    public String getLinkman() {
        return linkman;
    }
    
    public void setLinkman(String linkman) {
        this.linkman = linkman;
    }
        
// 联系人职务
    public String getPosition() {
        return position;
    }
    
    public void setPosition(String position) {
        this.position = position;
    }
        
// 手机
    public String getMobile() {
        return mobile;
    }
    
    public void setMobile(String mobile) {
        this.mobile = mobile;
    }
        
// Email
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }
        
// 行业
    public String getCustindustry() {
        return custindustry;
    }
    
    public void setCustindustry(String custindustry) {
        this.custindustry = custindustry;
    }
        
// 来源
    public String getCustsource() {
        return custsource;
    }
    
    public void setCustsource(String custsource) {
        this.custsource = custsource;
    }
        
// 地址
    public String getCustadd() {
        return custadd;
    }
    
    public void setCustadd(String custadd) {
        this.custadd = custadd;
    }
        
// 经度
    public Double getLongitude() {
        return longitude;
    }
    
    public void setLongitude(Double longitude) {
        this.longitude = longitude;
    }
        
// 纬度
    public Double getLatitude() {
        return latitude;
    }
    
    public void setLatitude(Double latitude) {
        this.latitude = latitude;
    }
        
// 线索详情
    public String getCluedetail() {
        return cluedetail;
    }
    
    public void setCluedetail(String cluedetail) {
        this.cluedetail = cluedetail;
    }
        
// 下次联系
    public Date getNextdate() {
        return nextdate;
    }
    
    public void setNextdate(Date nextdate) {
        this.nextdate = nextdate;
    }
        
// 经办人id
    public String getOperatorid() {
        return operatorid;
    }
    
    public void setOperatorid(String operatorid) {
        this.operatorid = operatorid;
    }
        
// 经办人
    public String getOperator() {
        return operator;
    }
    
    public void setOperator(String operator) {
        this.operator = operator;
    }
        
// 部门
    public String getDepartname() {
        return departname;
    }
    
    public void setDepartname(String departname) {
        this.departname = departname;
    }
        
// 跟踪进度
    public String getWorkstage() {
        return workstage;
    }
    
    public void setWorkstage(String workstage) {
        this.workstage = workstage;
    }
        
// 是否转为客户
    public Integer getCustmark() {
        return custmark;
    }
    
    public void setCustmark(Integer custmark) {
        this.custmark = custmark;
    }
        
// 领取时间
    public Date getReceivetime() {
        return receivetime;
    }
    
    public void setReceivetime(Date receivetime) {
        this.receivetime = receivetime;
    }
        
// 有效性
    public Integer getEnabledmark() {
        return enabledmark;
    }
    
    public void setEnabledmark(Integer enabledmark) {
        this.enabledmark = enabledmark;
    }
        
// 现场照片
    public String getPhoto1() {
        return photo1;
    }
    
    public void setPhoto1(String photo1) {
        this.photo1 = photo1;
    }
        
// 现场照片
    public String getPhoto2() {
        return photo2;
    }
    
    public void setPhoto2(String photo2) {
        this.photo2 = photo2;
    }
        
// 现场照片
    public String getPhoto3() {
        return photo3;
    }
    
    public void setPhoto3(String photo3) {
        this.photo3 = photo3;
    }
        
// 状态号
    public Integer getStatenum() {
        return statenum;
    }
    
    public void setStatenum(Integer statenum) {
        this.statenum = statenum;
    }
        
// 新建日期
    public Date getStatedate() {
        return statedate;
    }
    
    public void setStatedate(Date statedate) {
        this.statedate = statedate;
    }
        
// 是否关闭
    public Integer getClosed() {
        return closed;
    }
    
    public void setClosed(Integer closed) {
        this.closed = closed;
    }
        
// 关闭人id
    public String getClosedid() {
        return closedid;
    }
    
    public void setClosedid(String closedid) {
        this.closedid = closedid;
    }
        
// 关闭人
    public String getClosedname() {
        return closedname;
    }
    
    public void setClosedname(String closedname) {
        this.closedname = closedname;
    }
        
// 关闭时间
    public Date getCloseddate() {
        return closeddate;
    }
    
    public void setCloseddate(Date closeddate) {
        this.closeddate = closeddate;
    }
        
// 行号
    public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
// 摘要
    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
// 创建者
    public String getCreateby() {
        return createby;
    }
    
    public void setCreateby(String createby) {
        this.createby = createby;
    }
        
// 创建者id
    public String getCreatebyid() {
        return createbyid;
    }
    
    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
        
// 新建日期
    public Date getCreatedate() {
        return createdate;
    }
    
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
        
// 制表
    public String getLister() {
        return lister;
    }
    
    public void setLister(String lister) {
        this.lister = lister;
    }
        
// 制表id
    public String getListerid() {
        return listerid;
    }
    
    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
        
// 修改日期
    public Date getModifydate() {
        return modifydate;
    }
    
    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
        
// 负责人id
    public String getPrincipalid() {
        return principalid;
    }
    
    public void setPrincipalid(String principalid) {
        this.principalid = principalid;
    }
        
// 负责人
    public String getPrincipal() {
        return principal;
    }
    
    public void setPrincipal(String principal) {
        this.principal = principal;
    }
        
// 最后跟进时间
    public Date getLastfollowdate() {
        return lastfollowdate;
    }
    
    public void setLastfollowdate(Date lastfollowdate) {
        this.lastfollowdate = lastfollowdate;
    }
        
// 自定义1
    public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
// 自定义2
    public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
// 自定义3
    public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
// 自定义4
    public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
// 自定义5
    public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
// 自定义6
    public String getCustom6() {
        return custom6;
    }
    
    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }
        
// 自定义7
    public String getCustom7() {
        return custom7;
    }
    
    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }
        
// 自定义8
    public String getCustom8() {
        return custom8;
    }
    
    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }
        
// 自定义9
    public String getCustom9() {
        return custom9;
    }
    
    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }
        
// 自定义10
    public String getCustom10() {
        return custom10;
    }
    
    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }
        
// 租户id
    public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
// 租户名称
    public String getTenantname() {
        return tenantname;
    }
    
    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }
        
// 乐观锁
    public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

