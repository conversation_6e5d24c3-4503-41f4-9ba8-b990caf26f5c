package inks.service.sa.crm.domain;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * 跟进记录(SaFollowview)实体类
 *
 * <AUTHOR>
 * @since 2025-05-05 16:08:34
 */
@Data
public class SaFollowviewEntity implements Serializable {
    private static final long serialVersionUID = 965653114260719227L;
     // id
    private String id;
     // 关联id
    private String citeid;
     // 关联id类型:Leads/Customer/Business/Quotation/Contract
    private String citetype;
     // 类型
    private String itemtype;
     // 内容
    private String itemcontent;
     // 0跟进记录/1系统记录/2跟进计划
    private Integer isauto;
     // 客户名
    private String custname;
     // 销售员
    private String salesman;
     // 计划开始
    private Date nextdate;
     // 计划完成
    private Date enddate;
     // 完成时间
    private Date finishdate;
     // 完成人
    private String finisher;
     // 完成描述
    private String finishdesc;
     // 是否完成
    private Integer finishmark;
     // 图片1
    private String photourl1;
     // 图片2
    private String photourl2;
     // 图片3
    private String photourl3;
     // 图片1
    private String photoname1;
     // 图片2
    private String photoname2;
     // 图片3
    private String photoname3;
     // 经办人id
    private String operatorid;
     // 经办人
    private String operator;
     // 标签(json数组)
    private String labeljson;
     // 协作者ids
    private String collaboratorids;
     // 协作者们
    private String collaborators;
     // 行号
    private Integer rownum;
     // 备注
    private String remark;
     // 制表
    private String lister;
     // 制表id
    private String listerid;
     // 创建者
    private String createby;
     // 创建者id
    private String createbyid;
     // 新建日期
    private Date createdate;
     // 修改日期
    private Date modifydate;
     // 自定义1
    private String custom1;
     // 自定义2
    private String custom2;
     // 自定义3
    private String custom3;
     // 自定义4
    private String custom4;
     // 自定义5
    private String custom5;
     // 自定义6
    private String custom6;
     // 自定义7
    private String custom7;
     // 自定义8
    private String custom8;
     // 自定义9
    private String custom9;
     // 自定义10
    private String custom10;
     // 租户id
    private String tenantid;
     // 租户名称
    private String tenantname;
     // 乐观锁
    private Integer revision;



}

