package inks.service.sa.crm.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;
import java.util.List;
import lombok.Data;

/**
 * 商机(SaBusiness)实体类
 *
 * <AUTHOR>
 * @since 2024-02-20 14:21:29
 */
@Data
public class SaBusinessPojo implements Serializable {
    private static final long serialVersionUID = 497673256722967049L;
    // id
    @Excel(name = "id")
    private String id;
    // 商机单号
    @Excel(name = "商机单号")
    private String refno;
    // 单据类型
    @Excel(name = "单据类型")
    private String billtype;
    // 商机标题
    @Excel(name = "商机标题")
    private String billtitle;
    // 商机日期
    @Excel(name = "商机日期")
    private Date billdate;
     // 含税金额
     @Excel(name = "含税金额")
    private Double billtaxamount;
     // 未税金额
     @Excel(name = "未税金额")
    private Double billamount;
     // 税额
     @Excel(name = "税额")
    private Double billtaxtotal;
     // 合同金额
     @Excel(name = "合同金额")
    private Double contractamount;
    // 客户id
    @Excel(name = "客户id")
    private String groupid;
    // 地址
    @Excel(name = "地址")
    private String address;
    // 联系人
    @Excel(name = "联系人")
    private String linkman;
    // 电话
    @Excel(name = "电话")
    private String tel;
    // 传真
    @Excel(name = "传真")
    private String fax;
    // 报备方
    @Excel(name = "报备方")
    private String reporter;
    // 商机来源
    @Excel(name = "商机来源")
    private String source;
    // 商机类型
    @Excel(name = "商机类型")
    private String businesstype;
    // 销售类型
    @Excel(name = "销售类型")
    private String salestype;
    // 销售进度
    @Excel(name = "销售进度")
    private String salesprogress;
    // 预计成交金额
    @Excel(name = "预计成交金额")
    private String estimatedamt;
    // 预计成交时间
    @Excel(name = "预计成交时间")
    private Date estimatedtime;
    // 市场活动
    @Excel(name = "市场活动")
    private String marketing;
    // 商机情况
    @Excel(name = "商机情况")
    private String status;
    // 工作阶段
    @Excel(name = "工作阶段")
    private String workstage;
     // 阶段id
     @Excel(name = "阶段id")
    private String stageid;
     // 阶段数据
     @Excel(name = "阶段数据")
    private String stagetext;
     // 0进行中/1赢单/2输单/3无效
     @Excel(name = "0进行中/1赢单/2输单/3无效")
    private Integer stageresult;
    // 业务员id
    @Excel(name = "业务员id")
    private String operatorid;
    // 业务员
    @Excel(name = "业务员")
    private String operator;
    // 摘要
    @Excel(name = "摘要")
    private String summary;
    // 创建者
    @Excel(name = "创建者")
    private String createby;
    // 创建者id
    @Excel(name = "创建者id")
    private String createbyid;
    // 新建日期
    @Excel(name = "新建日期")
    private Date createdate;
    // 制表
    @Excel(name = "制表")
    private String lister;
    // 制表id
    @Excel(name = "制表id")
    private String listerid;
    // 修改日期
    @Excel(name = "修改日期")
    private Date modifydate;
    // 审核员
    @Excel(name = "审核员")
    private String assessor;
    // 审核员id
    @Excel(name = "审核员id")
    private String assessorid;
    // 审核日期
    @Excel(name = "审核日期")
    private Date assessdate;
     // 正在进行OA
     @Excel(name = "正在进行OA")
    private Integer oaflowmark;
    // 状态
    @Excel(name = "状态")
    private String statecode;
    // 状态日期
    @Excel(name = "状态日期")
    private Date statedate;
    // 自定义1
    @Excel(name = "自定义1")
    private String custom1;
    // 自定义2
    @Excel(name = "自定义2")
    private String custom2;
    // 自定义3
    @Excel(name = "自定义3")
    private String custom3;
    // 自定义4
    @Excel(name = "自定义4")
    private String custom4;
    // 自定义5
    @Excel(name = "自定义5")
    private String custom5;
    // 自定义6
    @Excel(name = "自定义6")
    private String custom6;
    // 自定义7
    @Excel(name = "自定义7")
    private String custom7;
    // 自定义8
    @Excel(name = "自定义8")
    private String custom8;
    // 自定义9
    @Excel(name = "自定义9")
    private String custom9;
    // 自定义10
    @Excel(name = "自定义10")
    private String custom10;
    // 部门id
    @Excel(name = "部门id")
    private String deptid;
    // 租户id
    @Excel(name = "租户id")
    private String tenantid;
    // 租户名称
    @Excel(name = "租户名称")
    private String tenantname;
    // 乐观锁
    @Excel(name = "乐观锁")
    private Integer revision;
    // 货品子表
    private List<SaBusinessitemPojo> item;
    //阶段子表
    private List<SaBusinessstagePojo> stage;
    // 客户名称

    //客户名称
    private String custname;
    //Sa_BusinessStage.StageName
    private String stagename;


}

