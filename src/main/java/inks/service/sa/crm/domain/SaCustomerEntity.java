package inks.service.sa.crm.domain;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * 客户(SaCustomer)实体类
 *
 * <AUTHOR>
 * @since 2025-06-19 09:00:30
 */
@Data
public class SaCustomerEntity implements Serializable {
    private static final long serialVersionUID = 556206922250733552L;
     // ID
    private String id;
     // 客户编码
    private String custuid;
     // 业务员id
    private String userid;
     // 部门id
    private String deptid;
     // 线索id
    private String leadsid;
     // 分组编码
    private String gengroupid;
     // 客户名称
    private String custname;
     // 客户类型
    private String custtype;
     // 客户等级
    private String custclass;
     // 电话
    private String telephone;
     // 联系人
    private String linkman;
     // 联系人职务
    private String position;
     // 手机
    private String mobile;
     // Email
    private String email;
     // 行业
    private String custindustry;
     // 来源
    private String custsource;
     // 经度
    private Double longitude;
     // 纬度
    private Double latitude;
     // 地址
    private String custadd;
     // 下次联系
    private Date nextdate;
     // 经办人id
    private String operatorid;
     // 经办人
    private String operator;
     // 部门
    private String departname;
     // 有效性
    private Integer enabledmark;
     // 备注
    private String remark;
     // 创建者
    private String createby;
     // 创建者id
    private String createbyid;
     // 新建日期
    private Date createdate;
     // 制表
    private String lister;
     // 制表id
    private String listerid;
     // 修改日期
    private Date modifydate;
     // 负责人id
    private String principalid;
     // 负责人
    private String principal;
     // 最后跟进时间
    private Date lastfollowdate;
     // 扩展编码
    private String extendcode;
     // 地级市
    private String city;
     // 县
    private String county;
     // 街道
    private String street;
     // 门牌号
    private String localadd;
     // 地域
    private String region;
     // 行为类型
    private String actiontype;
     // 自定义1
    private String custom1;
     // 自定义2
    private String custom2;
     // 自定义3
    private String custom3;
     // 自定义4
    private String custom4;
     // 自定义5
    private String custom5;
     // 自定义6
    private String custom6;
     // 自定义7
    private String custom7;
     // 自定义8
    private String custom8;
     // 自定义9
    private String custom9;
     // 自定义10
    private String custom10;
     // 租户id
    private String tenantid;
     // 租户名称
    private String tenantname;
     // 乐观锁
    private Integer revision;



}

