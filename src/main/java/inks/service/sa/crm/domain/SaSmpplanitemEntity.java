package inks.service.sa.crm.domain;

import java.util.Date;
import java.io.Serializable;

/**
 * 计划项目(SaSmpplanitem)Entity
 *
 * <AUTHOR>
 * @since 2024-10-25 16:58:04
 */
public class SaSmpplanitemEntity implements Serializable {
    private static final long serialVersionUID = 283446595035876473L;
     // id
    private String id;
     // Pid
    private String pid;
     // 货品id
    private String goodsid;
     // 产品编码
    private String itemcode;
     // 产品名称
    private String itemname;
     // 产品规格
    private String itemspec;
     // 产品单位
    private String itemunit;
     // 产品数量
    private Double quantity;
     // 加工单价
    private Double price;
     // 加工金额
    private Double amount;
     // 计划开工
    private Date startdate;
     // 计划完工
    private Date plandate;
     // 开单数量
    private Double startqty;
     // 完工数量
    private Double finishqty;
     // 报废数量
    private Double mrbqty;
     // PNL数量
    private Double startsecqty;
     // 有效标识
    private Integer enabledmark;
     // 关闭
    private Integer closed;
     // 备注
    private String remark;
     // 状态
    private String statecode;
     // 状态时间
    private Date statedate;
     // 行号
    private Integer rownum;
     // 客户
    private String customer;
     // 客户PO
    private String custpo;
     // MRP单号
    private String mrpuid;
     // MRP单id
    private String mrpid;
     // 属性Josn
    private String attributejson;
     // SPU文本
    private String attributestr;
     // 开单率
    private Double startrate;
     // 完工率
    private Double finishrate;
     // 当前工序id
    private String wkwpid;
     // 当前工序编码
    private String wkwpcode;
     // 当前工序名称
    private String wkwpname;
     // 自定义1
    private String custom1;
     // 自定义2
    private String custom2;
     // 自定义3
    private String custom3;
     // 自定义4
    private String custom4;
     // 自定义5
    private String custom5;
     // 自定义6
    private String custom6;
     // 自定义7
    private String custom7;
     // 自定义8
    private String custom8;
     // 自定义9
    private String custom9;
     // 自定义10
    private String custom10;
     // 租户id
    private String tenantid;
     // 乐观锁
    private Integer revision;

   // id
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
   // Pid
    public String getPid() {
        return pid;
    }
    
    public void setPid(String pid) {
        this.pid = pid;
    }
        
   // 货品id
    public String getGoodsid() {
        return goodsid;
    }
    
    public void setGoodsid(String goodsid) {
        this.goodsid = goodsid;
    }
        
   // 产品编码
    public String getItemcode() {
        return itemcode;
    }
    
    public void setItemcode(String itemcode) {
        this.itemcode = itemcode;
    }
        
   // 产品名称
    public String getItemname() {
        return itemname;
    }
    
    public void setItemname(String itemname) {
        this.itemname = itemname;
    }
        
   // 产品规格
    public String getItemspec() {
        return itemspec;
    }
    
    public void setItemspec(String itemspec) {
        this.itemspec = itemspec;
    }
        
   // 产品单位
    public String getItemunit() {
        return itemunit;
    }
    
    public void setItemunit(String itemunit) {
        this.itemunit = itemunit;
    }
        
   // 产品数量
    public Double getQuantity() {
        return quantity;
    }
    
    public void setQuantity(Double quantity) {
        this.quantity = quantity;
    }
        
   // 加工单价
    public Double getPrice() {
        return price;
    }
    
    public void setPrice(Double price) {
        this.price = price;
    }
        
   // 加工金额
    public Double getAmount() {
        return amount;
    }
    
    public void setAmount(Double amount) {
        this.amount = amount;
    }
        
   // 计划开工
    public Date getStartdate() {
        return startdate;
    }
    
    public void setStartdate(Date startdate) {
        this.startdate = startdate;
    }
        
   // 计划完工
    public Date getPlandate() {
        return plandate;
    }
    
    public void setPlandate(Date plandate) {
        this.plandate = plandate;
    }
        
   // 开单数量
    public Double getStartqty() {
        return startqty;
    }
    
    public void setStartqty(Double startqty) {
        this.startqty = startqty;
    }
        
   // 完工数量
    public Double getFinishqty() {
        return finishqty;
    }
    
    public void setFinishqty(Double finishqty) {
        this.finishqty = finishqty;
    }
        
   // 报废数量
    public Double getMrbqty() {
        return mrbqty;
    }
    
    public void setMrbqty(Double mrbqty) {
        this.mrbqty = mrbqty;
    }
        
   // PNL数量
    public Double getStartsecqty() {
        return startsecqty;
    }
    
    public void setStartsecqty(Double startsecqty) {
        this.startsecqty = startsecqty;
    }
        
   // 有效标识
    public Integer getEnabledmark() {
        return enabledmark;
    }
    
    public void setEnabledmark(Integer enabledmark) {
        this.enabledmark = enabledmark;
    }
        
   // 关闭
    public Integer getClosed() {
        return closed;
    }
    
    public void setClosed(Integer closed) {
        this.closed = closed;
    }
        
   // 备注
    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
   // 状态
    public String getStatecode() {
        return statecode;
    }
    
    public void setStatecode(String statecode) {
        this.statecode = statecode;
    }
        
   // 状态时间
    public Date getStatedate() {
        return statedate;
    }
    
    public void setStatedate(Date statedate) {
        this.statedate = statedate;
    }
        
   // 行号
    public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
   // 客户
    public String getCustomer() {
        return customer;
    }
    
    public void setCustomer(String customer) {
        this.customer = customer;
    }
        
   // 客户PO
    public String getCustpo() {
        return custpo;
    }
    
    public void setCustpo(String custpo) {
        this.custpo = custpo;
    }
        
   // MRP单号
    public String getMrpuid() {
        return mrpuid;
    }
    
    public void setMrpuid(String mrpuid) {
        this.mrpuid = mrpuid;
    }
        
   // MRP单id
    public String getMrpid() {
        return mrpid;
    }
    
    public void setMrpid(String mrpid) {
        this.mrpid = mrpid;
    }
        
   // 属性Josn
    public String getAttributejson() {
        return attributejson;
    }
    
    public void setAttributejson(String attributejson) {
        this.attributejson = attributejson;
    }
        
   // SPU文本
    public String getAttributestr() {
        return attributestr;
    }
    
    public void setAttributestr(String attributestr) {
        this.attributestr = attributestr;
    }
        
   // 开单率
    public Double getStartrate() {
        return startrate;
    }
    
    public void setStartrate(Double startrate) {
        this.startrate = startrate;
    }
        
   // 完工率
    public Double getFinishrate() {
        return finishrate;
    }
    
    public void setFinishrate(Double finishrate) {
        this.finishrate = finishrate;
    }
        
   // 当前工序id
    public String getWkwpid() {
        return wkwpid;
    }
    
    public void setWkwpid(String wkwpid) {
        this.wkwpid = wkwpid;
    }
        
   // 当前工序编码
    public String getWkwpcode() {
        return wkwpcode;
    }
    
    public void setWkwpcode(String wkwpcode) {
        this.wkwpcode = wkwpcode;
    }
        
   // 当前工序名称
    public String getWkwpname() {
        return wkwpname;
    }
    
    public void setWkwpname(String wkwpname) {
        this.wkwpname = wkwpname;
    }
        
   // 自定义1
    public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
   // 自定义2
    public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
   // 自定义3
    public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
   // 自定义4
    public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
   // 自定义5
    public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
   // 自定义6
    public String getCustom6() {
        return custom6;
    }
    
    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }
        
   // 自定义7
    public String getCustom7() {
        return custom7;
    }
    
    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }
        
   // 自定义8
    public String getCustom8() {
        return custom8;
    }
    
    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }
        
   // 自定义9
    public String getCustom9() {
        return custom9;
    }
    
    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }
        
   // 自定义10
    public String getCustom10() {
        return custom10;
    }
    
    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }
        
   // 租户id
    public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
   // 乐观锁
    public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

