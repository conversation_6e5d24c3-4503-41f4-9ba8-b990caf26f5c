package inks.service.sa.crm.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * 需求字典(SaDemanddict)实体类
 *
 * <AUTHOR>
 * @since 2024-10-11 10:02:21
 */
@Data
public class SaDemanddictPojo implements Serializable {
    private static final long serialVersionUID = -44443955523976222L;
     // ID
    @Excel(name = "ID") 
    private String id;
     // 需求编码
    @Excel(name = "需求编码") 
    private String demandcode;
     // 需求名称
    @Excel(name = "需求名称") 
    private String demandname;
     // 需求类型
    @Excel(name = "需求类型") 
    private String demandtype;
     // 需求描述
    @Excel(name = "需求描述") 
    private String description;
     // 行号
    @Excel(name = "行号") 
    private Integer rownum;
     // 备注
    @Excel(name = "备注") 
    private String remark;
     // 创建者
    @Excel(name = "创建者") 
    private String createby;
     // 创建者id
    @Excel(name = "创建者id") 
    private String createbyid;
     // 新建日期
    @Excel(name = "新建日期") 
    private Date createdate;
     // 制表
    @Excel(name = "制表") 
    private String lister;
     // 制表id
    @Excel(name = "制表id") 
    private String listerid;
     // 修改日期
    @Excel(name = "修改日期") 
    private Date modifydate;
     // 租户id
    @Excel(name = "租户id") 
    private String tenantid;
     // 乐观锁
    @Excel(name = "乐观锁") 
    private Integer revision;



}

