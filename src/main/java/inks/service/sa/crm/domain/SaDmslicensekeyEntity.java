package inks.service.sa.crm.domain;

import java.io.Serializable;
import java.util.Date;

/**
 * DMS用户-授权码关联表(SaDmslicensekey)实体类
 *
 * <AUTHOR>
 * @since 2023-12-09 09:56:37
 */
public class SaDmslicensekeyEntity implements Serializable {
    private static final long serialVersionUID = 623255693158370239L;
    // id
    private String id;
    // Dms用户id
    private String dmsuserid;
    // 授权码Key
    private String licensekey;
    // 软件名
    private String software;
    // 软件期限(单位:年)
    private Integer softwareperiod;
    // 最大在线用户数
    private Integer maxonlineuser;
    // 支持的硬件数
    private Integer maxsupporthard;
    // 是否有限期
    private Integer islimited;
    // 密钥到期日期
    private Long keyexpiration;
    // 本次生成数量
    private Integer keyquantity;
    // 状态编码
    private String statecode;
    // 备注
    private String remark;
    // 创建者
    private String createby;
    // 创建者id
    private String createbyid;
    // 新建日期
    private Date createdate;
    // 制表
    private String lister;
    // 制表id
    private String listerid;
    // 修改日期
    private Date modifydate;
    // 租户id
    private String tenantid;
    // 租户名称
    private String tenantname;
    // 乐观锁
    private Integer revision;

// id

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
// Dms用户id

    public String getDmsuserid() {
        return dmsuserid;
    }

    public void setDmsuserid(String dmsuserid) {
        this.dmsuserid = dmsuserid;
    }
// 授权码Key

    public String getLicensekey() {
        return licensekey;
    }

    public void setLicensekey(String licensekey) {
        this.licensekey = licensekey;
    }
// 软件名

    public String getSoftware() {
        return software;
    }

    public void setSoftware(String software) {
        this.software = software;
    }
// 软件期限(单位:年)

    public Integer getSoftwareperiod() {
        return softwareperiod;
    }

    public void setSoftwareperiod(Integer softwareperiod) {
        this.softwareperiod = softwareperiod;
    }
// 最大在线用户数

    public Integer getMaxonlineuser() {
        return maxonlineuser;
    }

    public void setMaxonlineuser(Integer maxonlineuser) {
        this.maxonlineuser = maxonlineuser;
    }
// 支持的硬件数

    public Integer getMaxsupporthard() {
        return maxsupporthard;
    }

    public void setMaxsupporthard(Integer maxsupporthard) {
        this.maxsupporthard = maxsupporthard;
    }
// 是否有限期

    public Integer getIslimited() {
        return islimited;
    }

    public void setIslimited(Integer islimited) {
        this.islimited = islimited;
    }
// 密钥到期日期

    public Long getKeyexpiration() {
        return keyexpiration;
    }

    public void setKeyexpiration(Long keyexpiration) {
        this.keyexpiration = keyexpiration;
    }
// 本次生成数量

    public Integer getKeyquantity() {
        return keyquantity;
    }

    public void setKeyquantity(Integer keyquantity) {
        this.keyquantity = keyquantity;
    }
// 状态编码

    public String getStatecode() {
        return statecode;
    }

    public void setStatecode(String statecode) {
        this.statecode = statecode;
    }
// 备注

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
// 创建者

    public String getCreateby() {
        return createby;
    }

    public void setCreateby(String createby) {
        this.createby = createby;
    }
// 创建者id

    public String getCreatebyid() {
        return createbyid;
    }

    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
// 新建日期

    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
// 制表

    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }
// 制表id

    public String getListerid() {
        return listerid;
    }

    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
// 修改日期

    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
// 租户id

    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
// 租户名称

    public String getTenantname() {
        return tenantname;
    }

    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }
// 乐观锁

    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }

}

