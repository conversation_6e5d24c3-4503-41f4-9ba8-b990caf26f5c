package inks.service.sa.crm.domain;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * 样品计划(SaSmpplan)实体类
 *
 * <AUTHOR>
 * @since 2025-07-10 16:02:49
 */
@Data
public class SaSmpplanEntity implements Serializable {
    private static final long serialVersionUID = 253198314656989863L;
     // id
    private String id;
     // 编码
    private String refno;
     // 单据类型
    private String billtype;
     // 单据日期
    private Date billdate;
     // 单据标题
    private String billtitle;
     // 客户ID
    private String groupid;
     // 商机id
    private String businessid;
     // 经办人
    private String operator;
     // 经办人id
    private String operatorid;
     // 摘要
    private String summary;
     // 创建者
    private String createby;
     // 创建者id
    private String createbyid;
     // 新建日期
    private Date createdate;
     // 制表
    private String lister;
     // 制表id
    private String listerid;
     // 修改日期
    private Date modifydate;
     // 审核员
    private String assessor;
     // 审核员id
    private String assessorid;
     // 审核日期
    private Date assessdate;
     // 单据状态
    private String billstatecode;
     // 状态日期
    private Date billstatedate;
     // 计划开工
    private Date billstartdate;
     // 单据计划
    private Date billplandate;
     // item行数
    private Integer itemcount;
     // Mrp行数
    private Integer mrpcount;
     // 开单行数
    private Integer startcount;
     // 作废行数
    private Integer disannulcount;
     // 完成行数
    private Integer finishcount;
     // 打印次数
    private Integer printcount;
     // OA审批中
    private Integer oaflowmark;
     // 最新工序id
    private String billwkwpid;
     // 最新工序编码
    private String billwkwpcode;
     // 最新工序名称
    private String billwkwpname;
     // 自定义1
    private String custom1;
     // 自定义2
    private String custom2;
     // 自定义3
    private String custom3;
     // 自定义4
    private String custom4;
     // 自定义5
    private String custom5;
     // 自定义6
    private String custom6;
     // 自定义7
    private String custom7;
     // 自定义8
    private String custom8;
     // 自定义9
    private String custom9;
     // 自定义10
    private String custom10;
     // 部门id
    private String deptid;
     // 租户id
    private String tenantid;
     // 租户名称
    private String tenantname;
     // 乐观锁
    private Integer revision;


}

