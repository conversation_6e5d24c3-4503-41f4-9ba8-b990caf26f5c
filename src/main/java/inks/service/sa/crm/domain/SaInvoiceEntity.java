package inks.service.sa.crm.domain;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * 发票管理(SaInvoice)实体类
 *
 * <AUTHOR>
 * @since 2025-01-23 10:51:59
 */
@Data
public class SaInvoiceEntity implements Serializable {
    private static final long serialVersionUID = -98448328420077030L;
     // id
    private String id;
     // 编码
    private String refno;
     // 单据类型
    private String billtype;
     // 单据标题
    private String billtitle;
     // 单据日期
    private Date billdate;
     // 合同id
    private String orderid;
     // 合同单据号
    private String orderuid;
     // 商机id
    private String businessid;
     // 回款单id
    private String receiptid;
     // 发票类型
    private String invotype;
     // 发票编码
    private String invocode;
     // 开票日期
    private Date invodate;
     // 客户id
    private String groupid;
     // 客户名
    private String custname;
     // 金额
    private Double amount;
     // 联系人
    private String contact;
     // 联系方式
    private String contactway;
     // 寄送地址
    private String address;
     // 经办人id
    private String operatorid;
     // 经办人
    private String operator;
     // 行号
    private Integer rownum;
     // 备注
    private String remark;
     // 创建者
    private String createby;
     // 创建者id
    private String createbyid;
     // 新建日期
    private Date createdate;
     // 制表
    private String lister;
     // 制表id
    private String listerid;
     // 修改日期
    private Date modifydate;
     // 审核员
    private String assessor;
     // 审核员id
    private String assessorid;
     // 审核日期
    private Date assessdate;
     // 自定义1
    private String custom1;
     // 自定义2
    private String custom2;
     // 自定义3
    private String custom3;
     // 自定义4
    private String custom4;
     // 自定义5
    private String custom5;
     // 自定义6
    private String custom6;
     // 自定义7
    private String custom7;
     // 自定义8
    private String custom8;
     // 自定义9
    private String custom9;
     // 自定义10
    private String custom10;
     // 租户id
    private String tenantid;
     // 租户名称
    private String tenantname;
     // 乐观锁
    private Integer revision;



}

