package inks.service.sa.crm.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;
import inks.common.core.domain.DatailPojo;
import lombok.Data;

/**
 * 需求调研子表(SaDemandresearchitem)Pojo
 *
 * <AUTHOR>
 * @since 2024-10-11 10:15:14
 */
@Data
public class SaDemandresearchitemdetailPojo extends DatailPojo implements Serializable {
    private static final long serialVersionUID = 232885936202680846L;
     // ID
  @Excel(name = "ID")    
  private String id;
     // 主表ID
  @Excel(name = "主表ID")    
  private String pid;
     // 需求字典ID
  @Excel(name = "需求字典ID")    
  private String demanddictid;
     // 需求描述
  @Excel(name = "需求描述")    
  private String description;
     // 核心需求标识
  @Excel(name = "核心需求标识")    
  private Integer mainmark;
     // 优先级
  @Excel(name = "优先级")    
  private Integer level;
     // 需求提出时间
  @Excel(name = "需求提出时间")    
  private Date requirementdate;
     // 实现难度
  @Excel(name = "实现难度")    
  private Integer difficulty;
     // 需求解决方案
  @Excel(name = "需求解决方案")    
  private String solution;
     // 行号
  @Excel(name = "行号")    
  private Integer rownum;
     // 备注
  @Excel(name = "备注")    
  private String remark;
     // 租户id
  @Excel(name = "租户id")    
  private String tenantid;
     // 乐观锁
  @Excel(name = "乐观锁")    
  private Integer revision;



}

