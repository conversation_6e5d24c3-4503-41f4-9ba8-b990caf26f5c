package inks.service.sa.crm.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;
import inks.common.core.domain.LoginUser;
import lombok.Data;

/**
 * 跟进记录(SaFollowview)实体类
 *
 * <AUTHOR>
 * @since 2025-04-24 10:24:20
 */
@Data
public class SaFollowviewPojo implements Serializable {
    private static final long serialVersionUID = -62201787044571132L;
    // id
    @Excel(name = "id")
    private String id;
    // 关联id
    @Excel(name = "关联id")
    private String citeid;
    // 关联id类型:Leads/Customer/Business/Quotation/Contract
    @Excel(name = "关联id类型:Leads/Customer/Business/Quotation/Contract")
    private String citetype;
    // 类型
    @Excel(name = "类型")
    private String itemtype;
    // 内容
    @Excel(name = "内容")
    private String itemcontent;
    // 0跟进记录/1系统记录/2跟进计划
    @Excel(name = "0跟进记录/1系统记录/2跟进计划")
    private Integer isauto;
    // 客户名
    @Excel(name = "客户名")
    private String custname;
     // 销售员
    @Excel(name = "销售员")
    private String salesman;
     // 计划开始
    @Excel(name = "计划开始")
    private Date nextdate;
     // 计划完成
    @Excel(name = "计划完成")
    private Date enddate;
     // 完成时间
    @Excel(name = "完成时间")
    private Date finishdate;
     // 完成人
    @Excel(name = "完成人")
    private String finisher;
    // 完成描述
    @Excel(name = "完成描述")
    private String finishdesc;
    // 是否完成
    @Excel(name = "是否完成")
    private Integer finishmark;
    // 图片1
    @Excel(name = "图片1")
    private String photourl1;
    // 图片2
    @Excel(name = "图片2")
    private String photourl2;
    // 图片3
    @Excel(name = "图片3")
    private String photourl3;
    // 图片1
    @Excel(name = "图片1")
    private String photoname1;
    // 图片2
    @Excel(name = "图片2")
    private String photoname2;
    // 图片3
    @Excel(name = "图片3")
    private String photoname3;
    // 经办人id
    @Excel(name = "经办人id")
    private String operatorid;
    // 经办人
    @Excel(name = "经办人")
    private String operator;
    // 标签(json数组)
    @Excel(name = "标签(json数组)")
    private String labeljson;
     // 协作者ids
    @Excel(name = "协作者ids")
    private String collaboratorids;
     // 协作者们
    @Excel(name = "协作者们")
    private String collaborators;
    // 行号
    @Excel(name = "行号")
    private Integer rownum;
    // 备注
    @Excel(name = "备注")
    private String remark;
    // 制表
    @Excel(name = "制表")
    private String lister;
    // 制表id
    @Excel(name = "制表id")
    private String listerid;
     // 创建者
    @Excel(name = "创建者")
    private String createby;
     // 创建者id
    @Excel(name = "创建者id")
    private String createbyid;
    // 新建日期
    @Excel(name = "新建日期")
    private Date createdate;
    // 修改日期
    @Excel(name = "修改日期")
    private Date modifydate;
    // 自定义1
    @Excel(name = "自定义1")
    private String custom1;
    // 自定义2
    @Excel(name = "自定义2")
    private String custom2;
    // 自定义3
    @Excel(name = "自定义3")
    private String custom3;
    // 自定义4
    @Excel(name = "自定义4")
    private String custom4;
    // 自定义5
    @Excel(name = "自定义5")
    private String custom5;
    // 自定义6
    @Excel(name = "自定义6")
    private String custom6;
    // 自定义7
    @Excel(name = "自定义7")
    private String custom7;
    // 自定义8
    @Excel(name = "自定义8")
    private String custom8;
    // 自定义9
    @Excel(name = "自定义9")
    private String custom9;
    // 自定义10
    @Excel(name = "自定义10")
    private String custom10;
    // 租户id
    @Excel(name = "租户id")
    private String tenantid;
    // 租户名称
    @Excel(name = "租户名称")
    private String tenantname;
    // 乐观锁
    @Excel(name = "乐观锁")
    private Integer revision;



    //
    public static <T> SaFollowviewPojo buildFollowview(String citeid, String citeType, String custname, String itemType, String itemContent, LoginUser loginUser) {

        // 创建跟进记录 IsAuto=1 为自动生成
        SaFollowviewPojo saFollowviewPojo = new SaFollowviewPojo();
        // 通用字段赋值
        saFollowviewPojo.setIsauto(1);
        saFollowviewPojo.setCreateby(loginUser.getRealName());
        saFollowviewPojo.setCreatebyid(loginUser.getUserid());
        saFollowviewPojo.setCreatedate(new Date());
        saFollowviewPojo.setLister(loginUser.getRealname());
        saFollowviewPojo.setListerid(loginUser.getUserid());
        saFollowviewPojo.setModifydate(new Date());

        // 自定义字段赋值
        saFollowviewPojo.setCiteid(citeid);
        saFollowviewPojo.setCitetype(citeType);
        saFollowviewPojo.setItemtype(itemType);
        saFollowviewPojo.setItemcontent(itemContent);
        saFollowviewPojo.setCustname(custname);

        return saFollowviewPojo;
    }


}

