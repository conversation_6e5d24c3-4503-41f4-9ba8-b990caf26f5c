package inks.service.sa.crm.domain;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * 需求字典(SaDemanddict)实体类
 *
 * <AUTHOR>
 * @since 2024-10-11 10:02:21
 */
@Data
public class SaDemanddictEntity implements Serializable {
    private static final long serialVersionUID = 961730767776389033L;
     // ID
    private String id;
     // 需求编码
    private String demandcode;
     // 需求名称
    private String demandname;
     // 需求类型
    private String demandtype;
     // 需求描述
    private String description;
     // 行号
    private Integer rownum;
     // 备注
    private String remark;
     // 创建者
    private String createby;
     // 创建者id
    private String createbyid;
     // 新建日期
    private Date createdate;
     // 制表
    private String lister;
     // 制表id
    private String listerid;
     // 修改日期
    private Date modifydate;
     // 租户id
    private String tenantid;
     // 乐观锁
    private Integer revision;



}

