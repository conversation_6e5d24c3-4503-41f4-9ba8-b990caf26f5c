package inks.service.sa.crm.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;
import inks.common.core.domain.DatailPojo;
import lombok.Data;

/**
 * 样品发货明细(SaSmpdeliitem)Pojo
 *
 * <AUTHOR>
 * @since 2024-09-03 16:20:54
 */
@Data
public class SaSmpdeliitemdetailPojo extends DatailPojo implements Serializable {
    private static final long serialVersionUID = -70626603329938499L;
     // id
  @Excel(name = "id")    
  private String id;
     // Pid
  @Excel(name = "Pid")    
  private String pid;
     // 商品编码
  @Excel(name = "商品编码")    
  private String goodsid;
     // 产品编码
  @Excel(name = "产品编码")    
  private String itemcode;
     // 产品名称
  @Excel(name = "产品名称")    
  private String itemname;
     // 产品规格
  @Excel(name = "产品规格")    
  private String itemspec;
     // 产品单位
  @Excel(name = "产品单位")    
  private String itemunit;
     // 数量
  @Excel(name = "数量")    
  private Double quantity;
     // 含税单价
  @Excel(name = "含税单价")    
  private Double taxprice;
     // 含税金额
  @Excel(name = "含税金额")    
  private Double taxamount;
     // 未税单价
  @Excel(name = "未税单价")    
  private Double price;
     // 未税金额
  @Excel(name = "未税金额")    
  private Double amount;
     // 记录税率
  @Excel(name = "记录税率")    
  private Integer itemtaxrate;
     // 税额
  @Excel(name = "税额")    
  private Double taxtotal;
     // 折扣
  @Excel(name = "折扣")    
  private Integer rebate;
     // 完成数量
  @Excel(name = "完成数量")    
  private Double finishqty;
     // 行号
  @Excel(name = "行号")    
  private Integer rownum;
  @Excel(name = "")
  private String attributejson;
     // SPU文本
  @Excel(name = "SPU文本")
  private String attributestr;
     // 备注
  @Excel(name = "备注")    
  private String remark;
     // 引用号
  @Excel(name = "引用号")    
  private String citeuid;
     // 引用子项id
  @Excel(name = "引用子项id")    
  private String citeitemid;
     // 虚拟货品
  @Excel(name = "虚拟货品")    
  private Integer virtualitem;
     // 关闭
  @Excel(name = "关闭")    
  private Integer closed;
     // 作废
  @Excel(name = "作废")    
  private Integer disannulmark;
     // 作废经办id
  @Excel(name = "作废经办id")    
  private String disannullisterid;
     // 作废经办
  @Excel(name = "作废经办")    
  private String disannullister;
     // 作废日期
  @Excel(name = "作废日期")    
  private Date disannuldate;
     // 自定义1
  @Excel(name = "自定义1")    
  private String custom1;
     // 自定义2
  @Excel(name = "自定义2")    
  private String custom2;
     // 自定义3
  @Excel(name = "自定义3")    
  private String custom3;
     // 自定义4
  @Excel(name = "自定义4")    
  private String custom4;
     // 自定义5
  @Excel(name = "自定义5")    
  private String custom5;
     // 自定义6
  @Excel(name = "自定义6")    
  private String custom6;
     // 自定义7
  @Excel(name = "自定义7")    
  private String custom7;
     // 自定义8
  @Excel(name = "自定义8")    
  private String custom8;
     // 自定义9
  @Excel(name = "自定义9")    
  private String custom9;
     // 自定义10
  @Excel(name = "自定义10")    
  private String custom10;
     // 租户id
  @Excel(name = "租户id")    
  private String tenantid;
     // 乐观锁
  @Excel(name = "乐观锁")    
  private Integer revision;

    //               Sa_SmpPlan.Groupid,
    //               Sa_SmpPlan.Businessid,
    //               Sa_Customer.CustName,
    //               Sa_Business.BillTitle as BusinessTitle,
    private String groupid;
    private String businessid;
    private String custname;
    private String businesstitle;

}

