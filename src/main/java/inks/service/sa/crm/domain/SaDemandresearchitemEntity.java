package inks.service.sa.crm.domain;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * 需求调研子表(SaDemandresearchitem)Entity
 *
 * <AUTHOR>
 * @since 2024-10-11 10:15:13
 */
@Data
public class SaDemandresearchitemEntity implements Serializable {
    private static final long serialVersionUID = -26657503713149972L;
     // ID
    private String id;
     // 主表ID
    private String pid;
     // 需求字典ID
    private String demanddictid;
     // 需求描述
    private String description;
     // 核心需求标识
    private Integer mainmark;
     // 优先级
    private Integer level;
     // 需求提出时间
    private Date requirementdate;
     // 实现难度
    private Integer difficulty;
     // 需求解决方案
    private String solution;
     // 行号
    private Integer rownum;
     // 备注
    private String remark;
     // 租户id
    private String tenantid;
     // 乐观锁
    private Integer revision;


}

