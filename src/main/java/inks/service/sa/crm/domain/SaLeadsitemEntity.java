package inks.service.sa.crm.domain;

import java.io.Serializable;
import java.util.Date;

/**
 * 线索记录(SaLeadsitem)Entity
 *
 * <AUTHOR>
 * @since 2023-03-06 12:46:24
 */
public class SaLeadsitemEntity implements Serializable {
    private static final long serialVersionUID = 602431174922471144L;
    // id
    private String id;
    // Pid
    private String pid;
    // 类型
    private String itemtype;
    // 内容
    private String itemcontent;
    // 图片1
    private String photourl1;
    // 图片2
    private String photourl2;
    // 图片3
    private String photourl3;
    // 图片1
    private String photoname1;
    // 图片2
    private String photoname2;
    // 图片3
    private String photoname3;
    // 行号
    private Integer rownum;
    // 下次联系
    private Date nextdate;
    // 备注
    private String remark;
    // 制表
    private String lister;
    // 新建日期
    private Date createdate;
    // 修改日期
    private Date modifydate;
    // 自定义1
    private String custom1;
    // 自定义2
    private String custom2;
    // 自定义3
    private String custom3;
    // 自定义4
    private String custom4;
    // 自定义5
    private String custom5;
    // 自定义6
    private String custom6;
    // 自定义7
    private String custom7;
    // 自定义8
    private String custom8;
    // 自定义9
    private String custom9;
    // 自定义10
    private String custom10;
    // 租户id
    private String tenantid;
    // 租户名称
    private String tenantname;
    // 乐观锁
    private Integer revision;

    // id
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    // Pid
    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    // 类型
    public String getItemtype() {
        return itemtype;
    }

    public void setItemtype(String itemtype) {
        this.itemtype = itemtype;
    }

    // 内容
    public String getItemcontent() {
        return itemcontent;
    }

    public void setItemcontent(String itemcontent) {
        this.itemcontent = itemcontent;
    }

    // 图片1
    public String getPhotourl1() {
        return photourl1;
    }

    public void setPhotourl1(String photourl1) {
        this.photourl1 = photourl1;
    }

    // 图片2
    public String getPhotourl2() {
        return photourl2;
    }

    public void setPhotourl2(String photourl2) {
        this.photourl2 = photourl2;
    }

    // 图片3
    public String getPhotourl3() {
        return photourl3;
    }

    public void setPhotourl3(String photourl3) {
        this.photourl3 = photourl3;
    }

    // 图片1
    public String getPhotoname1() {
        return photoname1;
    }

    public void setPhotoname1(String photoname1) {
        this.photoname1 = photoname1;
    }

    // 图片2
    public String getPhotoname2() {
        return photoname2;
    }

    public void setPhotoname2(String photoname2) {
        this.photoname2 = photoname2;
    }

    // 图片3
    public String getPhotoname3() {
        return photoname3;
    }

    public void setPhotoname3(String photoname3) {
        this.photoname3 = photoname3;
    }

    // 行号
    public Integer getRownum() {
        return rownum;
    }

    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }

    // 下次联系
    public Date getNextdate() {
        return nextdate;
    }

    public void setNextdate(Date nextdate) {
        this.nextdate = nextdate;
    }

    // 备注
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    // 制表
    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }

    // 新建日期
    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }

    // 修改日期
    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }

    // 自定义1
    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }

    // 自定义2
    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }

    // 自定义3
    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }

    // 自定义4
    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }

    // 自定义5
    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }

    // 自定义6
    public String getCustom6() {
        return custom6;
    }

    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }

    // 自定义7
    public String getCustom7() {
        return custom7;
    }

    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }

    // 自定义8
    public String getCustom8() {
        return custom8;
    }

    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }

    // 自定义9
    public String getCustom9() {
        return custom9;
    }

    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }

    // 自定义10
    public String getCustom10() {
        return custom10;
    }

    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }

    // 租户id
    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }

    // 租户名称
    public String getTenantname() {
        return tenantname;
    }

    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }

    // 乐观锁
    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }


}

