package inks.service.sa.crm.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;
import java.util.List;
import lombok.Data;

/**
 * 样品需求(SaSmpplan)实体类
 *
 * <AUTHOR>
 * @since 2024-09-03 15:17:54
 */
@Data
public class SaSmpplanPojo implements Serializable {
    private static final long serialVersionUID = 365953467680577115L;
     // id
     @Excel(name = "id")
    private String id;
     // 编码
     @Excel(name = "编码")
    private String refno;
     // 单据类型
     @Excel(name = "单据类型")
    private String billtype;
     // 单据日期
     @Excel(name = "单据日期")
    private Date billdate;
     // 单据标题
     @Excel(name = "单据标题")
    private String billtitle;
     // 客户ID
     @Excel(name = "客户ID")
    private String groupid;
     // 商机id
     @Excel(name = "商机id")
    private String businessid;
     // 经办人
     @Excel(name = "经办人")
    private String operator;
     // 经办人id
     @Excel(name = "经办人id")
    private String operatorid;
     // 摘要
     @Excel(name = "摘要")
    private String summary;
     // 创建者
     @Excel(name = "创建者")
    private String createby;
     // 创建者id
     @Excel(name = "创建者id")
    private String createbyid;
     // 新建日期
     @Excel(name = "新建日期")
    private Date createdate;
     // 制表
     @Excel(name = "制表")
    private String lister;
     // 制表id
     @Excel(name = "制表id")
    private String listerid;
     // 修改日期
     @Excel(name = "修改日期")
    private Date modifydate;
     // 审核员
     @Excel(name = "审核员")
    private String assessor;
     // 审核员id
     @Excel(name = "审核员id")
    private String assessorid;
     // 审核日期
     @Excel(name = "审核日期")
    private Date assessdate;
     // 单据状态
     @Excel(name = "单据状态")
    private String billstatecode;
     // 状态日期
     @Excel(name = "状态日期")
    private Date billstatedate;
     // 计划开工
     @Excel(name = "计划开工")
    private Date billstartdate;
     // 单据计划
     @Excel(name = "单据计划")
    private Date billplandate;
     // item行数
     @Excel(name = "item行数")
    private Integer itemcount;
     // Mrp行数
     @Excel(name = "Mrp行数")
    private Integer mrpcount;
     // 开单行数
     @Excel(name = "开单行数")
    private Integer startcount;
     // 作废行数
     @Excel(name = "作废行数")
    private Integer disannulcount;
     // 完成行数
     @Excel(name = "完成行数")
    private Integer finishcount;
     // 打印次数
     @Excel(name = "打印次数")
    private Integer printcount;
     // OA审批中
     @Excel(name = "OA审批中")
    private Integer oaflowmark;
     // 最新工序id
     @Excel(name = "最新工序id")
    private String billwkwpid;
     // 最新工序编码
     @Excel(name = "最新工序编码")
    private String billwkwpcode;
     // 最新工序名称
     @Excel(name = "最新工序名称")
    private String billwkwpname;
     // 自定义1
     @Excel(name = "自定义1")
    private String custom1;
     // 自定义2
     @Excel(name = "自定义2")
    private String custom2;
     // 自定义3
     @Excel(name = "自定义3")
    private String custom3;
     // 自定义4
     @Excel(name = "自定义4")
    private String custom4;
     // 自定义5
     @Excel(name = "自定义5")
    private String custom5;
     // 自定义6
     @Excel(name = "自定义6")
    private String custom6;
     // 自定义7
     @Excel(name = "自定义7")
    private String custom7;
     // 自定义8
     @Excel(name = "自定义8")
    private String custom8;
     // 自定义9
     @Excel(name = "自定义9")
    private String custom9;
     // 自定义10
     @Excel(name = "自定义10")
    private String custom10;
     // 部门id
     @Excel(name = "部门id")
    private String deptid;
     // 租户id
     @Excel(name = "租户id")
    private String tenantid;
     // 租户名称
     @Excel(name = "租户名称")
    private String tenantname;
     // 乐观锁
     @Excel(name = "乐观锁")
    private Integer revision;
    // 子表
    private List<SaSmpplanitemPojo> item;



    private String custname;
    private String businesstitle;
    // 同一个商机这是第几次转到样品需求：查询查询某个 Businessid（如一个商机 ID）在 当前时间之前 ，已经创建过多少条记录，然后 加 1 ，表示“这是第几次转到样品需求”。
    private Integer bustosmptimes;
}

