package inks.service.sa.crm.domain;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * SPU属性表(SaAttribute)实体类
 *
 * <AUTHOR>
 * @since 2024-10-25 17:09:05
 */
@Data
public class SaAttributeEntity implements Serializable {
    private static final long serialVersionUID = -45250275450698277L;
     // id
    private String id;
     // 通用分组
    private String attrgroupid;
     // 属性Key
    private String attrkey;
     // 属性名称
    private String attrname;
     // 数值类型
    private String valuetype;
     // 默认值
    private String defvalue;
     // 可择值
    private String valuejson;
     // 列表显示
    private Integer listshow;
     // 有效标识
    private Integer enabledmark;
     // 存储SKU
    private Integer skumark;
     // 备注
    private String remark;
    private Integer rownum;
     // 创建者
    private String createby;
     // 创建者id
    private String createbyid;
     // 新建日期
    private Date createdate;
     // 制表
    private String lister;
     // 制表id
    private String listerid;
     // 修改日期
    private Date modifydate;
     // 自定义1
    private String custom1;
     // 自定义2
    private String custom2;
     // 自定义3
    private String custom3;
     // 自定义4
    private String custom4;
     // 自定义5
    private String custom5;
     // 自定义6
    private String custom6;
     // 自定义7
    private String custom7;
     // 自定义8
    private String custom8;
     // 自定义9
    private String custom9;
     // 自定义10
    private String custom10;
     // 租户id
    private String tenantid;
     // 租户名称
    private String tenantname;
     // 乐观锁
    private Integer revision;



}

