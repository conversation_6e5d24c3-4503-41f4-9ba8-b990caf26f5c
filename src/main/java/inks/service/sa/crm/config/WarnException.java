package inks.service.sa.crm.config;

/**
 * 通用业务警告异常（默认HTTP状态码422，支持自定义code与返回结构化data）
 */
public class WarnException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    private final int code;
    private final Object data;

    private static final int DEFAULT_CODE = 422;

    /**
     * ✅ 构造方法：仅传字符串消息，状态码默认 422
     *
     * 示例：
     * throw new WarnException("参数不能为空");
     *
     * 返回：
     * {
     *   "code": 422,
     *   "msg": "参数不能为空"
     * }
     */
    public WarnException(String message) {
        super(message);
        this.code = DEFAULT_CODE;
        this.data = null;
    }

    /**
     * ✅ 构造方法：传入消息 + data，状态码默认 422
     *
     * 示例：
     * throw new WarnException("验证失败", pojo);
     *
     * 返回：
     * {
     *   "code": 422,
     *   "msg": "验证失败",
     *   "data": { ... }
     * }
     */
    public WarnException(String message, Object data) {
        super(message);
        this.code = DEFAULT_CODE;
        this.data = data;
    }

    /**
     * ✅ 构造方法：传入状态码 + 消息
     *
     * 示例：
     * throw new WarnException(666, "非法请求");
     *
     * 返回：
     * {
     *   "code": 666,
     *   "msg": "非法请求"
     * }
     */
    public WarnException(int code, String message) {
        super(message);
        this.code = code;
        this.data = null;
    }

    /**
     * ✅ 构造方法：传入状态码 + 消息 + data
     *
     * 示例：
     * throw new WarnException(666, "字段校验失败", pojo);
     *
     * 返回：
     * {
     *   "code": 666,
     *   "msg": "字段校验失败",
     *   "data": { ... }
     * }
     */
    public WarnException(int code, String message, Object data) {
        super(message);
        this.code = code;
        this.data = data;
    }

    public int getCode() {
        return code;
    }

    public Object getData() {
        return data;
    }
}
