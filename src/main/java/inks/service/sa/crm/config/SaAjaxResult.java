//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package inks.service.sa.crm.config;

import inks.common.core.domain.AjaxResult;
import inks.common.core.utils.StringUtils;
import java.util.HashMap;

public class SaAjaxResult extends HashMap<String, Object> {
    private static final long serialVersionUID = 1L;
    public static final String CODE_TAG = "code";
    public static final String MSG_TAG = "msg";
    public static final String DATA_TAG = "data";

    public SaAjaxResult() {
    }

    public SaAjaxResult(int code, String msg) {
        super.put("code", code);
        super.put("msg", msg);
    }

    public SaAjaxResult(int code, String msg, Object data) {
        super.put("code", code);
        super.put("msg", msg);
        if (StringUtils.isNotNull(data)) {
            super.put("data", data);
        }

    }
    /**
     * 通用信息响应（中性提示）
     *
     * 示例：
     * return AjaxResult.info(300, "请确认信息", extraInfo);
     */
    public static AjaxResult info(int code, String msg, Object data) {
        return new AjaxResult(code, msg, data);
    }


    public static SaAjaxResult success() {
        return success("操作成功");
    }

    public static SaAjaxResult success(Object data) {
        return success("操作成功", data);
    }

    public static SaAjaxResult success(String msg) {
        return success(msg, (Object)null);
    }

    public static SaAjaxResult success(String msg, Object data) {
        return new SaAjaxResult(200, msg, data);
    }

    public static SaAjaxResult error() {
        return error("操作失败");
    }

    public static SaAjaxResult error(String msg) {
        return error(msg, (Object)null);
    }

    public static SaAjaxResult error(String msg, Object data) {
        return new SaAjaxResult(500, msg, data);
    }

    public static SaAjaxResult error(int code, String msg) {
        return new SaAjaxResult(code, msg, (Object)null);
    }
}
