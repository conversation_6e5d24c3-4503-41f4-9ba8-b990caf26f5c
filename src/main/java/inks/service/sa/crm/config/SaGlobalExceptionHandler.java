package inks.service.sa.crm.config;

import inks.common.core.domain.AjaxResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * 全局异常处理器
 */
@RestControllerAdvice
public class SaGlobalExceptionHandler {

    private static final Logger log = LoggerFactory.getLogger(SaGlobalExceptionHandler.class);

    /**
     * 捕获业务警告类 自定义异状态码
     */
    @ExceptionHandler(WarnException.class)
    public AjaxResult handleInksWarnException(WarnException e) {
        return SaAjaxResult.info(e.getCode(), e.getMessage(), e.getData());
    }

}
