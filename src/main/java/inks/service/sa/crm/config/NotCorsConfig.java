//package inks.service.sa.crm.config;
//
//import org.springframework.boot.web.servlet.FilterRegistrationBean;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.context.annotation.Primary;
//import org.springframework.web.cors.CorsConfiguration;
//import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
//import org.springframework.web.filter.CorsFilter;
//
//@Configuration
//public class NotCorsConfig {
//    @Bean
//    @Primary
//    public FilterRegistrationBean<CorsFilter> corsFilter() {
//        // 创建一个严格的CORS配置
//        CorsConfiguration config = new CorsConfiguration();
//        config.setAllowCredentials(false);
//        // 不添加任何允许的源、头部或方法，实际上会阻止所有跨域请求
//
//        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
//        source.registerCorsConfiguration("/**", config);
//
//        FilterRegistrationBean<CorsFilter> bean = new FilterRegistrationBean<>(new CorsFilter(source));
//        bean.setOrder(0);  // 设置高优先级
//        return bean;
//    }
//}
