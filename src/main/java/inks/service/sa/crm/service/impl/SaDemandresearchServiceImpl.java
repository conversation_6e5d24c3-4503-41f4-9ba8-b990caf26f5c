package inks.service.sa.crm.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.crm.domain.pojo.SaDemandresearchPojo;
import inks.service.sa.crm.domain.pojo.SaDemandresearchitemPojo;
import inks.service.sa.crm.domain.pojo.SaDemandresearchitemdetailPojo;
import inks.service.sa.crm.domain.SaDemandresearchEntity;
import inks.service.sa.crm.domain.SaDemandresearchitemEntity;
import inks.service.sa.crm.mapper.SaDemandresearchMapper;
import inks.service.sa.crm.service.SaDemandresearchService;
import inks.service.sa.crm.service.SaDemandresearchitemService;
import inks.service.sa.crm.mapper.SaDemandresearchitemMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import inks.common.core.text.inksSnowflake;
/**
 * 需求调研(SaDemandresearch)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-10-11 10:15:07
 */
@Service("saDemandresearchService")
public class SaDemandresearchServiceImpl implements SaDemandresearchService {
    @Resource
    private SaDemandresearchMapper saDemandresearchMapper;
    
    @Resource
    private SaDemandresearchitemMapper saDemandresearchitemMapper;
    

    @Resource
    private SaDemandresearchitemService saDemandresearchitemService;
    

    @Override
    public SaDemandresearchPojo getEntity(String key) {
        return this.saDemandresearchMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaDemandresearchitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaDemandresearchitemdetailPojo> lst = saDemandresearchMapper.getPageList(queryParam);
            PageInfo<SaDemandresearchitemdetailPojo> pageInfo = new PageInfo<SaDemandresearchitemdetailPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }
    

    @Override
    public SaDemandresearchPojo getBillEntity(String key) {
       try {
        //读取主表
        SaDemandresearchPojo saDemandresearchPojo = this.saDemandresearchMapper.getEntity(key);
        //读取子表
        saDemandresearchPojo.setItem(saDemandresearchitemMapper.getList(saDemandresearchPojo.getId()));
        return saDemandresearchPojo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public PageInfo<SaDemandresearchPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaDemandresearchPojo> lst = saDemandresearchMapper.getPageTh(queryParam);
             //循环设置每个主表对象的item子表
            for(int i=0;i<lst.size();i++){
                lst.get(i).setItem(saDemandresearchitemMapper.getList(lst.get(i).getId()));
            }
            PageInfo<SaDemandresearchPojo> pageInfo = new PageInfo<SaDemandresearchPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }
    

    @Override
    public PageInfo<SaDemandresearchPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaDemandresearchPojo> lst = saDemandresearchMapper.getPageTh(queryParam);
            PageInfo<SaDemandresearchPojo> pageInfo = new PageInfo<SaDemandresearchPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    @Transactional
    public SaDemandresearchPojo insert(SaDemandresearchPojo saDemandresearchPojo) {
        //初始化NULL字段
        cleanNull(saDemandresearchPojo);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        SaDemandresearchEntity saDemandresearchEntity = new SaDemandresearchEntity(); 
        BeanUtils.copyProperties(saDemandresearchPojo,saDemandresearchEntity);
        //设置id和新建日期
        saDemandresearchEntity.setId(id);
        saDemandresearchEntity.setRevision(1);  //乐观锁
        //插入主表
        this.saDemandresearchMapper.insert(saDemandresearchEntity);
        //Item子表处理
        List<SaDemandresearchitemPojo> lst = saDemandresearchPojo.getItem();
        if (lst != null){
            //循环每个item子表
            for(int i=0;i<lst.size();i++){
               //初始化item的NULL
               SaDemandresearchitemPojo itemPojo =this.saDemandresearchitemService.clearNull(lst.get(i));
               SaDemandresearchitemEntity saDemandresearchitemEntity = new SaDemandresearchitemEntity(); 
               BeanUtils.copyProperties(itemPojo,saDemandresearchitemEntity);
               //设置id和Pid
               saDemandresearchitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
               saDemandresearchitemEntity.setPid(id);
               saDemandresearchitemEntity.setRevision(1);  //乐观锁
               //插入子表
               this.saDemandresearchitemMapper.insert(saDemandresearchitemEntity);
            }
        } 
        //返回Bill实例
        return this.getBillEntity(saDemandresearchEntity.getId());
    }


    @Override
    @Transactional
    public SaDemandresearchPojo update(SaDemandresearchPojo saDemandresearchPojo) {
        //主表更改
        SaDemandresearchEntity saDemandresearchEntity = new SaDemandresearchEntity(); 
        BeanUtils.copyProperties(saDemandresearchPojo,saDemandresearchEntity);
        this.saDemandresearchMapper.update(saDemandresearchEntity);
        if (saDemandresearchPojo.getItem() != null) {
        //Item子表处理
        List<SaDemandresearchitemPojo> lst = saDemandresearchPojo.getItem();
        //获取被删除的Item
         List<String> lstDelIds =saDemandresearchMapper.getDelItemIds(saDemandresearchPojo);
        if (lstDelIds != null){
            //循环每个删除item子表
            for(int i=0;i<lstDelIds.size();i++){
             this.saDemandresearchitemMapper.delete(lstDelIds.get(i));
            }
        }
        if (lst != null){
            //循环每个item子表
            for(int i=0;i<lst.size();i++){
               SaDemandresearchitemEntity saDemandresearchitemEntity = new SaDemandresearchitemEntity(); 
               if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null){
                //初始化item的NULL
               SaDemandresearchitemPojo itemPojo =this.saDemandresearchitemService.clearNull(lst.get(i));
               BeanUtils.copyProperties(itemPojo,saDemandresearchitemEntity);
               //设置id和Pid
               saDemandresearchitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
               saDemandresearchitemEntity.setPid(saDemandresearchEntity.getId());  // 主表 id
               saDemandresearchitemEntity.setRevision(1);  // 乐观锁
               //插入子表
               this.saDemandresearchitemMapper.insert(saDemandresearchitemEntity);
               }
               else
               {
               BeanUtils.copyProperties(lst.get(i),saDemandresearchitemEntity);             
               this.saDemandresearchitemMapper.update(saDemandresearchitemEntity);
               }
            }
        } 
        }
        //返回Bill实例
        return this.getBillEntity(saDemandresearchEntity.getId());
    }


    @Override
    @Transactional
    public int delete(String key) {
       SaDemandresearchPojo saDemandresearchPojo =  this.getBillEntity(key);
        //Item子表处理
        List<SaDemandresearchitemPojo> lst = saDemandresearchPojo.getItem();
        if (lst != null){
            //循环每个删除item子表
            for(int i=0;i<lst.size();i++){
             this.saDemandresearchitemMapper.delete(lst.get(i).getId());
            }
        }        
        return this.saDemandresearchMapper.delete(key) ;
    }
    

    
    @Override
    @Transactional
    public SaDemandresearchPojo approval(SaDemandresearchPojo saDemandresearchPojo) {
        //主表更改
        SaDemandresearchEntity saDemandresearchEntity = new SaDemandresearchEntity();
        BeanUtils.copyProperties(saDemandresearchPojo,saDemandresearchEntity);
        this.saDemandresearchMapper.approval(saDemandresearchEntity);
        //返回Bill实例
        return this.getBillEntity(saDemandresearchEntity.getId());
    }

    private static void cleanNull(SaDemandresearchPojo saDemandresearchPojo) {
        if(saDemandresearchPojo.getRefno()==null) saDemandresearchPojo.setRefno("");
        if(saDemandresearchPojo.getBilltype()==null) saDemandresearchPojo.setBilltype("");
        if(saDemandresearchPojo.getBilltitle()==null) saDemandresearchPojo.setBilltitle("");
        if(saDemandresearchPojo.getBilldate()==null) saDemandresearchPojo.setBilldate(new Date());
        if(saDemandresearchPojo.getGroupid()==null) saDemandresearchPojo.setGroupid("");
        if(saDemandresearchPojo.getGroupname()==null) saDemandresearchPojo.setGroupname("");
        if(saDemandresearchPojo.getDescription()==null) saDemandresearchPojo.setDescription("");
        if(saDemandresearchPojo.getRelatedservice()==null) saDemandresearchPojo.setRelatedservice("");
        if(saDemandresearchPojo.getExpecteddate()==null) saDemandresearchPojo.setExpecteddate(new Date());
        if(saDemandresearchPojo.getAssessor()==null) saDemandresearchPojo.setAssessor("");
        if(saDemandresearchPojo.getAssessorid()==null) saDemandresearchPojo.setAssessorid("");
        if(saDemandresearchPojo.getAssessdate()==null) saDemandresearchPojo.setAssessdate(new Date());
        if(saDemandresearchPojo.getRownum()==null) saDemandresearchPojo.setRownum(0);
        if(saDemandresearchPojo.getRemark()==null) saDemandresearchPojo.setRemark("");
        if(saDemandresearchPojo.getCreateby()==null) saDemandresearchPojo.setCreateby("");
        if(saDemandresearchPojo.getCreatebyid()==null) saDemandresearchPojo.setCreatebyid("");
        if(saDemandresearchPojo.getCreatedate()==null) saDemandresearchPojo.setCreatedate(new Date());
        if(saDemandresearchPojo.getLister()==null) saDemandresearchPojo.setLister("");
        if(saDemandresearchPojo.getListerid()==null) saDemandresearchPojo.setListerid("");
        if(saDemandresearchPojo.getModifydate()==null) saDemandresearchPojo.setModifydate(new Date());
        if(saDemandresearchPojo.getTenantid()==null) saDemandresearchPojo.setTenantid("");
        if(saDemandresearchPojo.getTenantname()==null) saDemandresearchPojo.setTenantname("");
        if(saDemandresearchPojo.getRevision()==null) saDemandresearchPojo.setRevision(0);
   }

}
