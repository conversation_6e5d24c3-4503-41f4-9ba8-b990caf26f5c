package inks.service.sa.crm.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.pojo.SaLeadsitemPojo;

import java.util.List;

/**
 * 线索记录(SaLeadsitem)表服务接口
 *
 * <AUTHOR>
 * @since 2023-03-06 12:46:24
 */
public interface SaLeadsitemService {


    SaLeadsitemPojo getEntity(String key);


    PageInfo<SaLeadsitemPojo> getPageList(QueryParam queryParam);

    List<SaLeadsitemPojo> getList(String Pid);

    /**
     * 新增数据
     *
     * @param saLeadsitemPojo 实例对象
     * @return 实例对象
     */
    SaLeadsitemPojo insert(SaLeadsitemPojo saLeadsitemPojo);

    /**
     * 修改数据
     *
     * @param saLeadsitempojo 实例对象
     * @return 实例对象
     */
    SaLeadsitemPojo update(SaLeadsitemPojo saLeadsitempojo);


    int delete(String key);

    /**
     * 修改数据
     *
     * @param saLeadsitempojo 实例对象
     * @return 实例对象
     */
    SaLeadsitemPojo clearNull(SaLeadsitemPojo saLeadsitempojo);
}
