package inks.service.sa.crm.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.crm.domain.SaContractitemEntity;
import inks.service.sa.crm.domain.pojo.SaContractitemPojo;
import inks.service.sa.crm.mapper.SaContractitemMapper;
import inks.service.sa.crm.service.SaContractitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 合同项目(SaContractitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-03-06 11:18:29
 */
@Service("saContractitemService")
public class SaContractitemServiceImpl implements SaContractitemService {
    @Resource
    private SaContractitemMapper saContractitemMapper;


    @Override
    public SaContractitemPojo getEntity(String key) {
        return this.saContractitemMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaContractitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaContractitemPojo> lst = saContractitemMapper.getPageList(queryParam);
            PageInfo<SaContractitemPojo> pageInfo = new PageInfo<SaContractitemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public List<SaContractitemPojo> getList(String Pid) {
        try {
            List<SaContractitemPojo> lst = saContractitemMapper.getList(Pid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param saContractitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaContractitemPojo insert(SaContractitemPojo saContractitemPojo) {
        //初始化item的NULL
        SaContractitemPojo itempojo = this.clearNull(saContractitemPojo);
        SaContractitemEntity saContractitemEntity = new SaContractitemEntity();
        BeanUtils.copyProperties(itempojo, saContractitemEntity);

        saContractitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saContractitemEntity.setRevision(1);  //乐观锁
        this.saContractitemMapper.insert(saContractitemEntity);
        return this.getEntity(saContractitemEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saContractitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaContractitemPojo update(SaContractitemPojo saContractitemPojo) {
        SaContractitemEntity saContractitemEntity = new SaContractitemEntity();
        BeanUtils.copyProperties(saContractitemPojo, saContractitemEntity);
        this.saContractitemMapper.update(saContractitemEntity);
        return this.getEntity(saContractitemEntity.getId());
    }


    @Override
    public int delete(String key) {
        return this.saContractitemMapper.delete(key);
    }

    /**
     * 修改数据
     *
     * @param saContractitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaContractitemPojo clearNull(SaContractitemPojo saContractitemPojo) {
        //初始化NULL字段
        if (saContractitemPojo.getPid() == null) saContractitemPojo.setPid("");
        if (saContractitemPojo.getGoodsid() == null) saContractitemPojo.setGoodsid("");
        if (saContractitemPojo.getItemcode() == null) saContractitemPojo.setItemcode("");
        if (saContractitemPojo.getItemname() == null) saContractitemPojo.setItemname("");
        if (saContractitemPojo.getItemspec() == null) saContractitemPojo.setItemspec("");
        if (saContractitemPojo.getItemunit() == null) saContractitemPojo.setItemunit("");
        if (saContractitemPojo.getPrice() == null) saContractitemPojo.setPrice(0D);
        if (saContractitemPojo.getQuantity() == null) saContractitemPojo.setQuantity(0D);
        if (saContractitemPojo.getItemtaxrate() == null) saContractitemPojo.setItemtaxrate(0);
        if (saContractitemPojo.getAmount() == null) saContractitemPojo.setAmount(0D);
        if (saContractitemPojo.getTaxtotal() == null) saContractitemPojo.setTaxtotal(0D);
        if (saContractitemPojo.getTaxprice() == null) saContractitemPojo.setTaxprice(0D);
        if (saContractitemPojo.getTaxamount() == null) saContractitemPojo.setTaxamount(0D);
        if (saContractitemPojo.getOperator() == null) saContractitemPojo.setOperator("");
     if(saContractitemPojo.getOperatorid()==null) saContractitemPojo.setOperatorid("");
     if(saContractitemPojo.getChanceitemid()==null) saContractitemPojo.setChanceitemid("");
        if (saContractitemPojo.getRownum() == null) saContractitemPojo.setRownum(0);
     if(saContractitemPojo.getAttributejson()==null) saContractitemPojo.setAttributejson("");
     if(saContractitemPojo.getAttributestr()==null) saContractitemPojo.setAttributestr("");
     if(saContractitemPojo.getRemark()==null) saContractitemPojo.setRemark("");
        if (saContractitemPojo.getCustom1() == null) saContractitemPojo.setCustom1("");
        if (saContractitemPojo.getCustom2() == null) saContractitemPojo.setCustom2("");
        if (saContractitemPojo.getCustom3() == null) saContractitemPojo.setCustom3("");
        if (saContractitemPojo.getCustom4() == null) saContractitemPojo.setCustom4("");
        if (saContractitemPojo.getCustom5() == null) saContractitemPojo.setCustom5("");
        if (saContractitemPojo.getCustom6() == null) saContractitemPojo.setCustom6("");
        if (saContractitemPojo.getCustom7() == null) saContractitemPojo.setCustom7("");
        if (saContractitemPojo.getCustom8() == null) saContractitemPojo.setCustom8("");
        if (saContractitemPojo.getCustom9() == null) saContractitemPojo.setCustom9("");
        if (saContractitemPojo.getCustom10() == null) saContractitemPojo.setCustom10("");
        if (saContractitemPojo.getTenantid() == null) saContractitemPojo.setTenantid("");
        if (saContractitemPojo.getRevision() == null) saContractitemPojo.setRevision(0);
        return saContractitemPojo;
    }
}
