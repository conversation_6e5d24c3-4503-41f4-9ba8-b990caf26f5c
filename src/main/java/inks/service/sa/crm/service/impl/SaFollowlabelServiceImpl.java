package inks.service.sa.crm.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.crm.domain.pojo.SaFollowlabelPojo;
import inks.service.sa.crm.domain.SaFollowlabelEntity;
import inks.service.sa.crm.mapper.SaFollowlabelMapper;
import inks.service.sa.crm.service.SaFollowlabelService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import inks.common.core.text.inksSnowflake;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
/**
 * 跟进标签(SaFollowlabel)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-04-24 09:39:51
 */
@Service("saFollowlabelService")
public class SaFollowlabelServiceImpl implements SaFollowlabelService {
    @Resource
    private SaFollowlabelMapper saFollowlabelMapper;

    @Override
    public SaFollowlabelPojo getEntity(String key) {
        return this.saFollowlabelMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaFollowlabelPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaFollowlabelPojo> lst = saFollowlabelMapper.getPageList(queryParam);
            PageInfo<SaFollowlabelPojo> pageInfo = new PageInfo<SaFollowlabelPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public SaFollowlabelPojo insert(SaFollowlabelPojo saFollowlabelPojo) {
        //初始化NULL字段
        cleanNull(saFollowlabelPojo);
        SaFollowlabelEntity saFollowlabelEntity = new SaFollowlabelEntity(); 
        BeanUtils.copyProperties(saFollowlabelPojo,saFollowlabelEntity);
        //生成雪花id
          saFollowlabelEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          saFollowlabelEntity.setRevision(1);  //乐观锁
          this.saFollowlabelMapper.insert(saFollowlabelEntity);
        return this.getEntity(saFollowlabelEntity.getId());
  
    }

    /**
     * 修改数据
     *
     * @param saFollowlabelPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaFollowlabelPojo update(SaFollowlabelPojo saFollowlabelPojo) {
        SaFollowlabelEntity saFollowlabelEntity = new SaFollowlabelEntity(); 
        BeanUtils.copyProperties(saFollowlabelPojo,saFollowlabelEntity);
        this.saFollowlabelMapper.update(saFollowlabelEntity);
        return this.getEntity(saFollowlabelEntity.getId());
    }


    @Override
    public int delete(String key) {
        return this.saFollowlabelMapper.delete(key) ;
    }
    

    private static void cleanNull(SaFollowlabelPojo saFollowlabelPojo) {
        if(saFollowlabelPojo.getLabelname()==null) saFollowlabelPojo.setLabelname("");
        if(saFollowlabelPojo.getLabelcolor()==null) saFollowlabelPojo.setLabelcolor("");
        if(saFollowlabelPojo.getWeight()==null) saFollowlabelPojo.setWeight(0);
        if(saFollowlabelPojo.getRownum()==null) saFollowlabelPojo.setRownum(0);
        if(saFollowlabelPojo.getRemark()==null) saFollowlabelPojo.setRemark("");
        if(saFollowlabelPojo.getCreateby()==null) saFollowlabelPojo.setCreateby("");
        if(saFollowlabelPojo.getCreatebyid()==null) saFollowlabelPojo.setCreatebyid("");
        if(saFollowlabelPojo.getCreatedate()==null) saFollowlabelPojo.setCreatedate(new Date());
        if(saFollowlabelPojo.getLister()==null) saFollowlabelPojo.setLister("");
        if(saFollowlabelPojo.getListerid()==null) saFollowlabelPojo.setListerid("");
        if(saFollowlabelPojo.getModifydate()==null) saFollowlabelPojo.setModifydate(new Date());
        if(saFollowlabelPojo.getCustom1()==null) saFollowlabelPojo.setCustom1("");
        if(saFollowlabelPojo.getCustom2()==null) saFollowlabelPojo.setCustom2("");
        if(saFollowlabelPojo.getCustom3()==null) saFollowlabelPojo.setCustom3("");
        if(saFollowlabelPojo.getCustom4()==null) saFollowlabelPojo.setCustom4("");
        if(saFollowlabelPojo.getCustom5()==null) saFollowlabelPojo.setCustom5("");
        if(saFollowlabelPojo.getDeptid()==null) saFollowlabelPojo.setDeptid("");
        if(saFollowlabelPojo.getTenantid()==null) saFollowlabelPojo.setTenantid("");
        if(saFollowlabelPojo.getTenantname()==null) saFollowlabelPojo.setTenantname("");
        if(saFollowlabelPojo.getRevision()==null) saFollowlabelPojo.setRevision(0);
   }

}
