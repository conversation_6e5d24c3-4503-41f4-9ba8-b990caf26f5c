package inks.service.sa.crm.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.pojo.SaReceiptplanPojo;

/**
 * 收款计划(SaReceiptplan)表服务接口
 *
 * <AUTHOR>
 * @since 2023-03-25 10:48:43
 */
public interface SaReceiptplanService {


    SaReceiptplanPojo getEntity(String key);


    PageInfo<SaReceiptplanPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saReceiptplanPojo 实例对象
     * @return 实例对象
     */
    SaReceiptplanPojo insert(SaReceiptplanPojo saReceiptplanPojo);

    /**
     * 修改数据
     *
     * @param saReceiptplanpojo 实例对象
     * @return 实例对象
     */
    SaReceiptplanPojo update(SaReceiptplanPojo saReceiptplanpojo);


    int delete(String key);

    /**
     * 审核数据
     *
     * @param saReceiptplanPojo 实例对象
     * @return 实例对象
     */
    SaReceiptplanPojo approval(SaReceiptplanPojo saReceiptplanPojo);

    SaReceiptplanPojo closed(String key, Integer type, LoginUser loginUser);
}
