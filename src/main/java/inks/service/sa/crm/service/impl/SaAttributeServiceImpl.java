package inks.service.sa.crm.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.crm.domain.pojo.SaAttributePojo;
import inks.service.sa.crm.domain.SaAttributeEntity;
import inks.service.sa.crm.mapper.SaAttributeMapper;
import inks.service.sa.crm.service.SaAttributeService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import inks.common.core.text.inksSnowflake;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
/**
 * SPU属性表(SaAttribute)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-10-25 17:09:07
 */
@Service("saAttributeService")
public class SaAttributeServiceImpl implements SaAttributeService {
    @Resource
    private SaAttributeMapper saAttributeMapper;

    @Override
    public SaAttributePojo getEntity(String key) {
        return this.saAttributeMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaAttributePojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaAttributePojo> lst = saAttributeMapper.getPageList(queryParam);
            PageInfo<SaAttributePojo> pageInfo = new PageInfo<SaAttributePojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public SaAttributePojo insert(SaAttributePojo saAttributePojo) {
        //初始化NULL字段
        cleanNull(saAttributePojo);
        SaAttributeEntity saAttributeEntity = new SaAttributeEntity(); 
        BeanUtils.copyProperties(saAttributePojo,saAttributeEntity);
        //生成雪花id
          saAttributeEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          saAttributeEntity.setRevision(1);  //乐观锁
          this.saAttributeMapper.insert(saAttributeEntity);
        return this.getEntity(saAttributeEntity.getId());
  
    }

    /**
     * 修改数据
     *
     * @param saAttributePojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaAttributePojo update(SaAttributePojo saAttributePojo) {
        SaAttributeEntity saAttributeEntity = new SaAttributeEntity(); 
        BeanUtils.copyProperties(saAttributePojo,saAttributeEntity);
        this.saAttributeMapper.update(saAttributeEntity);
        return this.getEntity(saAttributeEntity.getId());
    }


    @Override
    public int delete(String key) {
        return this.saAttributeMapper.delete(key) ;
    }
    

    private static void cleanNull(SaAttributePojo saAttributePojo) {
        if(saAttributePojo.getAttrgroupid()==null) saAttributePojo.setAttrgroupid("");
        if(saAttributePojo.getAttrkey()==null) saAttributePojo.setAttrkey("");
        if(saAttributePojo.getAttrname()==null) saAttributePojo.setAttrname("");
        if(saAttributePojo.getValuetype()==null) saAttributePojo.setValuetype("");
        if(saAttributePojo.getDefvalue()==null) saAttributePojo.setDefvalue("");
        if(saAttributePojo.getValuejson()==null) saAttributePojo.setValuejson("");
        if(saAttributePojo.getListshow()==null) saAttributePojo.setListshow(0);
        if(saAttributePojo.getEnabledmark()==null) saAttributePojo.setEnabledmark(0);
        if(saAttributePojo.getSkumark()==null) saAttributePojo.setSkumark(0);
        if(saAttributePojo.getRemark()==null) saAttributePojo.setRemark("");
        if(saAttributePojo.getRownum()==null) saAttributePojo.setRownum(0);
        if(saAttributePojo.getCreateby()==null) saAttributePojo.setCreateby("");
        if(saAttributePojo.getCreatebyid()==null) saAttributePojo.setCreatebyid("");
        if(saAttributePojo.getCreatedate()==null) saAttributePojo.setCreatedate(new Date());
        if(saAttributePojo.getLister()==null) saAttributePojo.setLister("");
        if(saAttributePojo.getListerid()==null) saAttributePojo.setListerid("");
        if(saAttributePojo.getModifydate()==null) saAttributePojo.setModifydate(new Date());
        if(saAttributePojo.getCustom1()==null) saAttributePojo.setCustom1("");
        if(saAttributePojo.getCustom2()==null) saAttributePojo.setCustom2("");
        if(saAttributePojo.getCustom3()==null) saAttributePojo.setCustom3("");
        if(saAttributePojo.getCustom4()==null) saAttributePojo.setCustom4("");
        if(saAttributePojo.getCustom5()==null) saAttributePojo.setCustom5("");
        if(saAttributePojo.getCustom6()==null) saAttributePojo.setCustom6("");
        if(saAttributePojo.getCustom7()==null) saAttributePojo.setCustom7("");
        if(saAttributePojo.getCustom8()==null) saAttributePojo.setCustom8("");
        if(saAttributePojo.getCustom9()==null) saAttributePojo.setCustom9("");
        if(saAttributePojo.getCustom10()==null) saAttributePojo.setCustom10("");
        if(saAttributePojo.getTenantid()==null) saAttributePojo.setTenantid("");
        if(saAttributePojo.getTenantname()==null) saAttributePojo.setTenantname("");
        if(saAttributePojo.getRevision()==null) saAttributePojo.setRevision(0);
   }
    @Override
    public List<SaAttributePojo> getListByShow(String tid) {
        return this.saAttributeMapper.getListByShow(tid);

    }

}
