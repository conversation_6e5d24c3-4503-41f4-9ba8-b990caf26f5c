package inks.service.sa.crm.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.crm.domain.pojo.SaSmpplanitemPojo;
import inks.service.sa.crm.domain.SaSmpplanitemEntity;
import inks.service.sa.crm.mapper.SaSmpplanitemMapper;
import inks.service.sa.crm.service.SaSmpplanitemService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import inks.common.core.text.inksSnowflake;
/**
 * 计划项目(SaSmpplanitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-09-03 15:18:10
 */
@Service("saSmpplanitemService")
public class SaSmpplanitemServiceImpl implements SaSmpplanitemService {
    @Resource
    private SaSmpplanitemMapper saSmpplanitemMapper;

    @Override
    public SaSmpplanitemPojo getEntity(String key) {
        return this.saSmpplanitemMapper.getEntity(key);
    }

    @Override
    public PageInfo<SaSmpplanitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaSmpplanitemPojo> lst = saSmpplanitemMapper.getPageList(queryParam);
            PageInfo<SaSmpplanitemPojo> pageInfo = new PageInfo<SaSmpplanitemPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    @Override
    public List<SaSmpplanitemPojo> getList(String Pid) { 
        try {
            List<SaSmpplanitemPojo> lst = saSmpplanitemMapper.getList(Pid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }      

    @Override
    public SaSmpplanitemPojo insert(SaSmpplanitemPojo saSmpplanitemPojo) {
        //初始化item的NULL
        SaSmpplanitemPojo itempojo =this.clearNull(saSmpplanitemPojo);
        SaSmpplanitemEntity saSmpplanitemEntity = new SaSmpplanitemEntity(); 
        BeanUtils.copyProperties(itempojo,saSmpplanitemEntity);
         //生成雪花id
          saSmpplanitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          saSmpplanitemEntity.setRevision(1);  //乐观锁      
          this.saSmpplanitemMapper.insert(saSmpplanitemEntity);
        return this.getEntity(saSmpplanitemEntity.getId());
  
    }

    @Override
    public SaSmpplanitemPojo update(SaSmpplanitemPojo saSmpplanitemPojo) {
        SaSmpplanitemEntity saSmpplanitemEntity = new SaSmpplanitemEntity(); 
        BeanUtils.copyProperties(saSmpplanitemPojo,saSmpplanitemEntity);
        this.saSmpplanitemMapper.update(saSmpplanitemEntity);
        return this.getEntity(saSmpplanitemEntity.getId());
    }

    @Override
    public int delete(String key) {
        return this.saSmpplanitemMapper.delete(key) ;
    }

     @Override
     public SaSmpplanitemPojo clearNull(SaSmpplanitemPojo saSmpplanitemPojo){
     //初始化NULL字段
     if(saSmpplanitemPojo.getPid()==null) saSmpplanitemPojo.setPid("");
     if(saSmpplanitemPojo.getGoodsid()==null) saSmpplanitemPojo.setGoodsid("");
     if(saSmpplanitemPojo.getItemcode()==null) saSmpplanitemPojo.setItemcode("");
     if(saSmpplanitemPojo.getItemname()==null) saSmpplanitemPojo.setItemname("");
     if(saSmpplanitemPojo.getItemspec()==null) saSmpplanitemPojo.setItemspec("");
     if(saSmpplanitemPojo.getItemunit()==null) saSmpplanitemPojo.setItemunit("");
     if(saSmpplanitemPojo.getQuantity()==null) saSmpplanitemPojo.setQuantity(0D);
     if(saSmpplanitemPojo.getPrice()==null) saSmpplanitemPojo.setPrice(0D);
     if(saSmpplanitemPojo.getAmount()==null) saSmpplanitemPojo.setAmount(0D);
     if(saSmpplanitemPojo.getStartdate()==null) saSmpplanitemPojo.setStartdate(new Date());
     if(saSmpplanitemPojo.getPlandate()==null) saSmpplanitemPojo.setPlandate(new Date());
     if(saSmpplanitemPojo.getStartqty()==null) saSmpplanitemPojo.setStartqty(0D);
     if(saSmpplanitemPojo.getFinishqty()==null) saSmpplanitemPojo.setFinishqty(0D);
     if(saSmpplanitemPojo.getMrbqty()==null) saSmpplanitemPojo.setMrbqty(0D);
     if(saSmpplanitemPojo.getStartsecqty()==null) saSmpplanitemPojo.setStartsecqty(0D);
     if(saSmpplanitemPojo.getEnabledmark()==null) saSmpplanitemPojo.setEnabledmark(0);
     if(saSmpplanitemPojo.getClosed()==null) saSmpplanitemPojo.setClosed(0);
     if(saSmpplanitemPojo.getRemark()==null) saSmpplanitemPojo.setRemark("");
     if(saSmpplanitemPojo.getStatecode()==null) saSmpplanitemPojo.setStatecode("");
     if(saSmpplanitemPojo.getStatedate()==null) saSmpplanitemPojo.setStatedate(new Date());
     if(saSmpplanitemPojo.getRownum()==null) saSmpplanitemPojo.setRownum(0);
     if(saSmpplanitemPojo.getCustomer()==null) saSmpplanitemPojo.setCustomer("");
     if(saSmpplanitemPojo.getCustpo()==null) saSmpplanitemPojo.setCustpo("");
     if(saSmpplanitemPojo.getMrpuid()==null) saSmpplanitemPojo.setMrpuid("");
     if(saSmpplanitemPojo.getMrpid()==null) saSmpplanitemPojo.setMrpid("");
     if(saSmpplanitemPojo.getAttributejson()==null) saSmpplanitemPojo.setAttributejson("");
     if(saSmpplanitemPojo.getAttributestr()==null) saSmpplanitemPojo.setAttributestr("");
     if(saSmpplanitemPojo.getStartrate()==null) saSmpplanitemPojo.setStartrate(0D);
     if(saSmpplanitemPojo.getFinishrate()==null) saSmpplanitemPojo.setFinishrate(0D);
     if(saSmpplanitemPojo.getWkwpid()==null) saSmpplanitemPojo.setWkwpid("");
     if(saSmpplanitemPojo.getWkwpcode()==null) saSmpplanitemPojo.setWkwpcode("");
     if(saSmpplanitemPojo.getWkwpname()==null) saSmpplanitemPojo.setWkwpname("");
     if(saSmpplanitemPojo.getCustom1()==null) saSmpplanitemPojo.setCustom1("");
     if(saSmpplanitemPojo.getCustom2()==null) saSmpplanitemPojo.setCustom2("");
     if(saSmpplanitemPojo.getCustom3()==null) saSmpplanitemPojo.setCustom3("");
     if(saSmpplanitemPojo.getCustom4()==null) saSmpplanitemPojo.setCustom4("");
     if(saSmpplanitemPojo.getCustom5()==null) saSmpplanitemPojo.setCustom5("");
     if(saSmpplanitemPojo.getCustom6()==null) saSmpplanitemPojo.setCustom6("");
     if(saSmpplanitemPojo.getCustom7()==null) saSmpplanitemPojo.setCustom7("");
     if(saSmpplanitemPojo.getCustom8()==null) saSmpplanitemPojo.setCustom8("");
     if(saSmpplanitemPojo.getCustom9()==null) saSmpplanitemPojo.setCustom9("");
     if(saSmpplanitemPojo.getCustom10()==null) saSmpplanitemPojo.setCustom10("");
     if(saSmpplanitemPojo.getTenantid()==null) saSmpplanitemPojo.setTenantid("");
     if(saSmpplanitemPojo.getRevision()==null) saSmpplanitemPojo.setRevision(0);
     return saSmpplanitemPojo;
     }
}
