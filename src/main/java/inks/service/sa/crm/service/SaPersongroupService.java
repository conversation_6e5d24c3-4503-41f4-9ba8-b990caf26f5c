package inks.service.sa.crm.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.pojo.SaPersongroupPojo;

/**
 * (SaPersongroup)表服务接口
 *
 * <AUTHOR>
 * @since 2023-04-13 13:14:03
 */
public interface SaPersongroupService {


    SaPersongroupPojo getEntity(String key);


    PageInfo<SaPersongroupPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saPersongroupPojo 实例对象
     * @return 实例对象
     */
    SaPersongroupPojo insert(SaPersongroupPojo saPersongroupPojo);

    /**
     * 修改数据
     *
     * @param saPersongrouppojo 实例对象
     * @return 实例对象
     */
    SaPersongroupPojo update(SaPersongroupPojo saPersongrouppojo);


    int delete(String key);
}
