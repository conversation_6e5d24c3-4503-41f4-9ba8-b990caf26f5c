package inks.service.sa.crm.service;

import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.pojo.SaSmpdeliitemPojo;
import inks.service.sa.crm.domain.SaSmpdeliitemEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import com.github.pagehelper.PageInfo;
import java.util.List;
/**
 * 样品发货明细(SaSmpdeliitem)表服务接口
 *
 * <AUTHOR>
 * @since 2024-09-03 16:20:55
 */
public interface SaSmpdeliitemService {


    SaSmpdeliitemPojo getEntity(String key);

    PageInfo<SaSmpdeliitemPojo> getPageList(QueryParam queryParam);

    List<SaSmpdeliitemPojo> getList(String Pid);  

    SaSmpdeliitemPojo insert(SaSmpdeliitemPojo saSmpdeliitemPojo);

    SaSmpdeliitemPojo update(SaSmpdeliitemPojo saSmpdeliitempojo);

    int delete(String key);

    SaSmpdeliitemPojo clearNull(SaSmpdeliitemPojo saSmpdeliitempojo);
}
