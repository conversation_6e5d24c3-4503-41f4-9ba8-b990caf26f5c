package inks.service.sa.crm.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.pojo.SaStagePojo;
import inks.service.sa.crm.domain.pojo.SaStageitemdetailPojo;

/**
 * 阶段(SaStage)表服务接口
 *
 * <AUTHOR>
 * @since 2024-02-20 09:09:25
 */
public interface SaStageService {


    SaStagePojo getEntity(String key);


    PageInfo<SaStageitemdetailPojo> getPageList(QueryParam queryParam);


    SaStagePojo getBillEntity(String key);


    PageInfo<SaStagePojo> getBillList(QueryParam queryParam);


    PageInfo<SaStagePojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saStagePojo 实例对象
     * @return 实例对象
     */
    SaStagePojo insert(SaStagePojo saStagePojo);

    /**
     * 修改数据
     *
     * @param saStagepojo 实例对象
     * @return 实例对象
     */
    SaStagePojo update(SaStagePojo saStagepojo);


    int delete(String key);

}
