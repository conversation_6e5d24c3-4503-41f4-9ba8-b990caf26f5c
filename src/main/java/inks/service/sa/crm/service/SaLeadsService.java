package inks.service.sa.crm.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.pojo.SaLeadsPojo;

/**
 * 线索管理(SaLeads)表服务接口
 *
 * <AUTHOR>
 * @since 2023-03-22 16:13:31
 */
public interface SaLeadsService {


    SaLeadsPojo getEntity(String key);


    PageInfo<SaLeadsPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saLeadsPojo 实例对象
     * @return 实例对象
     */
    SaLeadsPojo insert(SaLeadsPojo saLeadsPojo, LoginUser loginUser);

    /**
     * 修改数据
     *
     * @param saLeadspojo 实例对象
     * @return 实例对象
     */
    SaLeadsPojo update(SaLeadsPojo saLeadspojo, LoginUser loginUser);


    int delete(String key, LoginUser loginUser);

    SaLeadsPojo getEntityByCustname(String custname);
}
