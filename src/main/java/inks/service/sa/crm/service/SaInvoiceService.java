package inks.service.sa.crm.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.pojo.SaInvoicePojo;

/**
 * 发票管理(SaInvoice)表服务接口
 *
 * <AUTHOR>
 * @since 2023-03-25 14:11:06
 */
public interface SaInvoiceService {


    SaInvoicePojo getEntity(String key);


    PageInfo<SaInvoicePojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saInvoicePojo 实例对象
     * @return 实例对象
     */
    SaInvoicePojo insert(SaInvoicePojo saInvoicePojo);

    /**
     * 修改数据
     *
     * @param saInvoicepojo 实例对象
     * @return 实例对象
     */
    SaInvoicePojo update(SaInvoicePojo saInvoicepojo);


    int delete(String key);

    /**
     * 审核数据
     *
     * @param saInvoicePojo 实例对象
     * @return 实例对象
     */
    SaInvoicePojo approval(SaInvoicePojo saInvoicePojo);
}
