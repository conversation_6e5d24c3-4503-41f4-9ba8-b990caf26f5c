package inks.service.sa.crm.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.crm.domain.SaContractEntity;
import inks.service.sa.crm.domain.SaContractitemEntity;
import inks.service.sa.crm.domain.pojo.SaContractPojo;
import inks.service.sa.crm.domain.pojo.SaContractitemPojo;
import inks.service.sa.crm.domain.pojo.SaContractitemdetailPojo;
import inks.service.sa.crm.domain.pojo.SaFollowviewPojo;
import inks.service.sa.crm.mapper.SaContractMapper;
import inks.service.sa.crm.mapper.SaContractitemMapper;
import inks.service.sa.crm.service.SaContractService;
import inks.service.sa.crm.service.SaContractitemService;
import inks.service.sa.crm.service.SaFollowviewService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 合同管理(SaContract)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-04-14 14:41:28
 */
@Service("saContractService")
public class SaContractServiceImpl implements SaContractService {
    @Resource
    private SaContractMapper saContractMapper;

    @Resource
    private SaContractitemMapper saContractitemMapper;

    /**
     * 服务对象Item
     */
    @Resource
    private SaContractitemService saContractitemService;
    @Autowired
    private SaFollowviewService saFollowviewService;

    private static void cleanNull(SaContractPojo saContractPojo) {
        if (saContractPojo.getGengroupid() == null) saContractPojo.setGengroupid("");
        if (saContractPojo.getRefno() == null) saContractPojo.setRefno("");
        if (saContractPojo.getBilltype() == null) saContractPojo.setBilltype("");
        if (saContractPojo.getBilltitle() == null) saContractPojo.setBilltitle("");
        if (saContractPojo.getBilldate() == null) saContractPojo.setBilldate(new Date());
        if (saContractPojo.getBilltaxamount() == null) saContractPojo.setBilltaxamount(0D);
        if (saContractPojo.getGroupid() == null) saContractPojo.setGroupid("");
        if (saContractPojo.getBusinessid() == null) saContractPojo.setBusinessid("");
        if (saContractPojo.getCustcontractor() == null) saContractPojo.setCustcontractor("");
        if (saContractPojo.getCompcontractor() == null) saContractPojo.setCompcontractor("");
        if (saContractPojo.getAmount() == null) saContractPojo.setAmount(0D);
        if (saContractPojo.getContractsigndate() == null) saContractPojo.setContractsigndate(new Date());
        if (saContractPojo.getRenewalmark() == null) saContractPojo.setRenewalmark(0);
        if (saContractPojo.getRenewaltype() == null) saContractPojo.setRenewaltype(0);
        if (saContractPojo.getContractexpiredate() == null) saContractPojo.setContractexpiredate(new Date());
        if (saContractPojo.getCitetype() == null) saContractPojo.setCitetype("");
        if (saContractPojo.getCiteuid() == null) saContractPojo.setCiteuid("");
        if (saContractPojo.getCiteid() == null) saContractPojo.setCiteid("");
        if (saContractPojo.getLinkman() == null) saContractPojo.setLinkman("");
        if (saContractPojo.getCustadd() == null) saContractPojo.setCustadd("");
        if (saContractPojo.getOrdertime() == null) saContractPojo.setOrdertime(new Date());
        if (saContractPojo.getDeliverydate() == null) saContractPojo.setDeliverydate(new Date());
        if (saContractPojo.getCharge() == null) saContractPojo.setCharge("");
        if (saContractPojo.getChargedept() == null) saContractPojo.setChargedept("");
        if (saContractPojo.getOperatorid() == null) saContractPojo.setOperatorid("");
        if (saContractPojo.getOperator() == null) saContractPojo.setOperator("");
        if (saContractPojo.getEnabledmark() == null) saContractPojo.setEnabledmark(0);
        if(saContractPojo.getBillpaid()==null) saContractPojo.setBillpaid(0D);
        if (saContractPojo.getRownum() == null) saContractPojo.setRownum(0);
        if (saContractPojo.getSummary() == null) saContractPojo.setSummary("");
        if (saContractPojo.getCreateby() == null) saContractPojo.setCreateby("");
        if (saContractPojo.getCreatebyid() == null) saContractPojo.setCreatebyid("");
        if (saContractPojo.getCreatedate() == null) saContractPojo.setCreatedate(new Date());
        if (saContractPojo.getLister() == null) saContractPojo.setLister("");
        if (saContractPojo.getListerid() == null) saContractPojo.setListerid("");
        if (saContractPojo.getModifydate() == null) saContractPojo.setModifydate(new Date());
        if (saContractPojo.getAssessor() == null) saContractPojo.setAssessor("");
        if (saContractPojo.getAssessorid() == null) saContractPojo.setAssessorid("");
        if (saContractPojo.getAssessdate() == null) saContractPojo.setAssessdate(new Date());
        if (saContractPojo.getPrincipalid() == null) saContractPojo.setPrincipalid("");
        if (saContractPojo.getPrincipal() == null) saContractPojo.setPrincipal("");
        if (saContractPojo.getLastfollowdate() == null) saContractPojo.setLastfollowdate(new Date());
        if (saContractPojo.getCustom1() == null) saContractPojo.setCustom1("");
        if (saContractPojo.getCustom2() == null) saContractPojo.setCustom2("");
        if (saContractPojo.getCustom3() == null) saContractPojo.setCustom3("");
        if (saContractPojo.getCustom4() == null) saContractPojo.setCustom4("");
        if (saContractPojo.getCustom5() == null) saContractPojo.setCustom5("");
        if (saContractPojo.getCustom6() == null) saContractPojo.setCustom6("");
        if (saContractPojo.getCustom7() == null) saContractPojo.setCustom7("");
        if (saContractPojo.getCustom8() == null) saContractPojo.setCustom8("");
        if (saContractPojo.getCustom9() == null) saContractPojo.setCustom9("");
        if (saContractPojo.getCustom10() == null) saContractPojo.setCustom10("");
        if (saContractPojo.getDeptid() == null) saContractPojo.setDeptid("");
        if (saContractPojo.getDeptname() == null) saContractPojo.setDeptname("");
        if (saContractPojo.getTenantid() == null) saContractPojo.setTenantid("");
        if (saContractPojo.getTenantname() == null) saContractPojo.setTenantname("");
        if (saContractPojo.getRevision() == null) saContractPojo.setRevision(0);
    }

    @Override
    public SaContractPojo getEntity(String key) {
        return this.saContractMapper.getEntity(key);
    }

    @Override
    public PageInfo<SaContractitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaContractitemdetailPojo> lst = saContractMapper.getPageList(queryParam);
            PageInfo<SaContractitemdetailPojo> pageInfo = new PageInfo<SaContractitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public SaContractPojo getBillEntity(String key) {
        try {
            //读取主表
            SaContractPojo saContractPojo = this.saContractMapper.getEntity(key);
            //读取子表
            saContractPojo.setItem(saContractitemMapper.getList(saContractPojo.getId()));
            return saContractPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public PageInfo<SaContractPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaContractPojo> lst = saContractMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(saContractitemMapper.getList(lst.get(i).getId()));
            }
            PageInfo<SaContractPojo> pageInfo = new PageInfo<SaContractPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public PageInfo<SaContractPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaContractPojo> lst = saContractMapper.getPageTh(queryParam);
            PageInfo<SaContractPojo> pageInfo = new PageInfo<SaContractPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saContractPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public SaContractPojo insert(SaContractPojo saContractPojo, LoginUser loginUser) {
//初始化NULL字段
        cleanNull(saContractPojo);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        SaContractEntity saContractEntity = new SaContractEntity();
        BeanUtils.copyProperties(saContractPojo, saContractEntity);
        //设置id和新建日期
        saContractEntity.setId(id);
        saContractEntity.setRevision(1);  //乐观锁
        //插入主表
        this.saContractMapper.insert(saContractEntity);
        //Item子表处理
        List<SaContractitemPojo> lst = saContractPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (SaContractitemPojo saContractitemPojo : lst) {
                //初始化item的NULL
                SaContractitemPojo itemPojo = this.saContractitemService.clearNull(saContractitemPojo);
                SaContractitemEntity saContractitemEntity = new SaContractitemEntity();
                BeanUtils.copyProperties(itemPojo, saContractitemEntity);
                //设置id和Pid
                saContractitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                saContractitemEntity.setPid(id);
                saContractitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.saContractitemMapper.insert(saContractitemEntity);
            }
        }

        // 如果有商机id，同步合同金额到商机表
        String businessid = saContractPojo.getBusinessid();
        if (StringUtils.isNotBlank(businessid)) {
            saContractMapper.syncBussinessContractAmount(businessid);
        }

        // 创建客户跟进记录 IsAuto=1 为自动生成
        SaContractPojo billPojo = this.getEntity(saContractEntity.getId());
        String itemContent = loginUser.getRealname() + "创建了合同【" + billPojo.getRefno() + billPojo.getBilltitle() + "】";
        SaFollowviewPojo buildFollowview = SaFollowviewPojo.buildFollowview(billPojo.getGroupid(),
                "Customer",
                billPojo.getCustname(),
                "Contract.insert",
                itemContent,
                loginUser);
        saFollowviewService.insert(buildFollowview);
        //返回Bill实例
        return billPojo;

    }

    /**
     * 修改数据
     *
     * @param saContractPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public SaContractPojo update(SaContractPojo saContractPojo, LoginUser loginUser) {
        //主表更改
        SaContractEntity saContractEntity = new SaContractEntity();
        BeanUtils.copyProperties(saContractPojo, saContractEntity);
        this.saContractMapper.update(saContractEntity);
        if (saContractPojo.getItem() != null) {
            //Item子表处理
            List<SaContractitemPojo> lst = saContractPojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = saContractMapper.getDelItemIds(saContractPojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (String lstDelId : lstDelIds) {
                    this.saContractitemMapper.delete(lstDelId);
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (SaContractitemPojo saContractitemPojo : lst) {
                    SaContractitemEntity saContractitemEntity = new SaContractitemEntity();
                    if ("".equals(saContractitemPojo.getId()) || saContractitemPojo.getId() == null) {
                        //初始化item的NULL
                        SaContractitemPojo itemPojo = this.saContractitemService.clearNull(saContractitemPojo);
                        BeanUtils.copyProperties(itemPojo, saContractitemEntity);
                        //设置id和Pid
                        saContractitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        saContractitemEntity.setPid(saContractEntity.getId());  // 主表 id
                        saContractitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.saContractitemMapper.insert(saContractitemEntity);
                    } else {
                        BeanUtils.copyProperties(saContractitemPojo, saContractitemEntity);
                        this.saContractitemMapper.update(saContractitemEntity);
                    }
                }
            }
        }
        // 如果有商机id，同步合同金额到商机表
        String businessid = saContractPojo.getBusinessid();
        if (StringUtils.isNotBlank(businessid)) {
            saContractMapper.syncBussinessContractAmount(businessid);
        }

        // 4. 构建并插入变更日志
        // 4.1 取出更新前的旧数据
        SaContractPojo old = this.getEntity(saContractEntity.getId());

        StringBuilder content = new StringBuilder();
        String name = loginUser.getRealname();

        // 对比每个可能要记录的字段
        if (StringUtils.isNotBlank(saContractPojo.getBilltitle())
                && !saContractPojo.getBilltitle().equals(old.getBilltitle())) {
            content.append(name).append("修改了标题为:").append(saContractPojo.getBilltitle()).append("；");
        }
        if (saContractPojo.getBilldate() != null
                && !saContractPojo.getBilldate().equals(old.getBilldate())) {
            content.append(name).append("修改了日期为:").append(saContractPojo.getBilldate()).append("；");
        }
        if (saContractPojo.getBilltaxamount() != null
                && !saContractPojo.getBilltaxamount().equals(old.getBilltaxamount())) {
            content.append(name).append("修改了含税金额为:").append(saContractPojo.getBilltaxamount()).append("；");
        }
        if (saContractPojo.getAmount() != null
                && !saContractPojo.getAmount().equals(old.getAmount())) {
            content.append(name).append("修改了合同金额为:").append(saContractPojo.getAmount()).append("；");
        }
        if (StringUtils.isNotBlank(saContractPojo.getCustcontractor())
                && !saContractPojo.getCustcontractor().equals(old.getCustcontractor())) {
            content.append(name).append("修改了客户签约人为:").append(saContractPojo.getCustcontractor()).append("；");
        }
        if (StringUtils.isNotBlank(saContractPojo.getCompcontractor())
                && !saContractPojo.getCompcontractor().equals(old.getCompcontractor())) {
            content.append(name).append("修改了公司签约人为:").append(saContractPojo.getCompcontractor()).append("；");
        }
        if (saContractPojo.getContractsigndate() != null
                && !saContractPojo.getContractsigndate().equals(old.getContractsigndate())) {
            content.append(name).append("修改了签订时间为:").append(saContractPojo.getContractsigndate()).append("；");
        }
        if (saContractPojo.getContractexpiredate() != null
                && !saContractPojo.getContractexpiredate().equals(old.getContractexpiredate())) {
            content.append(name).append("修改了过期时间为:").append(saContractPojo.getContractexpiredate()).append("；");
        }
        if (saContractPojo.getRenewalmark() != null
                && !saContractPojo.getRenewalmark().equals(old.getRenewalmark())) {
            content.append(name).append("修改了续约标识为:").append(saContractPojo.getRenewalmark()).append("；");
        }
        if (saContractPojo.getRenewaltype() != null
                && !saContractPojo.getRenewaltype().equals(old.getRenewaltype())) {
            content.append(name).append("修改了续约结果类型为:").append(saContractPojo.getRenewaltype()).append("；");
        }
        if (StringUtils.isNotBlank(saContractPojo.getLinkman())
                && !saContractPojo.getLinkman().equals(old.getLinkman())) {
            content.append(name).append("修改了联系人为:").append(saContractPojo.getLinkman()).append("；");
        }
        if (StringUtils.isNotBlank(saContractPojo.getCustadd())
                && !saContractPojo.getCustadd().equals(old.getCustadd())) {
            content.append(name).append("修改了地址为:").append(saContractPojo.getCustadd()).append("；");
        }
        if (StringUtils.isNotBlank(saContractPojo.getCharge())
                && !saContractPojo.getCharge().equals(old.getCharge())) {
            content.append(name).append("修改了负责人为:").append(saContractPojo.getCharge()).append("；");
        }
        if (StringUtils.isNotBlank(saContractPojo.getChargedept())
                && !saContractPojo.getChargedept().equals(old.getChargedept())) {
            content.append(name).append("修改了负责人部门为:").append(saContractPojo.getChargedept()).append("；");
        }
        if (saContractPojo.getOperator()!=null
                && !saContractPojo.getOperator().equals(old.getOperator())) {
            content.append(name).append("修改了经办人为:").append(saContractPojo.getOperator()).append("；");
        }
        if (saContractPojo.getEnabledmark() != null
                && !saContractPojo.getEnabledmark().equals(old.getEnabledmark())) {
            content.append(name).append("修改了状态为:").append(saContractPojo.getEnabledmark()).append("；");
        }    // …其它字段按需添加

        if (content.length() > 0) {
            SaFollowviewPojo follow = SaFollowviewPojo.buildFollowview(
                    old.getGroupid(),
                    "Customer",
                    old.getCustname(),
                    "Contract.update",
                    content.toString(),
                    loginUser
            );
            saFollowviewService.insert(follow);
        }
        //返回Bill实例
        return this.getBillEntity(saContractEntity.getId());
    }

    @Override
    @Transactional
    public int delete(String key, LoginUser loginUser) {
        SaContractPojo saContractPojo = this.getBillEntity(key);
        //Item子表处理
        List<SaContractitemPojo> lst = saContractPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (SaContractitemPojo saContractitemPojo : lst) {
                this.saContractitemMapper.delete(saContractitemPojo.getId());
            }
        }
        // 创建客户跟进记录 IsAuto=1 为自动生成
        String itemContent = loginUser.getRealname() + "删除了合同【" + saContractPojo.getRefno() + saContractPojo.getBilltitle() + "】";
        SaFollowviewPojo buildFollowview = SaFollowviewPojo.buildFollowview(saContractPojo.getGroupid(),
                "Customer",
                saContractPojo.getCustname(),
                "Contract.delete",
                itemContent,
                loginUser);
        saFollowviewService.insert(buildFollowview);
        this.saContractMapper.delete(key);
        // 如果有商机id，同步合同金额到商机表
        String businessid = saContractPojo.getBusinessid();
        if (StringUtils.isNotBlank(businessid)) {
            saContractMapper.syncBussinessContractAmount(businessid);
        }
        return 1;
    }

    /**
     * 审核数据
     *
     * @param saContractPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public SaContractPojo approval(SaContractPojo saContractPojo) {
        //主表更改
        SaContractEntity saContractEntity = new SaContractEntity();
        BeanUtils.copyProperties(saContractPojo, saContractEntity);
        this.saContractMapper.approval(saContractEntity);
        //返回Bill实例
        return this.getBillEntity(saContractEntity.getId());
    }

}
