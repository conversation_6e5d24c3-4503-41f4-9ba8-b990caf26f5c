package inks.service.sa.crm.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.pojo.SaContractitemPojo;

import java.util.List;

/**
 * 合同项目(SaContractitem)表服务接口
 *
 * <AUTHOR>
 * @since 2023-03-06 11:18:29
 */
public interface SaContractitemService {


    SaContractitemPojo getEntity(String key);


    PageInfo<SaContractitemPojo> getPageList(QueryParam queryParam);

    List<SaContractitemPojo> getList(String Pid);

    /**
     * 新增数据
     *
     * @param saContractitemPojo 实例对象
     * @return 实例对象
     */
    SaContractitemPojo insert(SaContractitemPojo saContractitemPojo);

    /**
     * 修改数据
     *
     * @param saContractitempojo 实例对象
     * @return 实例对象
     */
    SaContractitemPojo update(SaContractitemPojo saContractitempojo);


    int delete(String key);

    /**
     * 修改数据
     *
     * @param saContractitempojo 实例对象
     * @return 实例对象
     */
    SaContractitemPojo clearNull(SaContractitemPojo saContractitempojo);
}
