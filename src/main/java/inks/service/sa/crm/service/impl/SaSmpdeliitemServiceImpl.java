package inks.service.sa.crm.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.crm.domain.pojo.SaSmpdeliitemPojo;
import inks.service.sa.crm.domain.SaSmpdeliitemEntity;
import inks.service.sa.crm.mapper.SaSmpdeliitemMapper;
import inks.service.sa.crm.service.SaSmpdeliitemService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import inks.common.core.text.inksSnowflake;
/**
 * 样品发货明细(SaSmpdeliitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-09-03 16:20:55
 */
@Service("saSmpdeliitemService")
public class SaSmpdeliitemServiceImpl implements SaSmpdeliitemService {
    @Resource
    private SaSmpdeliitemMapper saSmpdeliitemMapper;

    @Override
    public SaSmpdeliitemPojo getEntity(String key) {
        return this.saSmpdeliitemMapper.getEntity(key);
    }

    @Override
    public PageInfo<SaSmpdeliitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaSmpdeliitemPojo> lst = saSmpdeliitemMapper.getPageList(queryParam);
            PageInfo<SaSmpdeliitemPojo> pageInfo = new PageInfo<SaSmpdeliitemPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    @Override
    public List<SaSmpdeliitemPojo> getList(String Pid) { 
        try {
            List<SaSmpdeliitemPojo> lst = saSmpdeliitemMapper.getList(Pid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }      

    @Override
    public SaSmpdeliitemPojo insert(SaSmpdeliitemPojo saSmpdeliitemPojo) {
        //初始化item的NULL
        SaSmpdeliitemPojo itempojo =this.clearNull(saSmpdeliitemPojo);
        SaSmpdeliitemEntity saSmpdeliitemEntity = new SaSmpdeliitemEntity(); 
        BeanUtils.copyProperties(itempojo,saSmpdeliitemEntity);
         //生成雪花id
          saSmpdeliitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          saSmpdeliitemEntity.setRevision(1);  //乐观锁      
          this.saSmpdeliitemMapper.insert(saSmpdeliitemEntity);
        return this.getEntity(saSmpdeliitemEntity.getId());
  
    }

    @Override
    public SaSmpdeliitemPojo update(SaSmpdeliitemPojo saSmpdeliitemPojo) {
        SaSmpdeliitemEntity saSmpdeliitemEntity = new SaSmpdeliitemEntity(); 
        BeanUtils.copyProperties(saSmpdeliitemPojo,saSmpdeliitemEntity);
        this.saSmpdeliitemMapper.update(saSmpdeliitemEntity);
        return this.getEntity(saSmpdeliitemEntity.getId());
    }

    @Override
    public int delete(String key) {
        return this.saSmpdeliitemMapper.delete(key) ;
    }

     @Override
     public SaSmpdeliitemPojo clearNull(SaSmpdeliitemPojo saSmpdeliitemPojo){
     //初始化NULL字段
     if(saSmpdeliitemPojo.getPid()==null) saSmpdeliitemPojo.setPid("");
     if(saSmpdeliitemPojo.getGoodsid()==null) saSmpdeliitemPojo.setGoodsid("");
     if(saSmpdeliitemPojo.getItemcode()==null) saSmpdeliitemPojo.setItemcode("");
     if(saSmpdeliitemPojo.getItemname()==null) saSmpdeliitemPojo.setItemname("");
     if(saSmpdeliitemPojo.getItemspec()==null) saSmpdeliitemPojo.setItemspec("");
     if(saSmpdeliitemPojo.getItemunit()==null) saSmpdeliitemPojo.setItemunit("");
     if(saSmpdeliitemPojo.getQuantity()==null) saSmpdeliitemPojo.setQuantity(0D);
     if(saSmpdeliitemPojo.getTaxprice()==null) saSmpdeliitemPojo.setTaxprice(0D);
     if(saSmpdeliitemPojo.getTaxamount()==null) saSmpdeliitemPojo.setTaxamount(0D);
     if(saSmpdeliitemPojo.getPrice()==null) saSmpdeliitemPojo.setPrice(0D);
     if(saSmpdeliitemPojo.getAmount()==null) saSmpdeliitemPojo.setAmount(0D);
     if(saSmpdeliitemPojo.getItemtaxrate()==null) saSmpdeliitemPojo.setItemtaxrate(0);
     if(saSmpdeliitemPojo.getTaxtotal()==null) saSmpdeliitemPojo.setTaxtotal(0D);
     if(saSmpdeliitemPojo.getRebate()==null) saSmpdeliitemPojo.setRebate(0);
     if(saSmpdeliitemPojo.getFinishqty()==null) saSmpdeliitemPojo.setFinishqty(0D);
     if(saSmpdeliitemPojo.getRownum()==null) saSmpdeliitemPojo.setRownum(0);
     if(saSmpdeliitemPojo.getAttributejson()==null) saSmpdeliitemPojo.setAttributejson("");
     if(saSmpdeliitemPojo.getAttributestr()==null) saSmpdeliitemPojo.setAttributestr("");
     if(saSmpdeliitemPojo.getRemark()==null) saSmpdeliitemPojo.setRemark("");
     if(saSmpdeliitemPojo.getCiteuid()==null) saSmpdeliitemPojo.setCiteuid("");
     if(saSmpdeliitemPojo.getCiteitemid()==null) saSmpdeliitemPojo.setCiteitemid("");
     if(saSmpdeliitemPojo.getVirtualitem()==null) saSmpdeliitemPojo.setVirtualitem(0);
     if(saSmpdeliitemPojo.getClosed()==null) saSmpdeliitemPojo.setClosed(0);
     if(saSmpdeliitemPojo.getDisannulmark()==null) saSmpdeliitemPojo.setDisannulmark(0);
     if(saSmpdeliitemPojo.getDisannullisterid()==null) saSmpdeliitemPojo.setDisannullisterid("");
     if(saSmpdeliitemPojo.getDisannullister()==null) saSmpdeliitemPojo.setDisannullister("");
     if(saSmpdeliitemPojo.getDisannuldate()==null) saSmpdeliitemPojo.setDisannuldate(new Date());
     if(saSmpdeliitemPojo.getCustom1()==null) saSmpdeliitemPojo.setCustom1("");
     if(saSmpdeliitemPojo.getCustom2()==null) saSmpdeliitemPojo.setCustom2("");
     if(saSmpdeliitemPojo.getCustom3()==null) saSmpdeliitemPojo.setCustom3("");
     if(saSmpdeliitemPojo.getCustom4()==null) saSmpdeliitemPojo.setCustom4("");
     if(saSmpdeliitemPojo.getCustom5()==null) saSmpdeliitemPojo.setCustom5("");
     if(saSmpdeliitemPojo.getCustom6()==null) saSmpdeliitemPojo.setCustom6("");
     if(saSmpdeliitemPojo.getCustom7()==null) saSmpdeliitemPojo.setCustom7("");
     if(saSmpdeliitemPojo.getCustom8()==null) saSmpdeliitemPojo.setCustom8("");
     if(saSmpdeliitemPojo.getCustom9()==null) saSmpdeliitemPojo.setCustom9("");
     if(saSmpdeliitemPojo.getCustom10()==null) saSmpdeliitemPojo.setCustom10("");
     if(saSmpdeliitemPojo.getTenantid()==null) saSmpdeliitemPojo.setTenantid("");
     if(saSmpdeliitemPojo.getRevision()==null) saSmpdeliitemPojo.setRevision(0);
     return saSmpdeliitemPojo;
     }
}
