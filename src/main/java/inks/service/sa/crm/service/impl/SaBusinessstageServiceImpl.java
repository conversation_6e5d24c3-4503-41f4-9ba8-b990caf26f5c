package inks.service.sa.crm.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.crm.domain.SaBusinessstageEntity;
import inks.service.sa.crm.domain.pojo.SaBusinessstagePojo;
import inks.service.sa.crm.mapper.SaBusinessstageMapper;
import inks.service.sa.crm.service.SaBusinessstageService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 商机阶段子表(SaBusinessstage)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-07-25 16:30:46
 */
@Service("saBusinessstageService")
public class SaBusinessstageServiceImpl implements SaBusinessstageService {
    @Resource
    private SaBusinessstageMapper saBusinessstageMapper;

    @Override
    public SaBusinessstagePojo getEntity(String key) {
        return this.saBusinessstageMapper.getEntity(key);
    }

    @Override
    public PageInfo<SaBusinessstagePojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaBusinessstagePojo> lst = saBusinessstageMapper.getPageList(queryParam);
            PageInfo<SaBusinessstagePojo> pageInfo = new PageInfo<SaBusinessstagePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public List<SaBusinessstagePojo> getList(String Pid) {
        try {
            List<SaBusinessstagePojo> lst = saBusinessstageMapper.getList(Pid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public SaBusinessstagePojo insert(SaBusinessstagePojo saBusinessstagePojo) {
        //初始化item的NULL
        SaBusinessstagePojo itempojo = this.clearNull(saBusinessstagePojo);
        SaBusinessstageEntity saBusinessstageEntity = new SaBusinessstageEntity();
        BeanUtils.copyProperties(itempojo, saBusinessstageEntity);
        //生成雪花id
        saBusinessstageEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saBusinessstageEntity.setRevision(1);  //乐观锁
        this.saBusinessstageMapper.insert(saBusinessstageEntity);
        return this.getEntity(saBusinessstageEntity.getId());

    }

    @Override
    public SaBusinessstagePojo update(SaBusinessstagePojo saBusinessstagePojo) {
        SaBusinessstageEntity saBusinessstageEntity = new SaBusinessstageEntity();
        BeanUtils.copyProperties(saBusinessstagePojo, saBusinessstageEntity);
        this.saBusinessstageMapper.update(saBusinessstageEntity);
        return this.getEntity(saBusinessstageEntity.getId());
    }

    @Override
    public int delete(String key) {
        return this.saBusinessstageMapper.delete(key);
    }

    @Override
    public SaBusinessstagePojo clearNull(SaBusinessstagePojo saBusinessstagePojo) {
        //初始化NULL字段
        if (saBusinessstagePojo.getPid() == null) saBusinessstagePojo.setPid("");
        if (saBusinessstagePojo.getParentid() == null) saBusinessstagePojo.setParentid("");
        if (saBusinessstagePojo.getStageitemid() == null) saBusinessstagePojo.setStageitemid("");
        if (saBusinessstagePojo.getStagename() == null) saBusinessstagePojo.setStagename("");
        if (saBusinessstagePojo.getFinishmark() == null) saBusinessstagePojo.setFinishmark(0);
        if (saBusinessstagePojo.getPlandate() == null) saBusinessstagePojo.setPlandate(new Date());
        if (saBusinessstagePojo.getLister() == null) saBusinessstagePojo.setLister("");
        if (saBusinessstagePojo.getListerid() == null) saBusinessstagePojo.setListerid("");
        if (saBusinessstagePojo.getModifydate() == null) saBusinessstagePojo.setModifydate(new Date());
        if (saBusinessstagePojo.getRemark() == null) saBusinessstagePojo.setRemark("");
        if (saBusinessstagePojo.getRownum() == null) saBusinessstagePojo.setRownum(0);
        if (saBusinessstagePojo.getCustom1() == null) saBusinessstagePojo.setCustom1("");
        if (saBusinessstagePojo.getCustom2() == null) saBusinessstagePojo.setCustom2("");
        if (saBusinessstagePojo.getCustom3() == null) saBusinessstagePojo.setCustom3("");
        if (saBusinessstagePojo.getCustom4() == null) saBusinessstagePojo.setCustom4("");
        if (saBusinessstagePojo.getCustom5() == null) saBusinessstagePojo.setCustom5("");
        if (saBusinessstagePojo.getCustom6() == null) saBusinessstagePojo.setCustom6("");
        if (saBusinessstagePojo.getCustom7() == null) saBusinessstagePojo.setCustom7("");
        if (saBusinessstagePojo.getCustom8() == null) saBusinessstagePojo.setCustom8("");
        if (saBusinessstagePojo.getCustom9() == null) saBusinessstagePojo.setCustom9("");
        if (saBusinessstagePojo.getCustom10() == null) saBusinessstagePojo.setCustom10("");
        if (saBusinessstagePojo.getTenantid() == null) saBusinessstagePojo.setTenantid("");
        if (saBusinessstagePojo.getRevision() == null) saBusinessstagePojo.setRevision(0);
        return saBusinessstagePojo;
    }
}
