package inks.service.sa.crm.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.crm.domain.pojo.SaDemandresearchitemPojo;
import inks.service.sa.crm.domain.SaDemandresearchitemEntity;
import inks.service.sa.crm.mapper.SaDemandresearchitemMapper;
import inks.service.sa.crm.service.SaDemandresearchitemService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import inks.common.core.text.inksSnowflake;
/**
 * 需求调研子表(SaDemandresearchitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-10-11 10:15:16
 */
@Service("saDemandresearchitemService")
public class SaDemandresearchitemServiceImpl implements SaDemandresearchitemService {
    @Resource
    private SaDemandresearchitemMapper saDemandresearchitemMapper;

    @Override
    public SaDemandresearchitemPojo getEntity(String key) {
        return this.saDemandresearchitemMapper.getEntity(key);
    }

    @Override
    public PageInfo<SaDemandresearchitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaDemandresearchitemPojo> lst = saDemandresearchitemMapper.getPageList(queryParam);
            PageInfo<SaDemandresearchitemPojo> pageInfo = new PageInfo<SaDemandresearchitemPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    @Override
    public List<SaDemandresearchitemPojo> getList(String Pid) { 
        try {
            List<SaDemandresearchitemPojo> lst = saDemandresearchitemMapper.getList(Pid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }      

    @Override
    public SaDemandresearchitemPojo insert(SaDemandresearchitemPojo saDemandresearchitemPojo) {
        //初始化item的NULL
        SaDemandresearchitemPojo itempojo =this.clearNull(saDemandresearchitemPojo);
        SaDemandresearchitemEntity saDemandresearchitemEntity = new SaDemandresearchitemEntity(); 
        BeanUtils.copyProperties(itempojo,saDemandresearchitemEntity);
         //生成雪花id
          saDemandresearchitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          saDemandresearchitemEntity.setRevision(1);  //乐观锁      
          this.saDemandresearchitemMapper.insert(saDemandresearchitemEntity);
        return this.getEntity(saDemandresearchitemEntity.getId());
  
    }

    @Override
    public SaDemandresearchitemPojo update(SaDemandresearchitemPojo saDemandresearchitemPojo) {
        SaDemandresearchitemEntity saDemandresearchitemEntity = new SaDemandresearchitemEntity(); 
        BeanUtils.copyProperties(saDemandresearchitemPojo,saDemandresearchitemEntity);
        this.saDemandresearchitemMapper.update(saDemandresearchitemEntity);
        return this.getEntity(saDemandresearchitemEntity.getId());
    }

    @Override
    public int delete(String key) {
        return this.saDemandresearchitemMapper.delete(key) ;
    }

     @Override
     public SaDemandresearchitemPojo clearNull(SaDemandresearchitemPojo saDemandresearchitemPojo){
     //初始化NULL字段
     if(saDemandresearchitemPojo.getPid()==null) saDemandresearchitemPojo.setPid("");
     if(saDemandresearchitemPojo.getDemanddictid()==null) saDemandresearchitemPojo.setDemanddictid("");
     if(saDemandresearchitemPojo.getDescription()==null) saDemandresearchitemPojo.setDescription("");
     if(saDemandresearchitemPojo.getMainmark()==null) saDemandresearchitemPojo.setMainmark(0);
     if(saDemandresearchitemPojo.getLevel()==null) saDemandresearchitemPojo.setLevel(0);
     if(saDemandresearchitemPojo.getRequirementdate()==null) saDemandresearchitemPojo.setRequirementdate(new Date());
     if(saDemandresearchitemPojo.getDifficulty()==null) saDemandresearchitemPojo.setDifficulty(0);
     if(saDemandresearchitemPojo.getSolution()==null) saDemandresearchitemPojo.setSolution("");
     if(saDemandresearchitemPojo.getRownum()==null) saDemandresearchitemPojo.setRownum(0);
     if(saDemandresearchitemPojo.getRemark()==null) saDemandresearchitemPojo.setRemark("");
     if(saDemandresearchitemPojo.getTenantid()==null) saDemandresearchitemPojo.setTenantid("");
     if(saDemandresearchitemPojo.getRevision()==null) saDemandresearchitemPojo.setRevision(0);
     return saDemandresearchitemPojo;
     }
}
