package inks.service.sa.crm.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.crm.config.WarnException;
import inks.service.sa.crm.domain.SaCrmgoodsEntity;
import inks.service.sa.crm.domain.pojo.SaCrmgoodsPojo;
import inks.service.sa.crm.mapper.SaCrmgoodsMapper;
import inks.service.sa.crm.service.SaCrmgoodsService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 产品(SaCrmgoods)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-03-06 13:00:46
 */
@Service("saCrmgoodsService")
public class SaCrmgoodsServiceImpl implements SaCrmgoodsService {
    @Resource
    private SaCrmgoodsMapper saCrmgoodsMapper;


    @Override
    public SaCrmgoodsPojo getEntity(String key) {
        if (StringUtils.isBlank(key)) {
            throw new WarnException("key不能为空");
        }
        if (key.equals("1")) {
            throw new WarnException(666, "自定义异常");
        }
        if (key.equals("2")) {
            throw new BaseBusinessException("系统异常");
        }
        if (key.equals("3")) {
            SaCrmgoodsPojo entity = new SaCrmgoodsPojo();
            entity.setGoodsname("测试");
                throw new WarnException("校验失败",entity);
        }
        if (key.equals("4")) {
            SaCrmgoodsPojo entity = new SaCrmgoodsPojo();
            entity.setGoodsname("测试");
            throw new WarnException(777,"校验失败",entity);
        }
        return this.saCrmgoodsMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaCrmgoodsPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaCrmgoodsPojo> lst = saCrmgoodsMapper.getPageList(queryParam);
            PageInfo<SaCrmgoodsPojo> pageInfo = new PageInfo<SaCrmgoodsPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saCrmgoodsPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaCrmgoodsPojo insert(SaCrmgoodsPojo saCrmgoodsPojo) {
        //初始化NULL字段
        if (saCrmgoodsPojo.getGengroupid() == null) saCrmgoodsPojo.setGengroupid("");
        if (saCrmgoodsPojo.getGoodsuid() == null) saCrmgoodsPojo.setGoodsuid("");
        if (saCrmgoodsPojo.getGoodsname() == null) saCrmgoodsPojo.setGoodsname("");
        if (saCrmgoodsPojo.getGoodsspec() == null) saCrmgoodsPojo.setGoodsspec("");
        if (saCrmgoodsPojo.getGoodsunit() == null) saCrmgoodsPojo.setGoodsunit("");
        if (saCrmgoodsPojo.getGoodsprice() == null) saCrmgoodsPojo.setGoodsprice(0D);
        if (saCrmgoodsPojo.getGoodscost() == null) saCrmgoodsPojo.setGoodscost(0D);
        if (saCrmgoodsPojo.getDesciption() == null) saCrmgoodsPojo.setDesciption("");
        if (saCrmgoodsPojo.getEnabledmark() == null) saCrmgoodsPojo.setEnabledmark(0);
        if (saCrmgoodsPojo.getGoodsphote() == null) saCrmgoodsPojo.setGoodsphote("");
        if (saCrmgoodsPojo.getRownum() == null) saCrmgoodsPojo.setRownum(0);
        if (saCrmgoodsPojo.getRemark() == null) saCrmgoodsPojo.setRemark("");
        if (saCrmgoodsPojo.getCreateby() == null) saCrmgoodsPojo.setCreateby("");
        if (saCrmgoodsPojo.getCreatebyid() == null) saCrmgoodsPojo.setCreatebyid("");
        if (saCrmgoodsPojo.getCreatedate() == null) saCrmgoodsPojo.setCreatedate(new Date());
        if (saCrmgoodsPojo.getLister() == null) saCrmgoodsPojo.setLister("");
        if (saCrmgoodsPojo.getListerid() == null) saCrmgoodsPojo.setListerid("");
        if (saCrmgoodsPojo.getModifydate() == null) saCrmgoodsPojo.setModifydate(new Date());
        if (saCrmgoodsPojo.getCustom1() == null) saCrmgoodsPojo.setCustom1("");
        if (saCrmgoodsPojo.getCustom2() == null) saCrmgoodsPojo.setCustom2("");
        if (saCrmgoodsPojo.getCustom3() == null) saCrmgoodsPojo.setCustom3("");
        if (saCrmgoodsPojo.getCustom4() == null) saCrmgoodsPojo.setCustom4("");
        if (saCrmgoodsPojo.getCustom5() == null) saCrmgoodsPojo.setCustom5("");
        if (saCrmgoodsPojo.getCustom6() == null) saCrmgoodsPojo.setCustom6("");
        if (saCrmgoodsPojo.getCustom7() == null) saCrmgoodsPojo.setCustom7("");
        if (saCrmgoodsPojo.getCustom8() == null) saCrmgoodsPojo.setCustom8("");
        if (saCrmgoodsPojo.getCustom9() == null) saCrmgoodsPojo.setCustom9("");
        if (saCrmgoodsPojo.getCustom10() == null) saCrmgoodsPojo.setCustom10("");
        if (saCrmgoodsPojo.getTenantid() == null) saCrmgoodsPojo.setTenantid("");
        if (saCrmgoodsPojo.getTenantname() == null) saCrmgoodsPojo.setTenantname("");
        if (saCrmgoodsPojo.getRevision() == null) saCrmgoodsPojo.setRevision(0);
        SaCrmgoodsEntity saCrmgoodsEntity = new SaCrmgoodsEntity();
        BeanUtils.copyProperties(saCrmgoodsPojo, saCrmgoodsEntity);

        saCrmgoodsEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saCrmgoodsEntity.setRevision(1);  //乐观锁
        this.saCrmgoodsMapper.insert(saCrmgoodsEntity);
        return this.getEntity(saCrmgoodsEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saCrmgoodsPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaCrmgoodsPojo update(SaCrmgoodsPojo saCrmgoodsPojo) {
        SaCrmgoodsEntity saCrmgoodsEntity = new SaCrmgoodsEntity();
        BeanUtils.copyProperties(saCrmgoodsPojo, saCrmgoodsEntity);
        this.saCrmgoodsMapper.update(saCrmgoodsEntity);
        return this.getEntity(saCrmgoodsEntity.getId());
    }


    @Override
    public int delete(String key) {
        return this.saCrmgoodsMapper.delete(key);
    }


}
