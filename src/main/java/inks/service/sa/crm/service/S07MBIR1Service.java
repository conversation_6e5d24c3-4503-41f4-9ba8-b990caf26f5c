package inks.service.sa.crm.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;

import java.util.List;
import java.util.Map;

public interface S07MBIR1Service {
    Map<String, Object> getNewCountThisMonth(QueryParam queryParam);

    List<Map<String, Object>> getNewCountEveryDay(QueryParam queryParam, Integer trend);

    List<Map<String, Object>> getCustomerType(String qpfilter);

    List<Map<String, Object>> getCustomerClass();

    Map<String, Object> getNewCountAndAmtThisMonth(QueryParam queryParam,String userid);

    PageInfo<Map<String, Object>> getBusinessInfo(QueryParam queryParam);
    
    /**
     * 查询合同统计信息
     * 可指定客户ID或查询所有客户
     */
    Map<String, Object> getContractStatistics(String groupid);
}
