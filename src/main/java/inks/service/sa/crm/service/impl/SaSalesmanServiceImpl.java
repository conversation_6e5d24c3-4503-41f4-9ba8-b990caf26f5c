package inks.service.sa.crm.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.crm.domain.SaSalesmanEntity;
import inks.service.sa.crm.domain.pojo.SaSalesmanPojo;
import inks.service.sa.crm.mapper.SaSalesmanMapper;
import inks.service.sa.crm.service.SaSalesmanService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 业务员(SaSalesman)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-10-31 16:22:15
 */
@Service("saSalesmanService")
public class SaSalesmanServiceImpl implements SaSalesmanService {
    @Resource
    private SaSalesmanMapper saSalesmanMapper;


    @Override
    public SaSalesmanPojo getEntity(String key) {
        return this.saSalesmanMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaSalesmanPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaSalesmanPojo> lst = saSalesmanMapper.getPageList(queryParam);
            PageInfo<SaSalesmanPojo> pageInfo = new PageInfo<SaSalesmanPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saSalesmanPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaSalesmanPojo insert(SaSalesmanPojo saSalesmanPojo) {
        //初始化NULL字段
        if (saSalesmanPojo.getUserid() == null) saSalesmanPojo.setUserid("");
        if (saSalesmanPojo.getGengroupid() == null) saSalesmanPojo.setGengroupid("");
        if (saSalesmanPojo.getSalesmantype() == null) saSalesmanPojo.setSalesmantype("");
        if (saSalesmanPojo.getSalesmancode() == null) saSalesmanPojo.setSalesmancode("");
        if (saSalesmanPojo.getSalesmanname() == null) saSalesmanPojo.setSalesmanname("");
        if (saSalesmanPojo.getEnabledmark() == null) saSalesmanPojo.setEnabledmark(0);
        if (saSalesmanPojo.getEmail() == null) saSalesmanPojo.setEmail("");
        if (saSalesmanPojo.getRownum() == null) saSalesmanPojo.setRownum(0);
        if (saSalesmanPojo.getRemark() == null) saSalesmanPojo.setRemark("");
        if (saSalesmanPojo.getCreateby() == null) saSalesmanPojo.setCreateby("");
        if (saSalesmanPojo.getCreatebyid() == null) saSalesmanPojo.setCreatebyid("");
        if (saSalesmanPojo.getCreatedate() == null) saSalesmanPojo.setCreatedate(new Date());
        if (saSalesmanPojo.getLister() == null) saSalesmanPojo.setLister("");
        if (saSalesmanPojo.getListerid() == null) saSalesmanPojo.setListerid("");
        if (saSalesmanPojo.getModifydate() == null) saSalesmanPojo.setModifydate(new Date());
        if (saSalesmanPojo.getCustom1() == null) saSalesmanPojo.setCustom1("");
        if (saSalesmanPojo.getCustom2() == null) saSalesmanPojo.setCustom2("");
        if (saSalesmanPojo.getCustom3() == null) saSalesmanPojo.setCustom3("");
        if (saSalesmanPojo.getCustom4() == null) saSalesmanPojo.setCustom4("");
        if (saSalesmanPojo.getCustom5() == null) saSalesmanPojo.setCustom5("");
        if (saSalesmanPojo.getCustom6() == null) saSalesmanPojo.setCustom6("");
        if (saSalesmanPojo.getCustom7() == null) saSalesmanPojo.setCustom7("");
        if (saSalesmanPojo.getCustom8() == null) saSalesmanPojo.setCustom8("");
        if (saSalesmanPojo.getCustom9() == null) saSalesmanPojo.setCustom9("");
        if (saSalesmanPojo.getCustom10() == null) saSalesmanPojo.setCustom10("");
        if (saSalesmanPojo.getDeptid() == null) saSalesmanPojo.setDeptid("");
        if (saSalesmanPojo.getTenantid() == null) saSalesmanPojo.setTenantid("");
        if (saSalesmanPojo.getRevision() == null) saSalesmanPojo.setRevision(0);
        SaSalesmanEntity saSalesmanEntity = new SaSalesmanEntity();
        BeanUtils.copyProperties(saSalesmanPojo, saSalesmanEntity);
        //生成雪花id
        saSalesmanEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saSalesmanEntity.setRevision(1);  //乐观锁
        this.saSalesmanMapper.insert(saSalesmanEntity);
        return this.getEntity(saSalesmanEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saSalesmanPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaSalesmanPojo update(SaSalesmanPojo saSalesmanPojo) {
        SaSalesmanEntity saSalesmanEntity = new SaSalesmanEntity();
        BeanUtils.copyProperties(saSalesmanPojo, saSalesmanEntity);
        this.saSalesmanMapper.update(saSalesmanEntity);
        return this.getEntity(saSalesmanEntity.getId());
    }


    @Override
    public int delete(String key) {
        return this.saSalesmanMapper.delete(key);
    }


}
