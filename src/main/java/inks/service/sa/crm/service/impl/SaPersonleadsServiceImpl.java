package inks.service.sa.crm.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.crm.domain.SaPersonleadsEntity;
import inks.service.sa.crm.domain.pojo.SaPersonleadsPojo;
import inks.service.sa.crm.mapper.SaPersonleadsMapper;
import inks.service.sa.crm.service.SaPersonleadsService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * (SaPersonleads)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-04-13 13:14:03
 */
@Service("saPersonleadsService")
public class SaPersonleadsServiceImpl implements SaPersonleadsService {
    @Resource
    private SaPersonleadsMapper saPersonleadsMapper;


    @Override
    public SaPersonleadsPojo getEntity(String key) {
        return this.saPersonleadsMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaPersonleadsPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaPersonleadsPojo> lst = saPersonleadsMapper.getPageList(queryParam);
            PageInfo<SaPersonleadsPojo> pageInfo = new PageInfo<SaPersonleadsPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saPersonleadsPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaPersonleadsPojo insert(SaPersonleadsPojo saPersonleadsPojo) {
        //初始化NULL字段
        if (saPersonleadsPojo.getPersonid() == null) saPersonleadsPojo.setPersonid("");
        if (saPersonleadsPojo.getLeadsid() == null) saPersonleadsPojo.setLeadsid("");
        if (saPersonleadsPojo.getRownum() == null) saPersonleadsPojo.setRownum(0);
        if (saPersonleadsPojo.getRemark() == null) saPersonleadsPojo.setRemark("");
        if (saPersonleadsPojo.getCreateby() == null) saPersonleadsPojo.setCreateby("");
        if (saPersonleadsPojo.getCreatebyid() == null) saPersonleadsPojo.setCreatebyid("");
        if (saPersonleadsPojo.getCreatedate() == null) saPersonleadsPojo.setCreatedate(new Date());
        if (saPersonleadsPojo.getLister() == null) saPersonleadsPojo.setLister("");
        if (saPersonleadsPojo.getListerid() == null) saPersonleadsPojo.setListerid("");
        if (saPersonleadsPojo.getModifydate() == null) saPersonleadsPojo.setModifydate(new Date());
        if (saPersonleadsPojo.getCustom1() == null) saPersonleadsPojo.setCustom1("");
        if (saPersonleadsPojo.getCustom2() == null) saPersonleadsPojo.setCustom2("");
        if (saPersonleadsPojo.getCustom3() == null) saPersonleadsPojo.setCustom3("");
        if (saPersonleadsPojo.getCustom4() == null) saPersonleadsPojo.setCustom4("");
        if (saPersonleadsPojo.getCustom5() == null) saPersonleadsPojo.setCustom5("");
        if (saPersonleadsPojo.getTenantid() == null) saPersonleadsPojo.setTenantid("");
        if (saPersonleadsPojo.getTenantname() == null) saPersonleadsPojo.setTenantname("");
        if (saPersonleadsPojo.getRevision() == null) saPersonleadsPojo.setRevision(0);
        SaPersonleadsEntity saPersonleadsEntity = new SaPersonleadsEntity();
        BeanUtils.copyProperties(saPersonleadsPojo, saPersonleadsEntity);
        //生成雪花id
        saPersonleadsEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saPersonleadsEntity.setRevision(1);  //乐观锁
        this.saPersonleadsMapper.insert(saPersonleadsEntity);
        return this.getEntity(saPersonleadsEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saPersonleadsPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaPersonleadsPojo update(SaPersonleadsPojo saPersonleadsPojo) {
        SaPersonleadsEntity saPersonleadsEntity = new SaPersonleadsEntity();
        BeanUtils.copyProperties(saPersonleadsPojo, saPersonleadsEntity);
        this.saPersonleadsMapper.update(saPersonleadsEntity);
        return this.getEntity(saPersonleadsEntity.getId());
    }


    @Override
    public int delete(String key) {
        return this.saPersonleadsMapper.delete(key);
    }


}
