package inks.service.sa.crm.service;

import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.pojo.SaFollowlabelPojo;
import inks.service.sa.crm.domain.SaFollowlabelEntity;

import com.github.pagehelper.PageInfo;

/**
 * 跟进标签(SaFollowlabel)表服务接口
 *
 * <AUTHOR>
 * @since 2025-04-24 09:39:51
 */
public interface SaFollowlabelService {


    SaFollowlabelPojo getEntity(String key);

    PageInfo<SaFollowlabelPojo> getPageList(QueryParam queryParam);

    SaFollowlabelPojo insert(SaFollowlabelPojo saFollowlabelPojo);

    SaFollowlabelPojo update(SaFollowlabelPojo saFollowlabelpojo);

    int delete(String key);
}
