package inks.service.sa.crm.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.crm.domain.SaLeadsstateEntity;
import inks.service.sa.crm.domain.pojo.SaLeadsstatePojo;
import inks.service.sa.crm.mapper.SaLeadsstateMapper;
import inks.service.sa.crm.service.SaLeadsstateService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * (SaLeadsstate)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-04-13 14:45:06
 */
@Service("saLeadsstateService")
public class SaLeadsstateServiceImpl implements SaLeadsstateService {
    @Resource
    private SaLeadsstateMapper saLeadsstateMapper;


    @Override
    public SaLeadsstatePojo getEntity(String key) {
        return this.saLeadsstateMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaLeadsstatePojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaLeadsstatePojo> lst = saLeadsstateMapper.getPageList(queryParam);
            PageInfo<SaLeadsstatePojo> pageInfo = new PageInfo<SaLeadsstatePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saLeadsstatePojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaLeadsstatePojo insert(SaLeadsstatePojo saLeadsstatePojo) {
        //初始化NULL字段
        if (saLeadsstatePojo.getStatename() == null) saLeadsstatePojo.setStatename("");
        if (saLeadsstatePojo.getStatecode() == null) saLeadsstatePojo.setStatecode("");
        if (saLeadsstatePojo.getStatecolor() == null) saLeadsstatePojo.setStatecolor("");
        if (saLeadsstatePojo.getStateicon() == null) saLeadsstatePojo.setStateicon("");
        if (saLeadsstatePojo.getRownum() == null) saLeadsstatePojo.setRownum(0);
        if (saLeadsstatePojo.getCreatebyid() == null) saLeadsstatePojo.setCreatebyid("");
        if (saLeadsstatePojo.getCreateby() == null) saLeadsstatePojo.setCreateby("");
        if (saLeadsstatePojo.getCreatedate() == null) saLeadsstatePojo.setCreatedate(new Date());
        if (saLeadsstatePojo.getListerid() == null) saLeadsstatePojo.setListerid("");
        if (saLeadsstatePojo.getLister() == null) saLeadsstatePojo.setLister("");
        if (saLeadsstatePojo.getModifydate() == null) saLeadsstatePojo.setModifydate(new Date());
        if (saLeadsstatePojo.getModulecode() == null) saLeadsstatePojo.setModulecode("");
        if (saLeadsstatePojo.getCustom1() == null) saLeadsstatePojo.setCustom1("");
        if (saLeadsstatePojo.getCustom2() == null) saLeadsstatePojo.setCustom2("");
        if (saLeadsstatePojo.getCustom3() == null) saLeadsstatePojo.setCustom3("");
        if (saLeadsstatePojo.getCustom4() == null) saLeadsstatePojo.setCustom4("");
        if (saLeadsstatePojo.getCustom5() == null) saLeadsstatePojo.setCustom5("");
        if (saLeadsstatePojo.getTenantid() == null) saLeadsstatePojo.setTenantid("");
        if (saLeadsstatePojo.getDeptid() == null) saLeadsstatePojo.setDeptid("");
        if (saLeadsstatePojo.getTenantname() == null) saLeadsstatePojo.setTenantname("");
        if (saLeadsstatePojo.getRevision() == null) saLeadsstatePojo.setRevision(0);
        SaLeadsstateEntity saLeadsstateEntity = new SaLeadsstateEntity();
        BeanUtils.copyProperties(saLeadsstatePojo, saLeadsstateEntity);
        //生成雪花id
        saLeadsstateEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saLeadsstateEntity.setRevision(1);  //乐观锁
        this.saLeadsstateMapper.insert(saLeadsstateEntity);
        return this.getEntity(saLeadsstateEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saLeadsstatePojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaLeadsstatePojo update(SaLeadsstatePojo saLeadsstatePojo) {
        SaLeadsstateEntity saLeadsstateEntity = new SaLeadsstateEntity();
        BeanUtils.copyProperties(saLeadsstatePojo, saLeadsstateEntity);
        this.saLeadsstateMapper.update(saLeadsstateEntity);
        return this.getEntity(saLeadsstateEntity.getId());
    }


    @Override
    public int delete(String key) {
        return this.saLeadsstateMapper.delete(key);
    }


}
