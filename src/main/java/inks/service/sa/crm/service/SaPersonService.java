package inks.service.sa.crm.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.pojo.SaPersonPojo;

import java.util.List;

/**
 * 联系人(SaPerson)表服务接口
 *
 * <AUTHOR>
 * @since 2023-03-06 12:57:02
 */
public interface SaPersonService {


    SaPersonPojo getEntity(String key);


    PageInfo<SaPersonPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saPersonPojo 实例对象
     * @return 实例对象
     */
    SaPersonPojo insert(SaPersonPojo saPersonPojo);

    /**
     * 修改数据
     *
     * @param saPersonpojo 实例对象
     * @return 实例对象
     */
    SaPersonPojo update(SaPersonPojo saPersonpojo);


    int delete(String key);

    List<SaPersonPojo> getListByLeads(String id);

    List<SaPersonPojo> getListByGroup(String id);
}
