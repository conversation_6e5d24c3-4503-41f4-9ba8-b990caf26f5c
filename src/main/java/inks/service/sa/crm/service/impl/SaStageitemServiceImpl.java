package inks.service.sa.crm.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.crm.domain.SaStageitemEntity;
import inks.service.sa.crm.domain.pojo.SaStageitemPojo;
import inks.service.sa.crm.mapper.SaStageitemMapper;
import inks.service.sa.crm.service.SaStageitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 阶段子表(SaStageitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-02-20 09:09:40
 */
@Service("saStageitemService")
public class SaStageitemServiceImpl implements SaStageitemService {
    @Resource
    private SaStageitemMapper saStageitemMapper;


    @Override
    public SaStageitemPojo getEntity(String key) {
        return this.saStageitemMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaStageitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaStageitemPojo> lst = saStageitemMapper.getPageList(queryParam);
            PageInfo<SaStageitemPojo> pageInfo = new PageInfo<SaStageitemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public List<SaStageitemPojo> getList(String Pid) {
        try {
            List<SaStageitemPojo> lst = saStageitemMapper.getList(Pid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param saStageitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaStageitemPojo insert(SaStageitemPojo saStageitemPojo) {
        //初始化item的NULL
        SaStageitemPojo itempojo = this.clearNull(saStageitemPojo);
        SaStageitemEntity saStageitemEntity = new SaStageitemEntity();
        BeanUtils.copyProperties(itempojo, saStageitemEntity);
        //生成雪花id
        saStageitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saStageitemEntity.setRevision(1);  //乐观锁
        this.saStageitemMapper.insert(saStageitemEntity);
        return this.getEntity(saStageitemEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saStageitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaStageitemPojo update(SaStageitemPojo saStageitemPojo) {
        SaStageitemEntity saStageitemEntity = new SaStageitemEntity();
        BeanUtils.copyProperties(saStageitemPojo, saStageitemEntity);
        this.saStageitemMapper.update(saStageitemEntity);
        return this.getEntity(saStageitemEntity.getId());
    }


    @Override
    public int delete(String key) {
        return this.saStageitemMapper.delete(key);
    }

    /**
     * 修改数据
     *
     * @param saStageitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaStageitemPojo clearNull(SaStageitemPojo saStageitemPojo) {
        //初始化NULL字段
        if (saStageitemPojo.getPid() == null) saStageitemPojo.setPid("");
        if (saStageitemPojo.getParentid() == null) saStageitemPojo.setParentid("");
        if (saStageitemPojo.getStagename() == null) saStageitemPojo.setStagename("");
        if (saStageitemPojo.getRemark() == null) saStageitemPojo.setRemark("");
        if (saStageitemPojo.getRownum() == null) saStageitemPojo.setRownum(0);
        if (saStageitemPojo.getCustom1() == null) saStageitemPojo.setCustom1("");
        if (saStageitemPojo.getCustom2() == null) saStageitemPojo.setCustom2("");
        if (saStageitemPojo.getCustom3() == null) saStageitemPojo.setCustom3("");
        if (saStageitemPojo.getCustom4() == null) saStageitemPojo.setCustom4("");
        if (saStageitemPojo.getCustom5() == null) saStageitemPojo.setCustom5("");
        if (saStageitemPojo.getCustom6() == null) saStageitemPojo.setCustom6("");
        if (saStageitemPojo.getCustom7() == null) saStageitemPojo.setCustom7("");
        if (saStageitemPojo.getCustom8() == null) saStageitemPojo.setCustom8("");
        if (saStageitemPojo.getCustom9() == null) saStageitemPojo.setCustom9("");
        if (saStageitemPojo.getCustom10() == null) saStageitemPojo.setCustom10("");
        if (saStageitemPojo.getTenantid() == null) saStageitemPojo.setTenantid("");
        if (saStageitemPojo.getRevision() == null) saStageitemPojo.setRevision(0);
        return saStageitemPojo;
    }
}
