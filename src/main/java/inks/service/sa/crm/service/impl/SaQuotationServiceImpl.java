package inks.service.sa.crm.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.crm.domain.SaQuotationEntity;
import inks.service.sa.crm.domain.SaQuotationitemEntity;
import inks.service.sa.crm.domain.pojo.SaFollowviewPojo;
import inks.service.sa.crm.domain.pojo.SaQuotationPojo;
import inks.service.sa.crm.domain.pojo.SaQuotationitemPojo;
import inks.service.sa.crm.domain.pojo.SaQuotationitemdetailPojo;
import inks.service.sa.crm.mapper.SaQuotationMapper;
import inks.service.sa.crm.mapper.SaQuotationitemMapper;
import inks.service.sa.crm.service.SaFollowviewService;
import inks.service.sa.crm.service.SaQuotationService;
import inks.service.sa.crm.service.SaQuotationitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 报价单(SaQuotation)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-10-31 16:33:42
 */
@Service("saQuotationService")
public class SaQuotationServiceImpl implements SaQuotationService {
    @Resource
    private SaQuotationMapper saQuotationMapper;

    @Resource
    private SaQuotationitemMapper saQuotationitemMapper;

    /**
     * 服务对象Item
     */
    @Resource
    private SaQuotationitemService saQuotationitemService;
    @Autowired
    private SaFollowviewService saFollowviewService;

    private static void cleanNull(SaQuotationPojo saQuotationPojo) {
        if (saQuotationPojo.getRefno() == null) saQuotationPojo.setRefno("");
        if (saQuotationPojo.getBilltype() == null) saQuotationPojo.setBilltype("");
        if (saQuotationPojo.getBilltitle() == null) saQuotationPojo.setBilltitle("");
        if (saQuotationPojo.getBilldate() == null) saQuotationPojo.setBilldate(new Date());
        if (saQuotationPojo.getProbability() == null) saQuotationPojo.setProbability("");
        if (saQuotationPojo.getGroupid() == null) saQuotationPojo.setGroupid("");
        if (saQuotationPojo.getBusinessid() == null) saQuotationPojo.setBusinessid("");
        if (saQuotationPojo.getCustomer() == null) saQuotationPojo.setCustomer("");
        if (saQuotationPojo.getCustaddress() == null) saQuotationPojo.setCustaddress("");
        if (saQuotationPojo.getCustlinkman() == null) saQuotationPojo.setCustlinkman("");
        if (saQuotationPojo.getCusttel() == null) saQuotationPojo.setCusttel("");
        if (saQuotationPojo.getCustfax() == null) saQuotationPojo.setCustfax("");
        if (saQuotationPojo.getTaxmark() == null) saQuotationPojo.setTaxmark(0);
        if (saQuotationPojo.getPeriods() == null) saQuotationPojo.setPeriods("");
        if (saQuotationPojo.getValiditydate() == null) saQuotationPojo.setValiditydate("");
        if (saQuotationPojo.getCurrency() == null) saQuotationPojo.setCurrency("");
        if (saQuotationPojo.getDelivery() == null) saQuotationPojo.setDelivery("");
        if (saQuotationPojo.getPayment() == null) saQuotationPojo.setPayment("");
        if (saQuotationPojo.getWorkstage() == null) saQuotationPojo.setWorkstage("");
        if (saQuotationPojo.getOperatorid() == null) saQuotationPojo.setOperatorid("");
        if (saQuotationPojo.getOperator() == null) saQuotationPojo.setOperator("");
        if (saQuotationPojo.getBillclause() == null) saQuotationPojo.setBillclause("");
        if (saQuotationPojo.getBilltaxamount() == null) saQuotationPojo.setBilltaxamount(0D);
        if (saQuotationPojo.getBillamount() == null) saQuotationPojo.setBillamount(0D);
        if (saQuotationPojo.getBilltaxtotal() == null) saQuotationPojo.setBilltaxtotal(0D);
        if (saQuotationPojo.getSummary() == null) saQuotationPojo.setSummary("");
        if (saQuotationPojo.getCreateby() == null) saQuotationPojo.setCreateby("");
        if (saQuotationPojo.getCreatebyid() == null) saQuotationPojo.setCreatebyid("");
        if (saQuotationPojo.getCreatedate() == null) saQuotationPojo.setCreatedate(new Date());
        if (saQuotationPojo.getLister() == null) saQuotationPojo.setLister("");
        if (saQuotationPojo.getListerid() == null) saQuotationPojo.setListerid("");
        if (saQuotationPojo.getModifydate() == null) saQuotationPojo.setModifydate(new Date());
        if (saQuotationPojo.getAssessor() == null) saQuotationPojo.setAssessor("");
        if (saQuotationPojo.getAssessorid() == null) saQuotationPojo.setAssessorid("");
        if (saQuotationPojo.getAssessdate() == null) saQuotationPojo.setAssessdate(new Date());
        if(saQuotationPojo.getOaflowmark()==null) saQuotationPojo.setOaflowmark(0);
        if (saQuotationPojo.getStatecode() == null) saQuotationPojo.setStatecode("");
        if (saQuotationPojo.getStatedate() == null) saQuotationPojo.setStatedate(new Date());
        if (saQuotationPojo.getPrincipalid() == null) saQuotationPojo.setPrincipalid("");
        if (saQuotationPojo.getPrincipal() == null) saQuotationPojo.setPrincipal("");
        if (saQuotationPojo.getLastfollowdate() == null) saQuotationPojo.setLastfollowdate(new Date());
        if (saQuotationPojo.getCustom1() == null) saQuotationPojo.setCustom1("");
        if (saQuotationPojo.getCustom2() == null) saQuotationPojo.setCustom2("");
        if (saQuotationPojo.getCustom3() == null) saQuotationPojo.setCustom3("");
        if (saQuotationPojo.getCustom4() == null) saQuotationPojo.setCustom4("");
        if (saQuotationPojo.getCustom5() == null) saQuotationPojo.setCustom5("");
        if (saQuotationPojo.getCustom6() == null) saQuotationPojo.setCustom6("");
        if (saQuotationPojo.getCustom7() == null) saQuotationPojo.setCustom7("");
        if (saQuotationPojo.getCustom8() == null) saQuotationPojo.setCustom8("");
        if (saQuotationPojo.getCustom9() == null) saQuotationPojo.setCustom9("");
        if (saQuotationPojo.getCustom10() == null) saQuotationPojo.setCustom10("");
        if (saQuotationPojo.getDeptid() == null) saQuotationPojo.setDeptid("");
        if (saQuotationPojo.getTenantid() == null) saQuotationPojo.setTenantid("");
        if (saQuotationPojo.getTenantname() == null) saQuotationPojo.setTenantname("");
        if (saQuotationPojo.getRevision() == null) saQuotationPojo.setRevision(0);
    }

    @Override
    public SaQuotationPojo getEntity(String key) {
        return this.saQuotationMapper.getEntity(key);
    }

    @Override
    public PageInfo<SaQuotationitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaQuotationitemdetailPojo> lst = saQuotationMapper.getPageList(queryParam);
            PageInfo<SaQuotationitemdetailPojo> pageInfo = new PageInfo<SaQuotationitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public SaQuotationPojo getBillEntity(String key) {
        try {
            //读取主表
            SaQuotationPojo saQuotationPojo = this.saQuotationMapper.getEntity(key);
            //读取子表
            saQuotationPojo.setItem(saQuotationitemMapper.getList(saQuotationPojo.getId()));
            return saQuotationPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public PageInfo<SaQuotationPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaQuotationPojo> lst = saQuotationMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(saQuotationitemMapper.getList(lst.get(i).getId()));
            }
            PageInfo<SaQuotationPojo> pageInfo = new PageInfo<SaQuotationPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public PageInfo<SaQuotationPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaQuotationPojo> lst = saQuotationMapper.getPageTh(queryParam);
            PageInfo<SaQuotationPojo> pageInfo = new PageInfo<SaQuotationPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saQuotationPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public SaQuotationPojo insert(SaQuotationPojo saQuotationPojo, LoginUser loginUser) {
//初始化NULL字段
        cleanNull(saQuotationPojo);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        SaQuotationEntity saQuotationEntity = new SaQuotationEntity();
        BeanUtils.copyProperties(saQuotationPojo, saQuotationEntity);
        //设置id和新建日期
        saQuotationEntity.setId(id);
        saQuotationEntity.setRevision(1);  //乐观锁
        //插入主表
        this.saQuotationMapper.insert(saQuotationEntity);
        //Item子表处理
        List<SaQuotationitemPojo> lst = saQuotationPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (SaQuotationitemPojo saQuotationitemPojo : lst) {
                //初始化item的NULL
                SaQuotationitemPojo itemPojo = this.saQuotationitemService.clearNull(saQuotationitemPojo);
                SaQuotationitemEntity saQuotationitemEntity = new SaQuotationitemEntity();
                BeanUtils.copyProperties(itemPojo, saQuotationitemEntity);
                //设置id和Pid
                saQuotationitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                saQuotationitemEntity.setPid(id);
                saQuotationitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.saQuotationitemMapper.insert(saQuotationitemEntity);
            }
        }
        SaQuotationPojo billPojo = this.getBillEntity(saQuotationEntity.getId());
        // 创建客户跟进记录 IsAuto=1 为自动生成
        String itemContent = loginUser.getRealname() + "创建了报价单【" + billPojo.getRefno() + billPojo.getBilltitle() + "】";
        SaFollowviewPojo buildFollowview = SaFollowviewPojo.buildFollowview(billPojo.getGroupid(),
                "Customer",
                billPojo.getCustomer(),
                "Quotation.insert",
                itemContent,
                loginUser);
        saFollowviewService.insert(buildFollowview);
        //返回Bill实例
        return billPojo;

    }

    /**
     * 修改数据
     *
     * @param saQuotationPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public SaQuotationPojo update(SaQuotationPojo saQuotationPojo, LoginUser loginUser) {
        //主表更改
        SaQuotationEntity saQuotationEntity = new SaQuotationEntity();
        BeanUtils.copyProperties(saQuotationPojo, saQuotationEntity);
        this.saQuotationMapper.update(saQuotationEntity);
        if (saQuotationPojo.getItem() != null) {
            //Item子表处理
            List<SaQuotationitemPojo> lst = saQuotationPojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = saQuotationMapper.getDelItemIds(saQuotationPojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (int i = 0; i < lstDelIds.size(); i++) {
                    this.saQuotationitemMapper.delete(lstDelIds.get(i));
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (int i = 0; i < lst.size(); i++) {
                    SaQuotationitemEntity saQuotationitemEntity = new SaQuotationitemEntity();
                    if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null) {
                        //初始化item的NULL
                        SaQuotationitemPojo itemPojo = this.saQuotationitemService.clearNull(lst.get(i));
                        BeanUtils.copyProperties(itemPojo, saQuotationitemEntity);
                        //设置id和Pid
                        saQuotationitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        saQuotationitemEntity.setPid(saQuotationEntity.getId());  // 主表 id
                        saQuotationitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.saQuotationitemMapper.insert(saQuotationitemEntity);
                    } else {
                        BeanUtils.copyProperties(lst.get(i), saQuotationitemEntity);
                        this.saQuotationitemMapper.update(saQuotationitemEntity);
                    }
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(saQuotationEntity.getId());
    }

    @Override
    @Transactional
    public int delete(String key, LoginUser loginUser) {
        SaQuotationPojo saQuotationPojo = this.getBillEntity(key);
        //Item子表处理
        List<SaQuotationitemPojo> lst = saQuotationPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (SaQuotationitemPojo saQuotationitemPojo : lst) {
                this.saQuotationitemMapper.delete(saQuotationitemPojo.getId());
            }
        }
        // 创建客户跟进记录 IsAuto=1 为自动生成
        String itemContent = loginUser.getRealname() + "删除了报价单【" + saQuotationPojo.getRefno() + saQuotationPojo.getBilltitle() + "】";
        SaFollowviewPojo buildFollowview = SaFollowviewPojo.buildFollowview(saQuotationPojo.getGroupid(),
                "Customer",
                saQuotationPojo.getCustomer(),
                "Quotation.delete",
                itemContent,
                loginUser);
        saFollowviewService.insert(buildFollowview);
        return this.saQuotationMapper.delete(key);
    }

    /**
     * 审核数据
     *
     * @param saQuotationPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public SaQuotationPojo approval(SaQuotationPojo saQuotationPojo) {
        //主表更改
        SaQuotationEntity saQuotationEntity = new SaQuotationEntity();
        BeanUtils.copyProperties(saQuotationPojo, saQuotationEntity);
        this.saQuotationMapper.approval(saQuotationEntity);
        //返回Bill实例
        return this.getBillEntity(saQuotationEntity.getId());
    }

    @Override
    public void updateOaflowmark(SaQuotationPojo billPojo) {
        this.saQuotationMapper.updateOaflowmark(billPojo);
    }
}
