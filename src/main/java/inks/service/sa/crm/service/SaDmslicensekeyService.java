package inks.service.sa.crm.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.pojo.SaDmslicensekeyPojo;

/**
 * DMS用户-授权码关联表(SaDmslicensekey)表服务接口
 *
 * <AUTHOR>
 * @since 2023-12-09 09:56:37
 */
public interface SaDmslicensekeyService {


    SaDmslicensekeyPojo getEntity(String key);


    PageInfo<SaDmslicensekeyPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saDmslicensekeyPojo 实例对象
     * @return 实例对象
     */
    SaDmslicensekeyPojo insert(SaDmslicensekeyPojo saDmslicensekeyPojo);

    /**
     * 修改数据
     *
     * @param saDmslicensekeypojo 实例对象
     * @return 实例对象
     */
    SaDmslicensekeyPojo update(SaDmslicensekeyPojo saDmslicensekeypojo);


    int delete(String key);

}
