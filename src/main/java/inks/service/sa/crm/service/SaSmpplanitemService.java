package inks.service.sa.crm.service;

import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.pojo.SaSmpplanitemPojo;
import inks.service.sa.crm.domain.SaSmpplanitemEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import com.github.pagehelper.PageInfo;
import java.util.List;
/**
 * 计划项目(SaSmpplanitem)表服务接口
 *
 * <AUTHOR>
 * @since 2024-09-03 15:18:10
 */
public interface SaSmpplanitemService {


    SaSmpplanitemPojo getEntity(String key);

    PageInfo<SaSmpplanitemPojo> getPageList(QueryParam queryParam);

    List<SaSmpplanitemPojo> getList(String Pid);  

    SaSmpplanitemPojo insert(SaSmpplanitemPojo saSmpplanitemPojo);

    SaSmpplanitemPojo update(SaSmpplanitemPojo saSmpplanitempojo);

    int delete(String key);

    SaSmpplanitemPojo clearNull(SaSmpplanitemPojo saSmpplanitempojo);
}
