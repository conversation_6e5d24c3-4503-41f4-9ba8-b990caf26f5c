package inks.service.sa.crm.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.pojo.SaQuotationPojo;
import inks.service.sa.crm.domain.pojo.SaQuotationitemdetailPojo;

/**
 * 商机(SaQuotation)表服务接口
 *
 * <AUTHOR>
 * @since 2023-04-14 10:50:27
 */
public interface SaQuotationService {


    SaQuotationPojo getEntity(String key);


    PageInfo<SaQuotationitemdetailPojo> getPageList(QueryParam queryParam);


    SaQuotationPojo getBillEntity(String key);


    PageInfo<SaQuotationPojo> getBillList(QueryParam queryParam);


    PageInfo<SaQuotationPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saQuotationPojo 实例对象
     * @return 实例对象
     */
    SaQuotationPojo insert(SaQuotationPojo saQuotationPojo, LoginUser loginUser);

    /**
     * 修改数据
     *
     * @param saQuotationpojo 实例对象
     * @return 实例对象
     */
    SaQuotationPojo update(SaQuotationPojo saQuotationpojo, LoginUser loginUser);


    int delete(String key, LoginUser loginUser);

    /**
     * 审核数据
     *
     * @param saQuotationPojo 实例对象
     * @return 实例对象
     */
    SaQuotationPojo approval(SaQuotationPojo saQuotationPojo);

    void updateOaflowmark(SaQuotationPojo billPojo);
}
