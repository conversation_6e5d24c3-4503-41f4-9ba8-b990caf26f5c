package inks.service.sa.crm.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.pojo.SaDelieryPojo;
import inks.service.sa.crm.domain.pojo.SaDelieryitemdetailPojo;

/**
 * 发出商品(SaDeliery)表服务接口
 *
 * <AUTHOR>
 * @since 2024-04-05 16:51:44
 */
public interface SaDelieryService {


    SaDelieryPojo getEntity(String key);


    PageInfo<SaDelieryitemdetailPojo> getPageList(QueryParam queryParam);


    SaDelieryPojo getBillEntity(String key);


    PageInfo<SaDelieryPojo> getBillList(QueryParam queryParam);


    PageInfo<SaDelieryPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saDelieryPojo 实例对象
     * @return 实例对象
     */
    SaDelieryPojo insert(SaDelieryPojo saDelieryPojo);

    /**
     * 修改数据
     *
     * @param saDelierypojo 实例对象
     * @return 实例对象
     */
    SaDelieryPojo update(SaDelieryPojo saDelierypojo);


    int delete(String key);

    /**
     * 审核数据
     *
     * @param saDelieryPojo 实例对象
     * @return 实例对象
     */
    SaDelieryPojo approval(SaDelieryPojo saDelieryPojo);
}
