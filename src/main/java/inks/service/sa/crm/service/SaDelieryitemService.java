package inks.service.sa.crm.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.pojo.SaDelieryitemPojo;

import java.util.List;

/**
 * 发货明细(SaDelieryitem)表服务接口
 *
 * <AUTHOR>
 * @since 2024-04-05 16:52:14
 */
public interface SaDelieryitemService {


    SaDelieryitemPojo getEntity(String key);


    PageInfo<SaDelieryitemPojo> getPageList(QueryParam queryParam);

    List<SaDelieryitemPojo> getList(String Pid);

    /**
     * 新增数据
     *
     * @param saDelieryitemPojo 实例对象
     * @return 实例对象
     */
    SaDelieryitemPojo insert(SaDelieryitemPojo saDelieryitemPojo);

    /**
     * 修改数据
     *
     * @param saDelieryitempojo 实例对象
     * @return 实例对象
     */
    SaDelieryitemPojo update(SaDelieryitemPojo saDelieryitempojo);


    int delete(String key);

    /**
     * 修改数据
     *
     * @param saDelieryitempojo 实例对象
     * @return 实例对象
     */
    SaDelieryitemPojo clearNull(SaDelieryitemPojo saDelieryitempojo);
}
