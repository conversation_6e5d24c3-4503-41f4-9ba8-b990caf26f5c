package inks.service.sa.crm.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.pojo.SaPersonleadsPojo;

/**
 * (SaPersonleads)表服务接口
 *
 * <AUTHOR>
 * @since 2023-04-13 13:14:03
 */
public interface SaPersonleadsService {


    SaPersonleadsPojo getEntity(String key);


    PageInfo<SaPersonleadsPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saPersonleadsPojo 实例对象
     * @return 实例对象
     */
    SaPersonleadsPojo insert(SaPersonleadsPojo saPersonleadsPojo);

    /**
     * 修改数据
     *
     * @param saPersonleadspojo 实例对象
     * @return 实例对象
     */
    SaPersonleadsPojo update(SaPersonleadsPojo saPersonleadspojo);


    int delete(String key);
}
