package inks.service.sa.crm.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.crm.domain.SaReceiptEntity;
import inks.service.sa.crm.domain.pojo.SaReceiptPojo;
import inks.service.sa.crm.mapper.SaReceiptMapper;
import inks.service.sa.crm.service.SaReceiptService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 收款单(SaReceipt)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-03-25 12:48:51
 */
@Service("saReceiptService")
public class SaReceiptServiceImpl implements SaReceiptService {
    @Resource
    private SaReceiptMapper saReceiptMapper;

    private static void cleanNull(SaReceiptPojo saReceiptPojo) {
        if (saReceiptPojo.getRefno() == null) saReceiptPojo.setRefno("");
        if (saReceiptPojo.getBilltype() == null) saReceiptPojo.setBilltype("");
        if (saReceiptPojo.getBilltitle() == null) saReceiptPojo.setBilltitle("");
        if (saReceiptPojo.getBilldate() == null) saReceiptPojo.setBilldate(new Date());
        if (saReceiptPojo.getPlanid() == null) saReceiptPojo.setPlanid("");
        if (saReceiptPojo.getPlanuid() == null) saReceiptPojo.setPlanuid("");
        if (saReceiptPojo.getOrderid() == null) saReceiptPojo.setOrderid("");
        if (saReceiptPojo.getOrderuid() == null) saReceiptPojo.setOrderuid("");
        if (saReceiptPojo.getGroupid() == null) saReceiptPojo.setGroupid("");
        if (saReceiptPojo.getBusinessid() == null) saReceiptPojo.setBusinessid("");
        if (saReceiptPojo.getCustname() == null) saReceiptPojo.setCustname("");
        if (saReceiptPojo.getAmount() == null) saReceiptPojo.setAmount(0D);
        if (saReceiptPojo.getOperatorid() == null) saReceiptPojo.setOperatorid("");
        if (saReceiptPojo.getOperator() == null) saReceiptPojo.setOperator("");
        if (saReceiptPojo.getRownum() == null) saReceiptPojo.setRownum(0);
        if (saReceiptPojo.getRemark() == null) saReceiptPojo.setRemark("");
        if (saReceiptPojo.getCreateby() == null) saReceiptPojo.setCreateby("");
        if (saReceiptPojo.getCreatebyid() == null) saReceiptPojo.setCreatebyid("");
        if (saReceiptPojo.getCreatedate() == null) saReceiptPojo.setCreatedate(new Date());
        if (saReceiptPojo.getLister() == null) saReceiptPojo.setLister("");
        if (saReceiptPojo.getListerid() == null) saReceiptPojo.setListerid("");
        if (saReceiptPojo.getModifydate() == null) saReceiptPojo.setModifydate(new Date());
        if (saReceiptPojo.getAssessor() == null) saReceiptPojo.setAssessor("");
        if (saReceiptPojo.getAssessorid() == null) saReceiptPojo.setAssessorid("");
        if (saReceiptPojo.getAssessdate() == null) saReceiptPojo.setAssessdate(new Date());
        if (saReceiptPojo.getCustom1() == null) saReceiptPojo.setCustom1("");
        if (saReceiptPojo.getCustom2() == null) saReceiptPojo.setCustom2("");
        if (saReceiptPojo.getCustom3() == null) saReceiptPojo.setCustom3("");
        if (saReceiptPojo.getCustom4() == null) saReceiptPojo.setCustom4("");
        if (saReceiptPojo.getCustom5() == null) saReceiptPojo.setCustom5("");
        if (saReceiptPojo.getCustom6() == null) saReceiptPojo.setCustom6("");
        if (saReceiptPojo.getCustom7() == null) saReceiptPojo.setCustom7("");
        if (saReceiptPojo.getCustom8() == null) saReceiptPojo.setCustom8("");
        if (saReceiptPojo.getCustom9() == null) saReceiptPojo.setCustom9("");
        if (saReceiptPojo.getCustom10() == null) saReceiptPojo.setCustom10("");
        if (saReceiptPojo.getTenantid() == null) saReceiptPojo.setTenantid("");
        if (saReceiptPojo.getTenantname() == null) saReceiptPojo.setTenantname("");
        if (saReceiptPojo.getRevision() == null) saReceiptPojo.setRevision(0);
    }

    @Override
    public SaReceiptPojo getEntity(String key) {
        return this.saReceiptMapper.getEntity(key);
    }

    @Override
    public PageInfo<SaReceiptPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaReceiptPojo> lst = saReceiptMapper.getPageList(queryParam);
            PageInfo<SaReceiptPojo> pageInfo = new PageInfo<SaReceiptPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saReceiptPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaReceiptPojo insert(SaReceiptPojo saReceiptPojo) {
        //初始化NULL字段
        cleanNull(saReceiptPojo);
        SaReceiptEntity saReceiptEntity = new SaReceiptEntity();
        BeanUtils.copyProperties(saReceiptPojo, saReceiptEntity);

        saReceiptEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saReceiptEntity.setRevision(1);  //乐观锁
        this.saReceiptMapper.insert(saReceiptEntity);

        // 同步回款计划的完成金额Sa_ReceiptPlan.FinishAmt
        String planid = saReceiptPojo.getPlanid();
        if (StringUtils.isNotBlank(planid)) {
            this.saReceiptMapper.syncReceipPlanFinishAmt(planid);
        }

        return this.getEntity(saReceiptEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saReceiptPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaReceiptPojo update(SaReceiptPojo saReceiptPojo) {
        SaReceiptEntity saReceiptEntity = new SaReceiptEntity();
        BeanUtils.copyProperties(saReceiptPojo, saReceiptEntity);
        this.saReceiptMapper.update(saReceiptEntity);

        // 同步回款计划的完成金额Sa_ReceiptPlan.FinishAmt
        String planid = saReceiptPojo.getPlanid();
        if (StringUtils.isNotBlank(planid)) {
            this.saReceiptMapper.syncReceipPlanFinishAmt(planid);
        }
        return this.getEntity(saReceiptEntity.getId());
    }

    @Override
    public int delete(String key) {
        SaReceiptPojo saReceiptPojo = saReceiptMapper.getEntity(key);
        int i = this.saReceiptMapper.delete(key);
        // 同步回款计划的完成金额Sa_ReceiptPlan.FinishAmt
        String planid = saReceiptPojo.getPlanid();
        if (StringUtils.isNotBlank(planid)) {
            this.saReceiptMapper.syncReceipPlanFinishAmt(planid);
        }
        return i;
    }

    @Override
    @Transactional
    public SaReceiptPojo approval(SaReceiptPojo saReceiptPojo) {
        //主表更改
        SaReceiptEntity saReceiptEntity = new SaReceiptEntity();
        BeanUtils.copyProperties(saReceiptPojo, saReceiptEntity);
        this.saReceiptMapper.approval(saReceiptEntity);
        //返回Bill实例
        return this.getEntity(saReceiptEntity.getId());
    }

}
