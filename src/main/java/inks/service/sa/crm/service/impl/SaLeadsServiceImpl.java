package inks.service.sa.crm.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.crm.domain.SaLeadsEntity;
import inks.service.sa.crm.domain.pojo.SaFollowviewPojo;
import inks.service.sa.crm.domain.pojo.SaLeadsPojo;
import inks.service.sa.crm.mapper.SaLeadsMapper;
import inks.service.sa.crm.mapper.SaPersonleadsMapper;
import inks.service.sa.crm.service.SaFollowviewService;
import inks.service.sa.crm.service.SaLeadsService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 线索管理(SaLeads)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-04-13 16:02:26
 */
@Service("saLeadsService")
public class SaLeadsServiceImpl implements SaLeadsService {
    @Resource
    private SaLeadsMapper saLeadsMapper;

    @Resource
    private SaPersonleadsMapper saPersonleadsMapper;

    @Autowired
    private SaFollowviewService saFollowviewService;

    private static void cleanNull(SaLeadsPojo saLeadsPojo) {
        if (saLeadsPojo.getUserid() == null) saLeadsPojo.setUserid("");
        if (saLeadsPojo.getDeptid() == null) saLeadsPojo.setDeptid("");
        if (saLeadsPojo.getLeadsstate() == null) saLeadsPojo.setLeadsstate("");
        if (saLeadsPojo.getGengroupid() == null) saLeadsPojo.setGengroupid("");
        if (saLeadsPojo.getGroupid() == null) saLeadsPojo.setGroupid("");
        if (saLeadsPojo.getCustname() == null) saLeadsPojo.setCustname("");
        if (saLeadsPojo.getCusttype() == null) saLeadsPojo.setCusttype("");
        if (saLeadsPojo.getCustclass() == null) saLeadsPojo.setCustclass("");
        if (saLeadsPojo.getTelephone() == null) saLeadsPojo.setTelephone("");
        if (saLeadsPojo.getLinkman() == null) saLeadsPojo.setLinkman("");
        if (saLeadsPojo.getPosition() == null) saLeadsPojo.setPosition("");
        if (saLeadsPojo.getMobile() == null) saLeadsPojo.setMobile("");
        if (saLeadsPojo.getEmail() == null) saLeadsPojo.setEmail("");
        if (saLeadsPojo.getCustindustry() == null) saLeadsPojo.setCustindustry("");
        if (saLeadsPojo.getCustsource() == null) saLeadsPojo.setCustsource("");
        if (saLeadsPojo.getCustadd() == null) saLeadsPojo.setCustadd("");
        if (saLeadsPojo.getLongitude() == null) saLeadsPojo.setLongitude(0D);
        if (saLeadsPojo.getLatitude() == null) saLeadsPojo.setLatitude(0D);
        if (saLeadsPojo.getCluedetail() == null) saLeadsPojo.setCluedetail("");
        if (saLeadsPojo.getNextdate() == null) saLeadsPojo.setNextdate(new Date());
        if (saLeadsPojo.getOperatorid() == null) saLeadsPojo.setOperatorid("");
        if (saLeadsPojo.getOperator() == null) saLeadsPojo.setOperator("");
        if (saLeadsPojo.getDepartname() == null) saLeadsPojo.setDepartname("");
        if (saLeadsPojo.getWorkstage() == null) saLeadsPojo.setWorkstage("");
        if (saLeadsPojo.getCustmark() == null) saLeadsPojo.setCustmark(0);
        if (saLeadsPojo.getReceivetime() == null) saLeadsPojo.setReceivetime(new Date());
        if (saLeadsPojo.getEnabledmark() == null) saLeadsPojo.setEnabledmark(0);
        if (saLeadsPojo.getPhoto1() == null) saLeadsPojo.setPhoto1("");
        if (saLeadsPojo.getPhoto2() == null) saLeadsPojo.setPhoto2("");
        if (saLeadsPojo.getPhoto3() == null) saLeadsPojo.setPhoto3("");
        if (saLeadsPojo.getStatenum() == null) saLeadsPojo.setStatenum(0);
        if (saLeadsPojo.getStatedate() == null) saLeadsPojo.setStatedate(new Date());
        if (saLeadsPojo.getClosed() == null) saLeadsPojo.setClosed(0);
        if (saLeadsPojo.getClosedid() == null) saLeadsPojo.setClosedid("");
        if (saLeadsPojo.getClosedname() == null) saLeadsPojo.setClosedname("");
        if (saLeadsPojo.getCloseddate() == null) saLeadsPojo.setCloseddate(new Date());
        if (saLeadsPojo.getRownum() == null) saLeadsPojo.setRownum(0);
        if (saLeadsPojo.getRemark() == null) saLeadsPojo.setRemark("");
        if (saLeadsPojo.getCreateby() == null) saLeadsPojo.setCreateby("");
        if (saLeadsPojo.getCreatebyid() == null) saLeadsPojo.setCreatebyid("");
        if (saLeadsPojo.getCreatedate() == null) saLeadsPojo.setCreatedate(new Date());
        if (saLeadsPojo.getLister() == null) saLeadsPojo.setLister("");
        if (saLeadsPojo.getListerid() == null) saLeadsPojo.setListerid("");
        if (saLeadsPojo.getModifydate() == null) saLeadsPojo.setModifydate(new Date());
        if (saLeadsPojo.getPrincipalid() == null) saLeadsPojo.setPrincipalid("");
        if (saLeadsPojo.getPrincipal() == null) saLeadsPojo.setPrincipal("");
        if (saLeadsPojo.getLastfollowdate() == null) saLeadsPojo.setLastfollowdate(new Date());
        if (saLeadsPojo.getCustom1() == null) saLeadsPojo.setCustom1("");
        if (saLeadsPojo.getCustom2() == null) saLeadsPojo.setCustom2("");
        if (saLeadsPojo.getCustom3() == null) saLeadsPojo.setCustom3("");
        if (saLeadsPojo.getCustom4() == null) saLeadsPojo.setCustom4("");
        if (saLeadsPojo.getCustom5() == null) saLeadsPojo.setCustom5("");
        if (saLeadsPojo.getCustom6() == null) saLeadsPojo.setCustom6("");
        if (saLeadsPojo.getCustom7() == null) saLeadsPojo.setCustom7("");
        if (saLeadsPojo.getCustom8() == null) saLeadsPojo.setCustom8("");
        if (saLeadsPojo.getCustom9() == null) saLeadsPojo.setCustom9("");
        if (saLeadsPojo.getCustom10() == null) saLeadsPojo.setCustom10("");
        if (saLeadsPojo.getTenantid() == null) saLeadsPojo.setTenantid("");
        if (saLeadsPojo.getTenantname() == null) saLeadsPojo.setTenantname("");
        if (saLeadsPojo.getRevision() == null) saLeadsPojo.setRevision(0);
    }

    @Override
    public SaLeadsPojo getEntity(String key) {
        return this.saLeadsMapper.getEntity(key);
    }

    @Override
    public PageInfo<SaLeadsPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaLeadsPojo> lst = saLeadsMapper.getPageList(queryParam);
            PageInfo<SaLeadsPojo> pageInfo = new PageInfo<SaLeadsPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saLeadsPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaLeadsPojo insert(SaLeadsPojo saLeadsPojo, LoginUser loginUser) {
        //初始化NULL字段
        cleanNull(saLeadsPojo);
        SaLeadsEntity saLeadsEntity = new SaLeadsEntity();
        BeanUtils.copyProperties(saLeadsPojo, saLeadsEntity);
        //生成雪花id
        saLeadsEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saLeadsEntity.setRevision(1);  //乐观锁
        this.saLeadsMapper.insert(saLeadsEntity);

        // 创建跟进记录 IsAuto=1 为自动生成
        String itemContent = loginUser.getRealname() + "创建了线索";
        SaFollowviewPojo buildFollowview = SaFollowviewPojo.buildFollowview(saLeadsEntity.getId(), "Leads", saLeadsEntity.getCustname(),
                "Leads.insert", itemContent, loginUser);
        saFollowviewService.insert(buildFollowview);
        return this.getEntity(saLeadsEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saLeadsPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaLeadsPojo update(SaLeadsPojo saLeadsPojo, LoginUser loginUser) {
        // 数据库里的线索
        SaLeadsPojo saLeadsPojoDB = saLeadsMapper.getEntity(saLeadsPojo.getId());
        SaLeadsEntity saLeadsEntity = new SaLeadsEntity();
        BeanUtils.copyProperties(saLeadsPojo, saLeadsEntity);
        this.saLeadsMapper.update(saLeadsEntity);

        // 创建跟进记录 IsAuto=1 为自动生成
        // 构建日志内容 itemContent
        StringBuilder itemContentSB = new StringBuilder();
        String realName = loginUser.getRealname();
        // 如果传入了custname客户名,则新增一条需求日志: nanno修改了客户名为"XXX"  前端失焦触发update 只传入id和一个修改的字段
        if (saLeadsPojo.getCustname() != null) {
            itemContentSB.append(realName).append("修改了客户名为:").append(saLeadsPojo.getCustname());
        }//如果传入了custsource线索来源
        else if (saLeadsPojo.getCustsource() != null) {
            itemContentSB.append(realName).append("修改了线索来源为:").append(saLeadsPojo.getCustsource());
        }//如果传入了custtype客户类型
        else if (saLeadsPojo.getCusttype() != null) {
            itemContentSB.append(realName).append("修改了客户类型为:").append(saLeadsPojo.getCusttype());
        }//如果传入了custclass客户级别
        else if (saLeadsPojo.getCustclass() != null) {
            itemContentSB.append(realName).append("修改了客户级别为:").append(saLeadsPojo.getCustclass());
        }//如果传入了custindustry客户行业
        else if (saLeadsPojo.getCustindustry() != null) {
            itemContentSB.append(realName).append("修改了客户行业为:").append(saLeadsPojo.getCustindustry());
        }//如果传入了custadd客户地址
        else if (saLeadsPojo.getCustadd() != null) {
            itemContentSB.append(realName).append("修改了客户地址为:").append(saLeadsPojo.getCustadd());
        }//如果传入了cluedetail线索详情
        else if (saLeadsPojo.getCluedetail() != null) {
            itemContentSB.append(realName).append("修改了线索详情为:").append(saLeadsPojo.getCluedetail());
        }//如果传入了nextdate下次跟进时间
        else if (saLeadsPojo.getNextdate() != null) {
            itemContentSB.append(realName).append("修改了下次跟进时间为:").append(saLeadsPojo.getNextdate());
        }//如果传入了summary备注
        else if (saLeadsPojo.getRemark() != null) {
            itemContentSB.append(realName).append("修改了备注为:").append(saLeadsPojo.getRemark());
        }//如果传入了custmark客户标记
        else if (saLeadsPojo.getCustmark() != null) {
            itemContentSB.append(realName).append("修改了客户标记为:").append(saLeadsPojo.getCustmark());
        }//enabledmark有效性
        else if (saLeadsPojo.getEnabledmark() != null) {
            itemContentSB.append(realName).append("修改了有效性:").append(saLeadsPojo.getEnabledmark());
        }//如果传入了邮箱,联系人,职务,电话,地址
        else if (saLeadsPojo.getEmail() != null) {
            itemContentSB.append(realName).append("修改了邮箱为:").append(saLeadsPojo.getEmail());
        }//如果传入了部门
        else if (saLeadsPojo.getDepartname() != null) {
            itemContentSB.append(realName).append("修改了部门为:").append(saLeadsPojo.getDepartname());
        }//如果传入了工作阶段
        else if (saLeadsPojo.getWorkstage() != null) {
            itemContentSB.append(realName).append("修改了工作阶段为:").append(saLeadsPojo.getWorkstage());
        }//如果传入了联系人,职务,电话,地址
        else if (saLeadsPojo.getLinkman() != null) {
            itemContentSB.append(realName).append("修改了联系人:").append(saLeadsPojo.getLinkman());
        }//如果传入了职务
        else if (saLeadsPojo.getPosition() != null) {
            itemContentSB.append(realName).append("修改了职务为:").append(saLeadsPojo.getPosition());
        }//如果传入了电话
        else if (saLeadsPojo.getTelephone() != null) {
            itemContentSB.append(realName).append("修改了电话为:").append(saLeadsPojo.getTelephone());
        }//如果传入了手机
        else if (saLeadsPojo.getMobile() != null) {
            itemContentSB.append(realName).append("修改了手机为:").append(saLeadsPojo.getMobile());
        }//如果传入了负责人
        else if (saLeadsPojo.getPrincipal() != null) {
            itemContentSB.append(realName).append("修改了负责人为:").append(saLeadsPojo.getPrincipal());
        }

        SaFollowviewPojo buildFollowview = SaFollowviewPojo.buildFollowview(saLeadsEntity.getId(), "Leads", saLeadsEntity.getCustname(),
                "Leads.update", itemContentSB.toString(), loginUser);
        saFollowviewService.insert(buildFollowview);
        return this.getEntity(saLeadsEntity.getId());
    }

    @Override
    public int delete(String key, LoginUser loginUser) {
        SaLeadsPojo leadsPojo = getEntity(key);
        saPersonleadsMapper.deleteByLeadsId(key);
        // 创建跟进记录 IsAuto=1 为自动生成
        String itemContent = loginUser.getRealname() + "删除了线索";
        SaFollowviewPojo buildFollowview = SaFollowviewPojo.buildFollowview(leadsPojo.getId(), "Leads", leadsPojo.getCustname(),
                "Leads.delete", itemContent, loginUser);
        saFollowviewService.insert(buildFollowview);
        return this.saLeadsMapper.delete(key);
    }

    @Override
    public SaLeadsPojo getEntityByCustname(String custname) {
        return  this.saLeadsMapper.getEntityByCustname(custname);
    }
}
