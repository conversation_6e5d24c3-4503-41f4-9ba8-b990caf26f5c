package inks.service.sa.crm.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.pojo.SaLeadsstatePojo;

/**
 * (SaLeadsstate)表服务接口
 *
 * <AUTHOR>
 * @since 2023-04-13 14:45:06
 */
public interface SaLeadsstateService {


    SaLeadsstatePojo getEntity(String key);


    PageInfo<SaLeadsstatePojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saLeadsstatePojo 实例对象
     * @return 实例对象
     */
    SaLeadsstatePojo insert(SaLeadsstatePojo saLeadsstatePojo);

    /**
     * 修改数据
     *
     * @param saLeadsstatepojo 实例对象
     * @return 实例对象
     */
    SaLeadsstatePojo update(SaLeadsstatePojo saLeadsstatepojo);


    int delete(String key);
}
