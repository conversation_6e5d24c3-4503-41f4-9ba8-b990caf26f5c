package inks.service.sa.crm.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.crm.domain.SaBusinessitemEntity;
import inks.service.sa.crm.domain.pojo.SaBusinessitemPojo;
import inks.service.sa.crm.mapper.SaBusinessitemMapper;
import inks.service.sa.crm.service.SaBusinessitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 商机子表(SaBusinessitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-07-25 16:30:20
 */
@Service("saBusinessitemService")
public class SaBusinessitemServiceImpl implements SaBusinessitemService {
    @Resource
    private SaBusinessitemMapper saBusinessitemMapper;

    @Override
    public SaBusinessitemPojo getEntity(String key) {
        return this.saBusinessitemMapper.getEntity(key);
    }

    @Override
    public PageInfo<SaBusinessitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaBusinessitemPojo> lst = saBusinessitemMapper.getPageList(queryParam);
            PageInfo<SaBusinessitemPojo> pageInfo = new PageInfo<SaBusinessitemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public List<SaBusinessitemPojo> getList(String Pid) {
        try {
            List<SaBusinessitemPojo> lst = saBusinessitemMapper.getList(Pid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public SaBusinessitemPojo insert(SaBusinessitemPojo saBusinessitemPojo) {
        //初始化item的NULL
        SaBusinessitemPojo itempojo = this.clearNull(saBusinessitemPojo);
        SaBusinessitemEntity saBusinessitemEntity = new SaBusinessitemEntity();
        BeanUtils.copyProperties(itempojo, saBusinessitemEntity);
        //生成雪花id
        saBusinessitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saBusinessitemEntity.setRevision(1);  //乐观锁
        this.saBusinessitemMapper.insert(saBusinessitemEntity);
        return this.getEntity(saBusinessitemEntity.getId());

    }

    @Override
    public SaBusinessitemPojo update(SaBusinessitemPojo saBusinessitemPojo) {
        SaBusinessitemEntity saBusinessitemEntity = new SaBusinessitemEntity();
        BeanUtils.copyProperties(saBusinessitemPojo, saBusinessitemEntity);
        this.saBusinessitemMapper.update(saBusinessitemEntity);
        return this.getEntity(saBusinessitemEntity.getId());
    }

    @Override
    public int delete(String key) {
        return this.saBusinessitemMapper.delete(key);
    }

    @Override
    public SaBusinessitemPojo clearNull(SaBusinessitemPojo saBusinessitemPojo) {
        //初始化NULL字段
        if (saBusinessitemPojo.getPid() == null) saBusinessitemPojo.setPid("");
        if (saBusinessitemPojo.getGoodsid() == null) saBusinessitemPojo.setGoodsid("");
        if (saBusinessitemPojo.getItemtype() == null) saBusinessitemPojo.setItemtype("");
        if (saBusinessitemPojo.getItemname() == null) saBusinessitemPojo.setItemname("");
        if (saBusinessitemPojo.getItemspec() == null) saBusinessitemPojo.setItemspec("");
        if (saBusinessitemPojo.getItemunit() == null) saBusinessitemPojo.setItemunit("");
        if (saBusinessitemPojo.getQuantity() == null) saBusinessitemPojo.setQuantity(0D);
        if (saBusinessitemPojo.getPrice() == null) saBusinessitemPojo.setPrice(0D);
        if (saBusinessitemPojo.getAmount() == null) saBusinessitemPojo.setAmount(0D);
        if (saBusinessitemPojo.getItemtaxrate() == null) saBusinessitemPojo.setItemtaxrate(0);
        if (saBusinessitemPojo.getTaxtotal() == null) saBusinessitemPojo.setTaxtotal(0D);
        if (saBusinessitemPojo.getTaxprice() == null) saBusinessitemPojo.setTaxprice(0D);
        if (saBusinessitemPojo.getTaxamount() == null) saBusinessitemPojo.setTaxamount(0D);
        if (saBusinessitemPojo.getRemark() == null) saBusinessitemPojo.setRemark("");
        if (saBusinessitemPojo.getRownum() == null) saBusinessitemPojo.setRownum(0);
     if(saBusinessitemPojo.getAttributejson()==null) saBusinessitemPojo.setAttributejson("");
     if(saBusinessitemPojo.getAttributestr()==null) saBusinessitemPojo.setAttributestr("");
     if(saBusinessitemPojo.getCustom1()==null) saBusinessitemPojo.setCustom1("");
        if (saBusinessitemPojo.getCustom2() == null) saBusinessitemPojo.setCustom2("");
        if (saBusinessitemPojo.getCustom3() == null) saBusinessitemPojo.setCustom3("");
        if (saBusinessitemPojo.getCustom4() == null) saBusinessitemPojo.setCustom4("");
        if (saBusinessitemPojo.getCustom5() == null) saBusinessitemPojo.setCustom5("");
        if (saBusinessitemPojo.getCustom6() == null) saBusinessitemPojo.setCustom6("");
        if (saBusinessitemPojo.getCustom7() == null) saBusinessitemPojo.setCustom7("");
        if (saBusinessitemPojo.getCustom8() == null) saBusinessitemPojo.setCustom8("");
        if (saBusinessitemPojo.getCustom9() == null) saBusinessitemPojo.setCustom9("");
        if (saBusinessitemPojo.getCustom10() == null) saBusinessitemPojo.setCustom10("");
        if (saBusinessitemPojo.getTenantid() == null) saBusinessitemPojo.setTenantid("");
        if (saBusinessitemPojo.getRevision() == null) saBusinessitemPojo.setRevision(0);
        return saBusinessitemPojo;
    }
}
