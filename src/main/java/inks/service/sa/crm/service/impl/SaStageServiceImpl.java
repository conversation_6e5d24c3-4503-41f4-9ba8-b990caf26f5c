package inks.service.sa.crm.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.crm.domain.SaStageEntity;
import inks.service.sa.crm.domain.SaStageitemEntity;
import inks.service.sa.crm.domain.pojo.SaStagePojo;
import inks.service.sa.crm.domain.pojo.SaStageitemPojo;
import inks.service.sa.crm.domain.pojo.SaStageitemdetailPojo;
import inks.service.sa.crm.mapper.SaStageMapper;
import inks.service.sa.crm.mapper.SaStageitemMapper;
import inks.service.sa.crm.service.SaStageService;
import inks.service.sa.crm.service.SaStageitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 阶段(SaStage)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-02-23 13:39:17
 */
@Service("saStageService")
public class SaStageServiceImpl implements SaStageService {
    @Resource
    private SaStageMapper saStageMapper;

    @Resource
    private SaStageitemMapper saStageitemMapper;

    /**
     * 服务对象Item
     */
    @Resource
    private SaStageitemService saStageitemService;


    @Override
    public SaStagePojo getEntity(String key) {
        return this.saStageMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaStageitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaStageitemdetailPojo> lst = saStageMapper.getPageList(queryParam);
            PageInfo<SaStageitemdetailPojo> pageInfo = new PageInfo<SaStageitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public SaStagePojo getBillEntity(String key) {
        try {
            //读取主表
            SaStagePojo saStagePojo = this.saStageMapper.getEntity(key);
            //读取子表
            saStagePojo.setItem(saStageitemMapper.getList(saStagePojo.getId()));
            return saStagePojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public PageInfo<SaStagePojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaStagePojo> lst = saStageMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(saStageitemMapper.getList(lst.get(i).getId()));
            }
            PageInfo<SaStagePojo> pageInfo = new PageInfo<SaStagePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public PageInfo<SaStagePojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaStagePojo> lst = saStageMapper.getPageTh(queryParam);
            PageInfo<SaStagePojo> pageInfo = new PageInfo<SaStagePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saStagePojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public SaStagePojo insert(SaStagePojo saStagePojo) {
        //初始化NULL字段
        cleanNull(saStagePojo);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        SaStageEntity saStageEntity = new SaStageEntity();
        BeanUtils.copyProperties(saStagePojo, saStageEntity);
        //设置id和新建日期
        saStageEntity.setId(id);
        saStageEntity.setRevision(1);  //乐观锁
        //插入主表
        this.saStageMapper.insert(saStageEntity);
        //Item子表处理
        List<SaStageitemPojo> lst = saStagePojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                //初始化item的NULL
                SaStageitemPojo itemPojo = this.saStageitemService.clearNull(lst.get(i));
                SaStageitemEntity saStageitemEntity = new SaStageitemEntity();
                BeanUtils.copyProperties(itemPojo, saStageitemEntity);
                //设置id和Pid
                saStageitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                saStageitemEntity.setPid(id);
                saStageitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.saStageitemMapper.insert(saStageitemEntity);
            }
        }
        //返回Bill实例
        return this.getBillEntity(saStageEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saStagePojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public SaStagePojo update(SaStagePojo saStagePojo) {
        //主表更改
        SaStageEntity saStageEntity = new SaStageEntity();
        BeanUtils.copyProperties(saStagePojo, saStageEntity);
        this.saStageMapper.update(saStageEntity);
        if (saStagePojo.getItem() != null) {
            //Item子表处理
            List<SaStageitemPojo> lst = saStagePojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = saStageMapper.getDelItemIds(saStagePojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (int i = 0; i < lstDelIds.size(); i++) {
                    this.saStageitemMapper.delete(lstDelIds.get(i));
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (int i = 0; i < lst.size(); i++) {
                    SaStageitemEntity saStageitemEntity = new SaStageitemEntity();
                    if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null) {
                        //初始化item的NULL
                        SaStageitemPojo itemPojo = this.saStageitemService.clearNull(lst.get(i));
                        BeanUtils.copyProperties(itemPojo, saStageitemEntity);
                        //设置id和Pid
                        saStageitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        saStageitemEntity.setPid(saStageEntity.getId());  // 主表 id
                        saStageitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.saStageitemMapper.insert(saStageitemEntity);
                    } else {
                        BeanUtils.copyProperties(lst.get(i), saStageitemEntity);
                        this.saStageitemMapper.update(saStageitemEntity);
                    }
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(saStageEntity.getId());
    }


    @Override
    @Transactional
    public int delete(String key) {
        SaStagePojo saStagePojo = this.getBillEntity(key);
        //Item子表处理
        List<SaStageitemPojo> lst = saStagePojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (int i = 0; i < lst.size(); i++) {
                this.saStageitemMapper.delete(lst.get(i).getId());
            }
        }
        return this.saStageMapper.delete(key);
    }


    private static void cleanNull(SaStagePojo saStagePojo) {
        if(saStagePojo.getGrouptype()==null) saStagePojo.setGrouptype("");
        if(saStagePojo.getGroupcode()==null) saStagePojo.setGroupcode("");
        if(saStagePojo.getGroupname()==null) saStagePojo.setGroupname("");
        if(saStagePojo.getStagejson()==null) saStagePojo.setStagejson("");
        if(saStagePojo.getDescription()==null) saStagePojo.setDescription("");
        if(saStagePojo.getEnabledmark()==null) saStagePojo.setEnabledmark(0);
        if(saStagePojo.getSummary()==null) saStagePojo.setSummary("");
        if(saStagePojo.getCreateby()==null) saStagePojo.setCreateby("");
        if(saStagePojo.getCreatebyid()==null) saStagePojo.setCreatebyid("");
        if(saStagePojo.getCreatedate()==null) saStagePojo.setCreatedate(new Date());
        if(saStagePojo.getLister()==null) saStagePojo.setLister("");
        if(saStagePojo.getListerid()==null) saStagePojo.setListerid("");
        if(saStagePojo.getModifydate()==null) saStagePojo.setModifydate(new Date());
        if(saStagePojo.getCustom1()==null) saStagePojo.setCustom1("");
        if(saStagePojo.getCustom2()==null) saStagePojo.setCustom2("");
        if(saStagePojo.getCustom3()==null) saStagePojo.setCustom3("");
        if(saStagePojo.getCustom4()==null) saStagePojo.setCustom4("");
        if(saStagePojo.getCustom5()==null) saStagePojo.setCustom5("");
        if(saStagePojo.getCustom6()==null) saStagePojo.setCustom6("");
        if(saStagePojo.getCustom7()==null) saStagePojo.setCustom7("");
        if(saStagePojo.getCustom8()==null) saStagePojo.setCustom8("");
        if(saStagePojo.getCustom9()==null) saStagePojo.setCustom9("");
        if(saStagePojo.getCustom10()==null) saStagePojo.setCustom10("");
        if(saStagePojo.getDeptid()==null) saStagePojo.setDeptid("");
        if(saStagePojo.getTenantid()==null) saStagePojo.setTenantid("");
        if(saStagePojo.getTenantname()==null) saStagePojo.setTenantname("");
        if(saStagePojo.getRevision()==null) saStagePojo.setRevision(0);
   }
}
