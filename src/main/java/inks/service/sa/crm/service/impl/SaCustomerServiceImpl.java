package inks.service.sa.crm.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.BillCodeUtil;
import inks.service.sa.crm.domain.SaCustomerEntity;
import inks.service.sa.crm.domain.SaLeadsEntity;
import inks.service.sa.crm.domain.pojo.SaCustomerPojo;
import inks.service.sa.crm.domain.pojo.SaFollowviewPojo;
import inks.service.sa.crm.mapper.SaCustomerMapper;
import inks.service.sa.crm.mapper.SaLeadsMapper;
import inks.service.sa.crm.mapper.SaPersongroupMapper;
import inks.service.sa.crm.service.SaCustomerService;
import inks.service.sa.crm.service.SaFollowviewService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 客户(SaCustomer)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-03-06 14:17:22
 */
@Service("saCustomerService")
public class SaCustomerServiceImpl implements SaCustomerService {
    @Resource
    private SaCustomerMapper saCustomerMapper;
    @Resource
    private SaLeadsMapper saLeadsMapper;

    @Resource
    private SaPersongroupMapper SaPersongroupMapper;
    @Autowired
    private SaFollowviewService saFollowviewService;

    private static void cleanNull(SaCustomerPojo saCustomerPojo) {
        if (saCustomerPojo.getCustuid() == null) saCustomerPojo.setCustuid("");
        if (saCustomerPojo.getUserid() == null) saCustomerPojo.setUserid("");
        if (saCustomerPojo.getDeptid() == null) saCustomerPojo.setDeptid("");
        if (saCustomerPojo.getLeadsid() == null) saCustomerPojo.setLeadsid("");
        if (saCustomerPojo.getGengroupid() == null) saCustomerPojo.setGengroupid("");
        if (saCustomerPojo.getCustname() == null) saCustomerPojo.setCustname("");
        if (saCustomerPojo.getCusttype() == null) saCustomerPojo.setCusttype("");
        if (saCustomerPojo.getCustclass() == null) saCustomerPojo.setCustclass("");
        if (saCustomerPojo.getTelephone() == null) saCustomerPojo.setTelephone("");
        if (saCustomerPojo.getLinkman() == null) saCustomerPojo.setLinkman("");
        if (saCustomerPojo.getPosition() == null) saCustomerPojo.setPosition("");
        if (saCustomerPojo.getMobile() == null) saCustomerPojo.setMobile("");
        if (saCustomerPojo.getEmail() == null) saCustomerPojo.setEmail("");
        if (saCustomerPojo.getCustindustry() == null) saCustomerPojo.setCustindustry("");
        if (saCustomerPojo.getCustsource() == null) saCustomerPojo.setCustsource("");
        if (saCustomerPojo.getLongitude() == null) saCustomerPojo.setLongitude(0D);
        if (saCustomerPojo.getLatitude() == null) saCustomerPojo.setLatitude(0D);
        if (saCustomerPojo.getCustadd() == null) saCustomerPojo.setCustadd("");
        if (saCustomerPojo.getNextdate() == null) saCustomerPojo.setNextdate(new Date());
        if (saCustomerPojo.getOperatorid() == null) saCustomerPojo.setOperatorid("");
        if (saCustomerPojo.getOperator() == null) saCustomerPojo.setOperator("");
        if (saCustomerPojo.getDepartname() == null) saCustomerPojo.setDepartname("");
        if (saCustomerPojo.getEnabledmark() == null) saCustomerPojo.setEnabledmark(0);
        if (saCustomerPojo.getRemark() == null) saCustomerPojo.setRemark("");
        if (saCustomerPojo.getPrincipalid() == null) saCustomerPojo.setPrincipalid("");
        if (saCustomerPojo.getPrincipal() == null) saCustomerPojo.setPrincipal("");
        if (saCustomerPojo.getLastfollowdate() == null) saCustomerPojo.setLastfollowdate(new Date());
        if (saCustomerPojo.getCreateby() == null) saCustomerPojo.setCreateby("");
        if (saCustomerPojo.getCreatebyid() == null) saCustomerPojo.setCreatebyid("");
        if (saCustomerPojo.getCreatedate() == null) saCustomerPojo.setCreatedate(new Date());
        if (saCustomerPojo.getLister() == null) saCustomerPojo.setLister("");
        if (saCustomerPojo.getListerid() == null) saCustomerPojo.setListerid("");
        if (saCustomerPojo.getModifydate() == null) saCustomerPojo.setModifydate(new Date());
        if (saCustomerPojo.getExtendcode() == null) saCustomerPojo.setExtendcode("");
        if (saCustomerPojo.getCity() == null) saCustomerPojo.setCity("");
        if (saCustomerPojo.getCounty() == null) saCustomerPojo.setCounty("");
        if (saCustomerPojo.getStreet() == null) saCustomerPojo.setStreet("");
        if (saCustomerPojo.getLocaladd() == null) saCustomerPojo.setLocaladd("");
        if (saCustomerPojo.getRegion() == null) saCustomerPojo.setRegion("");
        if (saCustomerPojo.getActiontype() == null) saCustomerPojo.setActiontype("");
        if (saCustomerPojo.getCustom1() == null) saCustomerPojo.setCustom1("");
        if (saCustomerPojo.getCustom2() == null) saCustomerPojo.setCustom2("");
        if (saCustomerPojo.getCustom3() == null) saCustomerPojo.setCustom3("");
        if (saCustomerPojo.getCustom4() == null) saCustomerPojo.setCustom4("");
        if (saCustomerPojo.getCustom5() == null) saCustomerPojo.setCustom5("");
        if (saCustomerPojo.getCustom6() == null) saCustomerPojo.setCustom6("");
        if (saCustomerPojo.getCustom7() == null) saCustomerPojo.setCustom7("");
        if (saCustomerPojo.getCustom8() == null) saCustomerPojo.setCustom8("");
        if (saCustomerPojo.getCustom9() == null) saCustomerPojo.setCustom9("");
        if (saCustomerPojo.getCustom10() == null) saCustomerPojo.setCustom10("");
        if (saCustomerPojo.getTenantid() == null) saCustomerPojo.setTenantid("");
        if (saCustomerPojo.getTenantname() == null) saCustomerPojo.setTenantname("");
        if (saCustomerPojo.getRevision() == null) saCustomerPojo.setRevision(0);
    }

    @Override
    public SaCustomerPojo getEntity(String key) {
        return this.saCustomerMapper.getEntity(key);
    }

    @Override
    public PageInfo<SaCustomerPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaCustomerPojo> lst = saCustomerMapper.getPageList(queryParam);
            PageInfo<SaCustomerPojo> pageInfo = new PageInfo<SaCustomerPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saCustomerPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaCustomerPojo insert(SaCustomerPojo saCustomerPojo, LoginUser loginUser) {
        //校验custuid唯一
        if (StringUtils.isNotBlank(saCustomerPojo.getCustuid())) {
            SaCustomerPojo entityByCustuid = this.saCustomerMapper.getEntityByCustuid(saCustomerPojo.getCustuid());
            if (entityByCustuid != null) {
                throw new BaseBusinessException("客户编码【" + saCustomerPojo.getCustuid() + "】已存在！");
            }
        }
        //初始化NULL字段
        cleanNull(saCustomerPojo);
        SaCustomerEntity saCustomerEntity = new SaCustomerEntity();
        BeanUtils.copyProperties(saCustomerPojo, saCustomerEntity);
        saCustomerEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saCustomerEntity.setRevision(1);  //乐观锁
        this.saCustomerMapper.insert(saCustomerEntity);
        //反写线索表的Groupid,CustMark
        String leadsid = saCustomerEntity.getLeadsid();
        if (StringUtils.isNotBlank(leadsid)) {
            SaLeadsEntity saLeadsEntity = new SaLeadsEntity();
            saLeadsEntity.setId(leadsid);
            saLeadsEntity.setCustmark(1);
            saLeadsEntity.setGroupid(saCustomerEntity.getId());
            saLeadsMapper.update(saLeadsEntity);
        }

        // 创建客户跟进记录 IsAuto=1 为自动生成
        String itemContent = loginUser.getRealname() + "创建了客户【" + saCustomerEntity.getCustname() + "】";
        if (StringUtils.isNotBlank(leadsid)) {
            itemContent += "-由线索转入";
        }
        SaFollowviewPojo buildFollowview = SaFollowviewPojo.buildFollowview(saCustomerEntity.getId(), "Customer", saCustomerEntity.getCustname(),
                "Customer.insert", itemContent, loginUser);
        saFollowviewService.insert(buildFollowview);

        if (StringUtils.isNotBlank(leadsid)) {
            // 创建线索跟进记录 IsAuto=1 为自动生成
            String itemContent2 = "线索转为客户【" + saCustomerEntity.getCustname() + "】";
            SaFollowviewPojo buildFollowview2 = SaFollowviewPojo.buildFollowview(leadsid, "Leads", saCustomerEntity.getCustname(),
                    "Customer.insert", itemContent2, loginUser);
            saFollowviewService.insert(buildFollowview2);
        }

        return this.getEntity(saCustomerEntity.getId());
    }

    /**
     * 修改数据
     *
     * @param saCustomerPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaCustomerPojo update(SaCustomerPojo saCustomerPojo, LoginUser loginUser) {
//        // 数据库里的客户
//        SaCustomerPojo saCustomerPojoDB = saCustomerMapper.getEntity(saCustomerPojo.getId());
        //校验custuid唯一
        if (StringUtils.isNotBlank(saCustomerPojo.getCustuid())) {
            SaCustomerPojo entityByCustuid = this.saCustomerMapper.getEntityByCustuid(saCustomerPojo.getCustuid());
            if (entityByCustuid != null && !saCustomerPojo.getId().equals(entityByCustuid.getId())) {
                throw new BaseBusinessException("客户编码【" + saCustomerPojo.getCustuid() + "】已存在！");
            }
        }
        SaCustomerEntity saCustomerEntity = new SaCustomerEntity();
        BeanUtils.copyProperties(saCustomerPojo, saCustomerEntity);
        this.saCustomerMapper.update(saCustomerEntity);

        // 创建跟进客户记录 IsAuto=1 为自动生成0.00
        // 构建日志内容 itemContent
        StringBuilder itemContentSB = new StringBuilder();
        String realName = loginUser.getRealname();
        // 如果传入了custname客户名,则新增一条需求日志: nanno修改了客户名为"XXX" 前端失焦触发update 只传入id和一个修改的字段
        if (StringUtils.isNotBlank(saCustomerPojo.getCustname())) {
            itemContentSB.append(realName).append("修改了客户名为:").append(saCustomerPojo.getCustname());
        } else if (StringUtils.isNotBlank(saCustomerPojo.getCusttype())) {
            itemContentSB.append(realName).append("修改了客户类型为:").append(saCustomerPojo.getCusttype());
        } else if (StringUtils.isNotBlank(saCustomerPojo.getCustclass())) {
            itemContentSB.append(realName).append("修改了客户等级为:").append(saCustomerPojo.getCustclass());
        } else if (StringUtils.isNotBlank(saCustomerPojo.getTelephone())) {
            itemContentSB.append(realName).append("修改了电话为:").append(saCustomerPojo.getTelephone());
        } else if (StringUtils.isNotBlank(saCustomerPojo.getLinkman())) {
            itemContentSB.append(realName).append("修改了联系人为:").append(saCustomerPojo.getLinkman());
        } else if (StringUtils.isNotBlank(saCustomerPojo.getPosition())) {
            itemContentSB.append(realName).append("修改了联系人职务为:").append(saCustomerPojo.getPosition());
        } else if (StringUtils.isNotBlank(saCustomerPojo.getMobile())) {
            itemContentSB.append(realName).append("修改了手机为:").append(saCustomerPojo.getMobile());
        } else if (StringUtils.isNotBlank(saCustomerPojo.getEmail())) {
            itemContentSB.append(realName).append("修改了Email为:").append(saCustomerPojo.getEmail());
        } else if (StringUtils.isNotBlank(saCustomerPojo.getCustindustry())) {
            itemContentSB.append(realName).append("修改了行业为:").append(saCustomerPojo.getCustindustry());
        } else if (StringUtils.isNotBlank(saCustomerPojo.getCustsource())) {
            itemContentSB.append(realName).append("修改了来源为:").append(saCustomerPojo.getCustsource());
        } else if (saCustomerPojo.getLongitude() != null) {
            itemContentSB.append(realName).append("修改了经度为:").append(saCustomerPojo.getLongitude());
        } else if (saCustomerPojo.getLatitude() != null) {
            itemContentSB.append(realName).append("修改了纬度为:").append(saCustomerPojo.getLatitude());
        } else if (StringUtils.isNotBlank(saCustomerPojo.getCustadd())) {
            itemContentSB.append(realName).append("修改了地址为:").append(saCustomerPojo.getCustadd());
        } else if (saCustomerPojo.getNextdate() != null) {
            itemContentSB.append(realName).append("修改了下次联系为:").append(saCustomerPojo.getNextdate());
        } else if (StringUtils.isNotBlank(saCustomerPojo.getOperatorid())) {
            itemContentSB.append(realName).append("修改了经办人id为:").append(saCustomerPojo.getOperatorid());
        } else if (StringUtils.isNotBlank(saCustomerPojo.getOperator())) {
            itemContentSB.append(realName).append("修改了经办人为:").append(saCustomerPojo.getOperator());
        } else if (StringUtils.isNotBlank(saCustomerPojo.getDepartname())) {
            itemContentSB.append(realName).append("修改了部门为:").append(saCustomerPojo.getDepartname());
        } else if (saCustomerPojo.getEnabledmark() != null) {
            itemContentSB.append(realName).append("修改了有效性为:").append(saCustomerPojo.getEnabledmark());
        } else if (StringUtils.isNotBlank(saCustomerPojo.getRemark())) {
            itemContentSB.append(realName).append("修改了备注为:").append(saCustomerPojo.getRemark());
        }
        SaFollowviewPojo buildFollowview = SaFollowviewPojo.buildFollowview(saCustomerEntity.getId(), "Customer", saCustomerEntity.getCustname(),
                "Customer.update", itemContentSB.toString(), loginUser);
        saFollowviewService.insert(buildFollowview);

        return this.getEntity(saCustomerEntity.getId());
    }

    @Override
    public int delete(String key, LoginUser loginUser) {
        // 如果删除的客户是线索转过来的，则把该线索的CustMark置为0
        SaCustomerPojo saCustomerPojo = this.saCustomerMapper.getEntity(key);
        SaLeadsEntity saLeadsEntity = new SaLeadsEntity();
        saLeadsEntity.setId(saCustomerPojo.getLeadsid());
        saLeadsEntity.setCustmark(0);
        saLeadsMapper.update(saLeadsEntity);
        //删除客户与联系人组的关联
        SaPersongroupMapper.deleteByGroupId(key);
        // 创建跟进记录 IsAuto=1 为自动生成
        String itemContent = loginUser.getRealname() + "删除了客户【" + saCustomerPojo.getCustname() + "】";
        SaFollowviewPojo buildFollowview = SaFollowviewPojo.buildFollowview(saCustomerPojo.getId(), "Customer", saCustomerPojo.getCustname(),
                "Customer.delete", itemContent, loginUser);
        saFollowviewService.insert(buildFollowview);
        return this.saCustomerMapper.delete(key);
    }

    @Override
    public String getNextCode() {

        String NextCode = "";
        String MaxStr = this.saCustomerMapper.getMaxCode();
        if (MaxStr != null) {
            NextCode = BillCodeUtil.SnLatter(MaxStr, 1);
        }

        return NextCode;
    }

    @Override
    public SaCustomerPojo getEntityByCustname(String custname) {
        return this.saCustomerMapper.getEntityByCustname(custname);
    }
}
