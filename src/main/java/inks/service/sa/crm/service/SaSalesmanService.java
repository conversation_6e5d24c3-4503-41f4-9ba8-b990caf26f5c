package inks.service.sa.crm.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.pojo.SaSalesmanPojo;

/**
 * 业务员(SaSalesman)表服务接口
 *
 * <AUTHOR>
 * @since 2023-10-31 16:22:15
 */
public interface SaSalesmanService {


    SaSalesmanPojo getEntity(String key);


    PageInfo<SaSalesmanPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saSalesmanPojo 实例对象
     * @return 实例对象
     */
    SaSalesmanPojo insert(SaSalesmanPojo saSalesmanPojo);

    /**
     * 修改数据
     *
     * @param saSalesmanpojo 实例对象
     * @return 实例对象
     */
    SaSalesmanPojo update(SaSalesmanPojo saSalesmanpojo);


    int delete(String key);
}
