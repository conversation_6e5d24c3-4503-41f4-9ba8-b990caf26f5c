package inks.service.sa.crm.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.pojo.SaBusinessitemPojo;

import java.util.List;

/**
 * 商机子表(SaBusinessitem)表服务接口
 *
 * <AUTHOR>
 * @since 2024-07-25 16:30:11
 */
public interface SaBusinessitemService {


    SaBusinessitemPojo getEntity(String key);

    PageInfo<SaBusinessitemPojo> getPageList(QueryParam queryParam);

    List<SaBusinessitemPojo> getList(String Pid);

    SaBusinessitemPojo insert(SaBusinessitemPojo saBusinessitemPojo);

    SaBusinessitemPojo update(SaBusinessitemPojo saBusinessitempojo);

    int delete(String key);

    SaBusinessitemPojo clearNull(SaBusinessitemPojo saBusinessitempojo);
}
