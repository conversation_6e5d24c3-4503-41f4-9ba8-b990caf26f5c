package inks.service.sa.crm.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.crm.domain.SaDelieryEntity;
import inks.service.sa.crm.domain.SaDelieryitemEntity;
import inks.service.sa.crm.domain.pojo.SaDelieryPojo;
import inks.service.sa.crm.domain.pojo.SaDelieryitemPojo;
import inks.service.sa.crm.domain.pojo.SaDelieryitemdetailPojo;
import inks.service.sa.crm.mapper.SaDelieryMapper;
import inks.service.sa.crm.mapper.SaDelieryitemMapper;
import inks.service.sa.crm.service.SaDelieryService;
import inks.service.sa.crm.service.SaDelieryitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 发出商品(SaDeliery)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-04-05 16:51:45
 */
@Service("saDelieryService")
public class SaDelieryServiceImpl implements SaDelieryService {
    @Resource
    private SaDelieryMapper saDelieryMapper;

    @Resource
    private SaDelieryitemMapper saDelieryitemMapper;


    @Resource
    private SaDelieryitemService saDelieryitemService;

    private static void cleanNull(SaDelieryPojo saDelieryPojo) {
        if (saDelieryPojo.getRefno() == null) saDelieryPojo.setRefno("");
        if (saDelieryPojo.getBilltype() == null) saDelieryPojo.setBilltype("");
        if (saDelieryPojo.getBilltitle() == null) saDelieryPojo.setBilltitle("");
        if (saDelieryPojo.getBilldate() == null) saDelieryPojo.setBilldate(new Date());
        if (saDelieryPojo.getGroupid() == null) saDelieryPojo.setGroupid("");
        if (saDelieryPojo.getTelephone() == null) saDelieryPojo.setTelephone("");
        if (saDelieryPojo.getLinkman() == null) saDelieryPojo.setLinkman("");
        if (saDelieryPojo.getDeliadd() == null) saDelieryPojo.setDeliadd("");
        if (saDelieryPojo.getTaxrate() == null) saDelieryPojo.setTaxrate(0);
        if (saDelieryPojo.getTransport() == null) saDelieryPojo.setTransport("");
        if (saDelieryPojo.getSalesman() == null) saDelieryPojo.setSalesman("");
        if (saDelieryPojo.getSalesmanid() == null) saDelieryPojo.setSalesmanid("");
        if (saDelieryPojo.getOperator() == null) saDelieryPojo.setOperator("");
        if (saDelieryPojo.getOperatorid() == null) saDelieryPojo.setOperatorid("");
        if (saDelieryPojo.getSummary() == null) saDelieryPojo.setSummary("");
        if (saDelieryPojo.getCreateby() == null) saDelieryPojo.setCreateby("");
        if (saDelieryPojo.getCreatebyid() == null) saDelieryPojo.setCreatebyid("");
        if (saDelieryPojo.getCreatedate() == null) saDelieryPojo.setCreatedate(new Date());
        if (saDelieryPojo.getLister() == null) saDelieryPojo.setLister("");
        if (saDelieryPojo.getListerid() == null) saDelieryPojo.setListerid("");
        if (saDelieryPojo.getModifydate() == null) saDelieryPojo.setModifydate(new Date());
        if (saDelieryPojo.getAssessor() == null) saDelieryPojo.setAssessor("");
        if (saDelieryPojo.getAssessorid() == null) saDelieryPojo.setAssessorid("");
        if (saDelieryPojo.getAssessdate() == null) saDelieryPojo.setAssessdate(new Date());
        if (saDelieryPojo.getBillstatecode() == null) saDelieryPojo.setBillstatecode("");
        if (saDelieryPojo.getBillstatedate() == null) saDelieryPojo.setBillstatedate(new Date());
        if (saDelieryPojo.getBilltaxamount() == null) saDelieryPojo.setBilltaxamount(0D);
        if (saDelieryPojo.getBilltaxtotal() == null) saDelieryPojo.setBilltaxtotal(0D);
        if (saDelieryPojo.getBillamount() == null) saDelieryPojo.setBillamount(0D);
        if (saDelieryPojo.getBillreceived() == null) saDelieryPojo.setBillreceived(0D);
        if (saDelieryPojo.getItemcount() == null) saDelieryPojo.setItemcount(0);
        if (saDelieryPojo.getPickcount() == null) saDelieryPojo.setPickcount(0);
        if (saDelieryPojo.getFinishcount() == null) saDelieryPojo.setFinishcount(0);
        if (saDelieryPojo.getInvocount() == null) saDelieryPojo.setInvocount(0);
        if (saDelieryPojo.getDisannulcount() == null) saDelieryPojo.setDisannulcount(0);
        if (saDelieryPojo.getPrintcount() == null) saDelieryPojo.setPrintcount(0);
        if (saDelieryPojo.getOaflowmark() == null) saDelieryPojo.setOaflowmark(0);
        if (saDelieryPojo.getFirstamt() == null) saDelieryPojo.setFirstamt(0D);
        if (saDelieryPojo.getLastamt() == null) saDelieryPojo.setLastamt(0D);
        if (saDelieryPojo.getCustom1() == null) saDelieryPojo.setCustom1("");
        if (saDelieryPojo.getCustom2() == null) saDelieryPojo.setCustom2("");
        if (saDelieryPojo.getCustom3() == null) saDelieryPojo.setCustom3("");
        if (saDelieryPojo.getCustom4() == null) saDelieryPojo.setCustom4("");
        if (saDelieryPojo.getCustom5() == null) saDelieryPojo.setCustom5("");
        if (saDelieryPojo.getCustom6() == null) saDelieryPojo.setCustom6("");
        if (saDelieryPojo.getCustom7() == null) saDelieryPojo.setCustom7("");
        if (saDelieryPojo.getCustom8() == null) saDelieryPojo.setCustom8("");
        if (saDelieryPojo.getCustom9() == null) saDelieryPojo.setCustom9("");
        if (saDelieryPojo.getCustom10() == null) saDelieryPojo.setCustom10("");
        if (saDelieryPojo.getTenantid() == null) saDelieryPojo.setTenantid("");
        if (saDelieryPojo.getTenantname() == null) saDelieryPojo.setTenantname("");
        if (saDelieryPojo.getRevision() == null) saDelieryPojo.setRevision(0);
    }

    @Override
    public SaDelieryPojo getEntity(String key) {
        return this.saDelieryMapper.getEntity(key);
    }

    @Override
    public PageInfo<SaDelieryitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaDelieryitemdetailPojo> lst = saDelieryMapper.getPageList(queryParam);
            PageInfo<SaDelieryitemdetailPojo> pageInfo = new PageInfo<SaDelieryitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public SaDelieryPojo getBillEntity(String key) {
        try {
            //读取主表
            SaDelieryPojo saDelieryPojo = this.saDelieryMapper.getEntity(key);
            //读取子表
            saDelieryPojo.setItem(saDelieryitemMapper.getList(saDelieryPojo.getId()));
            return saDelieryPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public PageInfo<SaDelieryPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaDelieryPojo> lst = saDelieryMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(saDelieryitemMapper.getList(lst.get(i).getId()));
            }
            PageInfo<SaDelieryPojo> pageInfo = new PageInfo<SaDelieryPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public PageInfo<SaDelieryPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaDelieryPojo> lst = saDelieryMapper.getPageTh(queryParam);
            PageInfo<SaDelieryPojo> pageInfo = new PageInfo<SaDelieryPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    @Transactional
    public SaDelieryPojo insert(SaDelieryPojo saDelieryPojo) {
        //初始化NULL字段
        cleanNull(saDelieryPojo);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        SaDelieryEntity saDelieryEntity = new SaDelieryEntity();
        BeanUtils.copyProperties(saDelieryPojo, saDelieryEntity);
        //设置id和新建日期
        saDelieryEntity.setId(id);
        saDelieryEntity.setRevision(1);  //乐观锁
        //插入主表
        this.saDelieryMapper.insert(saDelieryEntity);
        //Item子表处理
        List<SaDelieryitemPojo> lst = saDelieryPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                //初始化item的NULL
                SaDelieryitemPojo itemPojo = this.saDelieryitemService.clearNull(lst.get(i));
                SaDelieryitemEntity saDelieryitemEntity = new SaDelieryitemEntity();
                BeanUtils.copyProperties(itemPojo, saDelieryitemEntity);
                //设置id和Pid
                saDelieryitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                saDelieryitemEntity.setPid(id);
                saDelieryitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.saDelieryitemMapper.insert(saDelieryitemEntity);
            }
        }
        //返回Bill实例
        return this.getBillEntity(saDelieryEntity.getId());
    }

    @Override
    @Transactional
    public SaDelieryPojo update(SaDelieryPojo saDelieryPojo) {
        //主表更改
        SaDelieryEntity saDelieryEntity = new SaDelieryEntity();
        BeanUtils.copyProperties(saDelieryPojo, saDelieryEntity);
        this.saDelieryMapper.update(saDelieryEntity);
        if (saDelieryPojo.getItem() != null) {
            //Item子表处理
            List<SaDelieryitemPojo> lst = saDelieryPojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = saDelieryMapper.getDelItemIds(saDelieryPojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (int i = 0; i < lstDelIds.size(); i++) {
                    this.saDelieryitemMapper.delete(lstDelIds.get(i));
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (int i = 0; i < lst.size(); i++) {
                    SaDelieryitemEntity saDelieryitemEntity = new SaDelieryitemEntity();
                    if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null) {
                        //初始化item的NULL
                        SaDelieryitemPojo itemPojo = this.saDelieryitemService.clearNull(lst.get(i));
                        BeanUtils.copyProperties(itemPojo, saDelieryitemEntity);
                        //设置id和Pid
                        saDelieryitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        saDelieryitemEntity.setPid(saDelieryEntity.getId());  // 主表 id
                        saDelieryitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.saDelieryitemMapper.insert(saDelieryitemEntity);
                    } else {
                        BeanUtils.copyProperties(lst.get(i), saDelieryitemEntity);
                        this.saDelieryitemMapper.update(saDelieryitemEntity);
                    }
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(saDelieryEntity.getId());
    }

    @Override
    @Transactional
    public int delete(String key) {
        SaDelieryPojo saDelieryPojo = this.getBillEntity(key);
        //Item子表处理
        List<SaDelieryitemPojo> lst = saDelieryPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (int i = 0; i < lst.size(); i++) {
                this.saDelieryitemMapper.delete(lst.get(i).getId());
            }
        }
        return this.saDelieryMapper.delete(key);
    }

    @Override
    @Transactional
    public SaDelieryPojo approval(SaDelieryPojo saDelieryPojo) {
        //主表更改
        SaDelieryEntity saDelieryEntity = new SaDelieryEntity();
        BeanUtils.copyProperties(saDelieryPojo, saDelieryEntity);
        this.saDelieryMapper.approval(saDelieryEntity);
        //返回Bill实例
        return this.getBillEntity(saDelieryEntity.getId());
    }

}
