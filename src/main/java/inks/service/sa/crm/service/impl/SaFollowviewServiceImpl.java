package inks.service.sa.crm.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.crm.domain.SaFollowviewEntity;
import inks.service.sa.crm.domain.pojo.SaFollowviewPojo;
import inks.service.sa.crm.mapper.SaCustomerMapper;
import inks.service.sa.crm.mapper.SaFollowviewMapper;
import inks.service.sa.crm.mapper.SaLeadsMapper;
import inks.service.sa.crm.mapper.SaLeadsitemMapper;
import inks.service.sa.crm.service.SaFollowviewService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 跟踪记录(SaFollowview)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-07-26 09:51:37
 */
@Service("saFollowviewService")
public class SaFollowviewServiceImpl implements SaFollowviewService {
    @Resource
    private SaLeadsMapper saLeadsMapper;
    @Resource
    private SaFollowviewMapper saFollowviewMapper;

    @Resource
    private SaCustomerMapper saCustomerMapper;

    private static void cleanNull(SaFollowviewPojo saFollowviewPojo) {
        if (saFollowviewPojo.getCiteid() == null) saFollowviewPojo.setCiteid("");
        if (saFollowviewPojo.getCitetype() == null) saFollowviewPojo.setCitetype("");
        if (saFollowviewPojo.getItemtype() == null) saFollowviewPojo.setItemtype("");
        if (saFollowviewPojo.getItemcontent() == null) saFollowviewPojo.setItemcontent("");
        if (saFollowviewPojo.getIsauto() == null) saFollowviewPojo.setIsauto(0);
        if (saFollowviewPojo.getCustname() == null) saFollowviewPojo.setCustname("");
        if(saFollowviewPojo.getSalesman()==null) saFollowviewPojo.setSalesman("");
//        if(saFollowviewPojo.getNextdate()==null) saFollowviewPojo.setNextdate(new Date());
        //if(saFollowviewPojo.getEnddate()==null) saFollowviewPojo.setEnddate(new Date());
//        if(saFollowviewPojo.getFinishdate()==null) saFollowviewPojo.setFinishdate(new Date());
        if(saFollowviewPojo.getFinisher()==null) saFollowviewPojo.setFinisher("");
        if(saFollowviewPojo.getFinishdesc()==null) saFollowviewPojo.setFinishdesc("");
        if(saFollowviewPojo.getFinishmark()==null) saFollowviewPojo.setFinishmark(0);
        if(saFollowviewPojo.getPhotourl1()==null) saFollowviewPojo.setPhotourl1("");
        if (saFollowviewPojo.getPhotourl2() == null) saFollowviewPojo.setPhotourl2("");
        if (saFollowviewPojo.getPhotourl3() == null) saFollowviewPojo.setPhotourl3("");
        if (saFollowviewPojo.getPhotoname1() == null) saFollowviewPojo.setPhotoname1("");
        if (saFollowviewPojo.getPhotoname2() == null) saFollowviewPojo.setPhotoname2("");
        if (saFollowviewPojo.getPhotoname3() == null) saFollowviewPojo.setPhotoname3("");
        if (saFollowviewPojo.getOperatorid() == null) saFollowviewPojo.setOperatorid("");
        if (saFollowviewPojo.getOperator() == null) saFollowviewPojo.setOperator("");
        if(saFollowviewPojo.getLabeljson()==null) saFollowviewPojo.setLabeljson("[]");
        if(saFollowviewPojo.getCollaboratorids()==null) saFollowviewPojo.setCollaboratorids("");
        if(saFollowviewPojo.getCollaborators()==null) saFollowviewPojo.setCollaborators("");
        if(saFollowviewPojo.getRownum()==null) saFollowviewPojo.setRownum(0);
        if (saFollowviewPojo.getRemark() == null) saFollowviewPojo.setRemark("");
        if (saFollowviewPojo.getLister() == null) saFollowviewPojo.setLister("");
        if (saFollowviewPojo.getListerid() == null) saFollowviewPojo.setListerid("");
        if(saFollowviewPojo.getCreateby()==null) saFollowviewPojo.setCreateby("");
        if(saFollowviewPojo.getCreatebyid()==null) saFollowviewPojo.setCreatebyid("");
        if(saFollowviewPojo.getCreatedate()==null) saFollowviewPojo.setCreatedate(new Date());
        if (saFollowviewPojo.getModifydate() == null) saFollowviewPojo.setModifydate(new Date());
        if (saFollowviewPojo.getCustom1() == null) saFollowviewPojo.setCustom1("");
        if (saFollowviewPojo.getCustom2() == null) saFollowviewPojo.setCustom2("");
        if (saFollowviewPojo.getCustom3() == null) saFollowviewPojo.setCustom3("");
        if (saFollowviewPojo.getCustom4() == null) saFollowviewPojo.setCustom4("");
        if (saFollowviewPojo.getCustom5() == null) saFollowviewPojo.setCustom5("");
        if (saFollowviewPojo.getCustom6() == null) saFollowviewPojo.setCustom6("");
        if (saFollowviewPojo.getCustom7() == null) saFollowviewPojo.setCustom7("");
        if (saFollowviewPojo.getCustom8() == null) saFollowviewPojo.setCustom8("");
        if (saFollowviewPojo.getCustom9() == null) saFollowviewPojo.setCustom9("");
        if (saFollowviewPojo.getCustom10() == null) saFollowviewPojo.setCustom10("");
        if (saFollowviewPojo.getTenantid() == null) saFollowviewPojo.setTenantid("");
        if (saFollowviewPojo.getTenantname() == null) saFollowviewPojo.setTenantname("");
        if (saFollowviewPojo.getRevision() == null) saFollowviewPojo.setRevision(0);
        if (saFollowviewPojo.getCreateby() == null) saFollowviewPojo.setCreateby("");
        if (saFollowviewPojo.getCreatebyid() == null) saFollowviewPojo.setCreatebyid("");
    }

    @Override
    public SaFollowviewPojo getEntity(String key) {
        return this.saFollowviewMapper.getEntity(key);
    }

    @Override
    public PageInfo<SaFollowviewPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaFollowviewPojo> lst = saFollowviewMapper.getPageList(queryParam);
            PageInfo<SaFollowviewPojo> pageInfo = new PageInfo<SaFollowviewPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public SaFollowviewPojo insert(SaFollowviewPojo saFollowviewPojo) {
        //初始化NULL字段
        cleanNull(saFollowviewPojo);
        SaFollowviewEntity saFollowviewEntity = new SaFollowviewEntity();
        BeanUtils.copyProperties(saFollowviewPojo, saFollowviewEntity);
        //生成雪花id
        saFollowviewEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saFollowviewEntity.setRevision(1);  //乐观锁
        this.saFollowviewMapper.insert(saFollowviewEntity);
        //根据类型同步 客户或线索的下次联系时间=最近一个没有完成的计划 关联id类型:Leads/Customer/Business/Quotation/Contract
        String citeType = saFollowviewPojo.getCitetype();
        switch (citeType) {
            case "Leads":
                saLeadsMapper.syncNextDateByFollowview(saFollowviewPojo.getCiteid());
                break;
            case "Customer":
                saCustomerMapper.syncNextDateByFollowview(saFollowviewPojo.getCiteid());
                break;
            default:
                break;
        }
        return this.getEntity(saFollowviewEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saFollowviewPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaFollowviewPojo update(SaFollowviewPojo saFollowviewPojo) {
        SaFollowviewEntity saFollowviewEntity = new SaFollowviewEntity();
        BeanUtils.copyProperties(saFollowviewPojo, saFollowviewEntity);
        this.saFollowviewMapper.update(saFollowviewEntity);
        //根据类型同步 客户或线索的下次联系时间=最近一个没有完成的计划 关联id类型:Leads/Customer/Business/Quotation/Contract
        String citeType = saFollowviewPojo.getCitetype();
        switch (citeType) {
            case "Leads":
                saLeadsMapper.syncNextDateByFollowview(saFollowviewPojo.getCiteid());
                break;
            case "Customer":
                saCustomerMapper.syncNextDateByFollowview(saFollowviewPojo.getCiteid());
                break;
            default:
                break;
        }
        return this.getEntity(saFollowviewEntity.getId());
    }

    @Override
    public int delete(String key) {
        return this.saFollowviewMapper.delete(key);
    }

}
