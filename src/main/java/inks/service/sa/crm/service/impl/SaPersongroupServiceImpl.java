package inks.service.sa.crm.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.crm.domain.SaPersongroupEntity;
import inks.service.sa.crm.domain.pojo.SaPersongroupPojo;
import inks.service.sa.crm.mapper.SaPersongroupMapper;
import inks.service.sa.crm.service.SaPersongroupService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * (SaPersongroup)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-04-13 13:14:03
 */
@Service("saPersongroupService")
public class SaPersongroupServiceImpl implements SaPersongroupService {
    @Resource
    private SaPersongroupMapper saPersongroupMapper;


    @Override
    public SaPersongroupPojo getEntity(String key) {
        return this.saPersongroupMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaPersongroupPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaPersongroupPojo> lst = saPersongroupMapper.getPageList(queryParam);
            PageInfo<SaPersongroupPojo> pageInfo = new PageInfo<SaPersongroupPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saPersongroupPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaPersongroupPojo insert(SaPersongroupPojo saPersongroupPojo) {
        //初始化NULL字段
        if (saPersongroupPojo.getPersonid() == null) saPersongroupPojo.setPersonid("");
        if (saPersongroupPojo.getGroupid() == null) saPersongroupPojo.setGroupid("");
        if (saPersongroupPojo.getRownum() == null) saPersongroupPojo.setRownum(0);
        if (saPersongroupPojo.getRemark() == null) saPersongroupPojo.setRemark("");
        if (saPersongroupPojo.getCreateby() == null) saPersongroupPojo.setCreateby("");
        if (saPersongroupPojo.getCreatebyid() == null) saPersongroupPojo.setCreatebyid("");
        if (saPersongroupPojo.getCreatedate() == null) saPersongroupPojo.setCreatedate(new Date());
        if (saPersongroupPojo.getLister() == null) saPersongroupPojo.setLister("");
        if (saPersongroupPojo.getListerid() == null) saPersongroupPojo.setListerid("");
        if (saPersongroupPojo.getModifydate() == null) saPersongroupPojo.setModifydate(new Date());
        if (saPersongroupPojo.getCustom1() == null) saPersongroupPojo.setCustom1("");
        if (saPersongroupPojo.getCustom2() == null) saPersongroupPojo.setCustom2("");
        if (saPersongroupPojo.getCustom3() == null) saPersongroupPojo.setCustom3("");
        if (saPersongroupPojo.getCustom4() == null) saPersongroupPojo.setCustom4("");
        if (saPersongroupPojo.getCustom5() == null) saPersongroupPojo.setCustom5("");
        if (saPersongroupPojo.getTenantid() == null) saPersongroupPojo.setTenantid("");
        if (saPersongroupPojo.getTenantname() == null) saPersongroupPojo.setTenantname("");
        if (saPersongroupPojo.getRevision() == null) saPersongroupPojo.setRevision(0);
        SaPersongroupEntity saPersongroupEntity = new SaPersongroupEntity();
        BeanUtils.copyProperties(saPersongroupPojo, saPersongroupEntity);
        //生成雪花id
        saPersongroupEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saPersongroupEntity.setRevision(1);  //乐观锁
        this.saPersongroupMapper.insert(saPersongroupEntity);
        return this.getEntity(saPersongroupEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saPersongroupPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaPersongroupPojo update(SaPersongroupPojo saPersongroupPojo) {
        SaPersongroupEntity saPersongroupEntity = new SaPersongroupEntity();
        BeanUtils.copyProperties(saPersongroupPojo, saPersongroupEntity);
        this.saPersongroupMapper.update(saPersongroupEntity);
        return this.getEntity(saPersongroupEntity.getId());
    }


    @Override
    public int delete(String key) {
        return this.saPersongroupMapper.delete(key);
    }


}
