package inks.service.sa.crm.service;

import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.pojo.SaDemandresearchitemPojo;
import inks.service.sa.crm.domain.SaDemandresearchitemEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import com.github.pagehelper.PageInfo;
import java.util.List;
/**
 * 需求调研子表(SaDemandresearchitem)表服务接口
 *
 * <AUTHOR>
 * @since 2024-10-11 10:15:15
 */
public interface SaDemandresearchitemService {


    SaDemandresearchitemPojo getEntity(String key);

    PageInfo<SaDemandresearchitemPojo> getPageList(QueryParam queryParam);

    List<SaDemandresearchitemPojo> getList(String Pid);  

    SaDemandresearchitemPojo insert(SaDemandresearchitemPojo saDemandresearchitemPojo);

    SaDemandresearchitemPojo update(SaDemandresearchitemPojo saDemandresearchitempojo);

    int delete(String key);

    SaDemandresearchitemPojo clearNull(SaDemandresearchitemPojo saDemandresearchitempojo);
}
