package inks.service.sa.crm.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.pojo.SaBusinessstagePojo;

import java.util.List;

/**
 * 商机阶段子表(SaBusinessstage)表服务接口
 *
 * <AUTHOR>
 * @since 2024-07-25 16:30:46
 */
public interface SaBusinessstageService {


    SaBusinessstagePojo getEntity(String key);

    PageInfo<SaBusinessstagePojo> getPageList(QueryParam queryParam);

    List<SaBusinessstagePojo> getList(String Pid);

    SaBusinessstagePojo insert(SaBusinessstagePojo saBusinessstagePojo);

    SaBusinessstagePojo update(SaBusinessstagePojo saBusinessstagepojo);

    int delete(String key);

    SaBusinessstagePojo clearNull(SaBusinessstagePojo saBusinessstagepojo);
}
