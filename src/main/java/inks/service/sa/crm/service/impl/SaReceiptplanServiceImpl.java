package inks.service.sa.crm.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.crm.domain.SaReceiptplanEntity;
import inks.service.sa.crm.domain.pojo.SaReceiptplanPojo;
import inks.service.sa.crm.mapper.SaReceiptplanMapper;
import inks.service.sa.crm.service.SaReceiptplanService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 收款计划(SaReceiptplan)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-03-25 11:08:15
 */
@Service("saReceiptplanService")
public class SaReceiptplanServiceImpl implements SaReceiptplanService {
    @Resource
    private SaReceiptplanMapper saReceiptplanMapper;


    @Override
    public SaReceiptplanPojo getEntity(String key) {
        return this.saReceiptplanMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaReceiptplanPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaReceiptplanPojo> lst = saReceiptplanMapper.getPageList(queryParam);
            PageInfo<SaReceiptplanPojo> pageInfo = new PageInfo<SaReceiptplanPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saReceiptplanPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaReceiptplanPojo insert(SaReceiptplanPojo saReceiptplanPojo) {
        //初始化NULL字段
        if (saReceiptplanPojo.getRefno() == null) saReceiptplanPojo.setRefno("");
        if (saReceiptplanPojo.getBilltype() == null) saReceiptplanPojo.setBilltype("");
        if (saReceiptplanPojo.getBilltitle() == null) saReceiptplanPojo.setBilltitle("");
        if (saReceiptplanPojo.getBilldate() == null) saReceiptplanPojo.setBilldate(new Date());
        if (saReceiptplanPojo.getOrderid() == null) saReceiptplanPojo.setOrderid("");
        if (saReceiptplanPojo.getOrderuid() == null) saReceiptplanPojo.setOrderuid("");
        if (saReceiptplanPojo.getGroupid() == null) saReceiptplanPojo.setGroupid("");
        if (saReceiptplanPojo.getBusinessid() == null) saReceiptplanPojo.setBusinessid("");
        if (saReceiptplanPojo.getCustname() == null) saReceiptplanPojo.setCustname("");
        if (saReceiptplanPojo.getAmount() == null) saReceiptplanPojo.setAmount(0D);
        if (saReceiptplanPojo.getPlandate() == null) saReceiptplanPojo.setPlandate(new Date());
        if (saReceiptplanPojo.getOperatorid() == null) saReceiptplanPojo.setOperatorid("");
        if (saReceiptplanPojo.getOperator() == null) saReceiptplanPojo.setOperator("");
        if (saReceiptplanPojo.getFinishamt() == null) saReceiptplanPojo.setFinishamt(0D);
        if (saReceiptplanPojo.getClosed() == null) saReceiptplanPojo.setClosed(0);
        if (saReceiptplanPojo.getRownum() == null) saReceiptplanPojo.setRownum(0);
        if (saReceiptplanPojo.getRemark() == null) saReceiptplanPojo.setRemark("");
        if (saReceiptplanPojo.getCreateby() == null) saReceiptplanPojo.setCreateby("");
        if (saReceiptplanPojo.getCreatebyid() == null) saReceiptplanPojo.setCreatebyid("");
        if (saReceiptplanPojo.getCreatedate() == null) saReceiptplanPojo.setCreatedate(new Date());
        if (saReceiptplanPojo.getLister() == null) saReceiptplanPojo.setLister("");
        if (saReceiptplanPojo.getListerid() == null) saReceiptplanPojo.setListerid("");
        if (saReceiptplanPojo.getModifydate() == null) saReceiptplanPojo.setModifydate(new Date());
        if (saReceiptplanPojo.getAssessor() == null) saReceiptplanPojo.setAssessor("");
        if (saReceiptplanPojo.getAssessorid() == null) saReceiptplanPojo.setAssessorid("");
        if (saReceiptplanPojo.getAssessdate() == null) saReceiptplanPojo.setAssessdate(new Date());
        if (saReceiptplanPojo.getCustom1() == null) saReceiptplanPojo.setCustom1("");
        if (saReceiptplanPojo.getCustom2() == null) saReceiptplanPojo.setCustom2("");
        if (saReceiptplanPojo.getCustom3() == null) saReceiptplanPojo.setCustom3("");
        if (saReceiptplanPojo.getCustom4() == null) saReceiptplanPojo.setCustom4("");
        if (saReceiptplanPojo.getCustom5() == null) saReceiptplanPojo.setCustom5("");
        if (saReceiptplanPojo.getCustom6() == null) saReceiptplanPojo.setCustom6("");
        if (saReceiptplanPojo.getCustom7() == null) saReceiptplanPojo.setCustom7("");
        if (saReceiptplanPojo.getCustom8() == null) saReceiptplanPojo.setCustom8("");
        if (saReceiptplanPojo.getCustom9() == null) saReceiptplanPojo.setCustom9("");
        if (saReceiptplanPojo.getCustom10() == null) saReceiptplanPojo.setCustom10("");
        if (saReceiptplanPojo.getTenantid() == null) saReceiptplanPojo.setTenantid("");
        if (saReceiptplanPojo.getTenantname() == null) saReceiptplanPojo.setTenantname("");
        if (saReceiptplanPojo.getRevision() == null) saReceiptplanPojo.setRevision(0);
        SaReceiptplanEntity saReceiptplanEntity = new SaReceiptplanEntity();
        BeanUtils.copyProperties(saReceiptplanPojo, saReceiptplanEntity);

        saReceiptplanEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saReceiptplanEntity.setRevision(1);  //乐观锁
        this.saReceiptplanMapper.insert(saReceiptplanEntity);
        return this.getEntity(saReceiptplanEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saReceiptplanPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaReceiptplanPojo update(SaReceiptplanPojo saReceiptplanPojo) {
        SaReceiptplanEntity saReceiptplanEntity = new SaReceiptplanEntity();
        BeanUtils.copyProperties(saReceiptplanPojo, saReceiptplanEntity);
        this.saReceiptplanMapper.update(saReceiptplanEntity);
        return this.getEntity(saReceiptplanEntity.getId());
    }


    @Override
    public int delete(String key) {
        return this.saReceiptplanMapper.delete(key);
    }

    /**
     * 审核数据
     *
     * @param saReceiptplanPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public SaReceiptplanPojo approval(SaReceiptplanPojo saReceiptplanPojo) {
        //主表更改
        SaReceiptplanEntity saReceiptplanEntity = new SaReceiptplanEntity();
        BeanUtils.copyProperties(saReceiptplanPojo, saReceiptplanEntity);
        this.saReceiptplanMapper.approval(saReceiptplanEntity);
        //返回Bill实例
        return this.getEntity(saReceiptplanEntity.getId());
    }


    @Override
    @Transactional
    public SaReceiptplanPojo closed(String key, Integer type, LoginUser loginUser) {

        String strType = type == 1 ? "关闭" : "开启";
        SaReceiptplanPojo dbPojo = this.saReceiptplanMapper.getEntity(key);
        if (Objects.equals(dbPojo.getClosed(), type)) {
            throw new RuntimeException(dbPojo.getRefno() + "已" + strType + ",无需操作");
        }
        //主表更改
        SaReceiptplanEntity saReceiptplanEntity = new SaReceiptplanEntity();
        saReceiptplanEntity.setId(key);
        saReceiptplanEntity.setClosed(type);
        saReceiptplanEntity.setLister(loginUser.getRealname());
        saReceiptplanEntity.setListerid(loginUser.getUserid());
        saReceiptplanEntity.setModifydate(new Date());
        saReceiptplanEntity.setTenantid(loginUser.getTenantid());
        this.saReceiptplanMapper.update(saReceiptplanEntity);
        //返回Bill实例
        return this.getEntity(saReceiptplanEntity.getId());
    }
}
