package inks.service.sa.crm.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.pojo.SaBusinessPojo;
import inks.service.sa.crm.domain.pojo.SaBusinessitemdetailPojo;

/**
 * 商机(SaBusiness)表服务接口
 *
 * <AUTHOR>
 * @since 2024-02-20 09:08:46
 */
public interface SaBusinessService {


    SaBusinessPojo getEntity(String key);


    PageInfo<SaBusinessitemdetailPojo> getPageList(QueryParam queryParam);


    SaBusinessPojo getBillEntity(String key);


    PageInfo<SaBusinessPojo> getBillList(QueryParam queryParam);


    PageInfo<SaBusinessPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saBusinessPojo 实例对象
     * @return 实例对象
     */
    SaBusinessPojo insert(SaBusinessPojo saBusinessPojo, LoginUser loginUser);

    /**
     * 修改数据
     *
     * @param saBusinesspojo 实例对象
     * @return 实例对象
     */
    SaBusinessPojo update(SaBusinessPojo saBusinesspojo, LoginUser loginUser);


    int delete(String key, LoginUser loginUser);

    /**
     * 审核数据
     *
     * @param saBusinessPojo 实例对象
     * @return 实例对象
     */
    SaBusinessPojo approval(SaBusinessPojo saBusinessPojo);

    void updateOaflowmark(SaBusinessPojo billEntity);
}
