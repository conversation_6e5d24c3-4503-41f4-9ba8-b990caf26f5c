package inks.service.sa.crm.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.DateUtils;
import inks.service.sa.crm.mapper.S07MBIR1Mapper;
import inks.service.sa.crm.service.S07MBIR1Service;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class S07MBIR1ServiceImpl implements S07MBIR1Service {

    @Resource
    private S07MBIR1Mapper s07MBIR1Mapper;

    @Override
    public Map<String, Object> getNewCountThisMonth(QueryParam queryParam) {
        return s07MBIR1Mapper.getNewCountThisMonth(queryParam);
    }

    @Override
    public Map<String, Object> getNewCountAndAmtThisMonth(QueryParam queryParam, String userid) {
        Map<String, Object> result = new HashMap<>();
        result.put("customercount", 0);
        // 时间范围内负责人是我的【所有线索】
        List<String> leadidsBetweenDate = s07MBIR1Mapper.getLeadIdsByPrincipalid(queryParam, userid);
        result.put("leadcount", leadidsBetweenDate.size());
        // 我的所有线索
        List<String> allLeadids = s07MBIR1Mapper.getAllLeadIdsByPrincipalid(queryParam, userid);
        // 时间范围内我的线索转为的【所有客户】
        List<String> allGroupids = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(allLeadids)) {
            List<String> customeridsBetweenDate = s07MBIR1Mapper.getCustomerIdsByUserid(queryParam, allLeadids);
            result.put("customercount", customeridsBetweenDate.size());
            allGroupids = s07MBIR1Mapper.getAllCustomerIdsByUserid(queryParam, allLeadids);
        }

        Map<String, Object> newCountAndAmtThisMonth = s07MBIR1Mapper.getNewCountAndAmtThisMonth(queryParam, allGroupids);
        result.putAll(newCountAndAmtThisMonth);
        return result;
    }

    @Override
    public List<Map<String, Object>> getNewCountEveryDay(QueryParam queryParam, Integer trend) {
        // 获取日期范围内(startDate-endDate)时间列表List<String> dateListInRange: "2021-01-01","2021-01-02","2021-01-03"...
        Date startDate = queryParam.getDateRange().getStartDate();
        Date endDate = queryParam.getDateRange().getEndDate();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date currentDate = startDate;
        List<String> dateListInRange = new ArrayList<>();
        while (!currentDate.after(endDate)) {
            dateListInRange.add(simpleDateFormat.format(currentDate));
            currentDate = DateUtils.addDays(currentDate, 1);
        }


//        SELECT
//        DATE(c.CreateDate)    AS createdate,
//        COUNT(DISTINCT c.Id)  AS customer, 每天新增客户数
//        COUNT(DISTINCT l.Id)  AS leads, 每天新增线索数
//        COUNT(DISTINCT b.Id)  AS business,  每天新增商机数
//        COUNT(DISTINCT f.Id)  AS followview, 每天新增跟进数
//        COUNT(DISTINCT ct.Id) AS contract 每天新增合同数
        List<Map<String, Object>> newCountEveryDay = s07MBIR1Mapper.getNewCountEveryDay(queryParam);
        // 统计值，趋势时为累加，普通时常态为0.0
        double customer = 0.0;
        double leads = 0.0;
        double business = 0.0;
        double followview = 0.0;
        double contract = 0.0;
        // 最终返回结果
        List<Map<String, Object>> newCountEveryDayList = new ArrayList<>();
        for (String dateString : dateListInRange) {
            HashMap<String, Object> map = new HashMap<>();
            map.put("date", dateString);
            map.put("customer", 0.0);
            map.put("leads", 0.0);
            map.put("business", 0.0);
            map.put("followview", 0.0);
            map.put("contract", 0.0);
            for (Map<String, Object> countEveryDay : newCountEveryDay) {
                if (countEveryDay != null) {
                    String createdate = simpleDateFormat.format(countEveryDay.get("createdate"));
                    double mapCustomer = ((Long) countEveryDay.get("customer")).doubleValue();
                    double mapLeads = ((Long) countEveryDay.get("leads")).doubleValue();
                    double mapBusiness = ((Long) countEveryDay.get("business")).doubleValue();
                    double mapFollowview = ((Long) countEveryDay.get("followview")).doubleValue();
                    double mapContract = ((Long) countEveryDay.get("contract")).doubleValue();

                    if (createdate.equals(dateString)) {
                        if (Objects.equals(trend, 1)) {
                            customer += mapCustomer;
                            leads += mapLeads;
                            business += mapBusiness;
                            followview += mapFollowview;
                            contract += mapContract;
                        } else {
                            customer = mapCustomer;
                            leads = mapLeads;
                            business = mapBusiness;
                            followview = mapFollowview;
                            contract = mapContract;
                        }
                        map.put("customer", customer);
                        map.put("leads", leads);
                        map.put("business", business);
                        map.put("followview", followview);
                        map.put("contract", contract);
                        break;
                    }
                }
            }
            newCountEveryDayList.add(map);
        }

        return newCountEveryDayList;
    }


    @Override
    public List<Map<String, Object>> getCustomerType(String qpfilter) {
        return s07MBIR1Mapper.getCustomerType(qpfilter);
    }

    @Override
    public List<Map<String, Object>> getCustomerClass() {
        return s07MBIR1Mapper.getCustomerClass();
    }


    @Override
    public PageInfo<Map<String, Object>> getBusinessInfo(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<Map<String, Object>> lst = s07MBIR1Mapper.getBusinessInfo(queryParam);
            PageInfo<Map<String, Object>> pageInfo = new PageInfo<>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        }
    }
    
    @Override
    public Map<String, Object> getContractStatistics(String groupid) {
        return s07MBIR1Mapper.getContractStatistics(groupid);
    }
}
