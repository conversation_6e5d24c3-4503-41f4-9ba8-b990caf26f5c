//package inks.service.sa.crm.service.impl;
//
//
//import inks.common.core.constant.UserConstants;
//import inks.common.core.domain.LoginUser;
//import inks.common.core.exception.BaseBusinessException;
//import inks.common.core.utils.AESUtil;
//import inks.common.core.utils.StringUtils;
//import inks.common.redis.service.RedisService;
//import inks.service.sa.crm.domain.pojo.SaDmsuserPojo;
//import inks.service.sa.crm.mapper.SysDmsLogingMapper;
//import inks.sa.common.core.service.SaRedisService;
//import inks.service.sa.crm.service.SysDmsLogingService;
//import org.springframework.stereotype.Service;
//
//import javax.annotation.Resource;
//import javax.servlet.http.HttpServletRequest;
//import java.text.ParseException;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
//@Service
//public class SysDmsLoginServiceImpl implements SysDmsLogingService {
//
//    @Resource
//    private SysDmsLogingMapper sysDmsLogingMapper;
//    @Resource
//    private SaRedisService saRedisService;
//    private final String SCANLOGIN_CODE = "scanlogin_code:";//扫码登录
//
//    @Override
//    public LoginUser login(String username, String password, HttpServletRequest request) throws ParseException, Exception {
//        //构建登陆日志对象
////        CiLoginLogEntity ciLoginLogEntity = new CiLoginLogEntity();
////        ciLoginLogEntity.setLoginTime(BillCodeUtil.newDate());
////        ciLoginLogEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
////        Map<String, String> map = UserAgentUtil.getOs(request.getHeader("User-Agent"));
////        ciLoginLogEntity.setBrowserName(map.get("browser"));
////        ciLoginLogEntity.setHostSystem(map.get("os"));
////        String ip = UserAgentUtil.getIpAddr(request);
////        String address = UserAgentUtil.getRealAddressByIP(ip);
////        ciLoginLogEntity.setIpAddr(ip);
////        ciLoginLogEntity.setLoginLocation(address);
//
//        if (StringUtils.isAnyBlank(username, password)) {
//            throw new BaseBusinessException("用户/密码必须填写");
//        }
//        if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
//                || password.length() > UserConstants.PASSWORD_MAX_LENGTH) {
//            throw new BaseBusinessException("用户密码不在指定范围");
//        }
//        if (username.length() < UserConstants.USERNAME_MIN_LENGTH
//                || username.length() > UserConstants.USERNAME_MAX_LENGTH) {
//            throw new BaseBusinessException("用户名不在指定范围");
//        }
//        password = AESUtil.Encrypt(password);
//        Integer count = sysDmsLogingMapper.count(username);
//        if (count < 1) {
////            ciLoginLogEntity.setUserid("");
////            ciLoginLogEntity.setUserName(username);
////            ciLoginLogEntity.setRealName("");
////            ciLoginLogEntity.setLoginStatus(1L);
////            ciLoginLogEntity.setDirection("登录");
////            ciLoginLogEntity.setLoginMsg("Dms用户：" + username + "登录失败,登录时间："
////                    + BillCodeUtil.forMatDate(ciLoginLogEntity.getLoginTime()) + ",原因：账号不存在" + ",登录系统："
////                    + ciLoginLogEntity.getHostSystem() + ",操作浏览器："
////                    + ciLoginLogEntity.getBrowserName() + ",登录地址："
////                    + ciLoginLogEntity.getLoginLocation());
////            ciLoginLogMapper.insert(ciLoginLogEntity);
//            throw new BaseBusinessException("账号不存在");
//        }
//        LoginUser loginUser = sysDmsLogingMapper.login(username, password);
////        if (loginUser == null) {
////            ciLoginLogEntity.setUserid("");
////            ciLoginLogEntity.setUserName(username);
////            ciLoginLogEntity.setRealName("");
////            ciLoginLogEntity.setLoginStatus(1L);
////            ciLoginLogEntity.setDirection("登录");
////            ciLoginLogEntity.setLoginMsg("Dms用户：" + username + "登录失败,登录时间："
////                    + BillCodeUtil.forMatDate(ciLoginLogEntity.getLoginTime()) + ",原因：密码错误" + ",登录系统："
////                    + ciLoginLogEntity.getHostSystem() + ",操作浏览器："
////                    + ciLoginLogEntity.getBrowserName() + ",登录地址："
////                    + ciLoginLogEntity.getLoginLocation());
////            ciLoginLogMapper.insert(ciLoginLogEntity);
////            throw new BaseBusinessException("账号密码错误");
////        }
////        ciLoginLogEntity.setUserid(loginUser.getUserid());
////        ciLoginLogEntity.setUserName(username);
////        ciLoginLogEntity.setRealName(loginUser.getRealname());
////        ciLoginLogEntity.setDirection("登录");
////        ciLoginLogEntity.setLoginStatus(0L);
////        ciLoginLogEntity.setLoginMsg("Dms用户：" + username + "登录成功,登录时间："
////                + BillCodeUtil.forMatDate(ciLoginLogEntity.getLoginTime()) + ",登录系统："
////                + ciLoginLogEntity.getHostSystem() + ",操作浏览器："
////                + ciLoginLogEntity.getBrowserName() + ",登录地址："
////                + ciLoginLogEntity.getLoginLocation());
////        ciLoginLogMapper.insert(ciLoginLogEntity);
////        loginUser.setLogid(ciLoginLogEntity.getId());
//        return loginUser;
//    }
//
//    @Override
//    public void record(LoginUser loginUser, HttpServletRequest request) throws ParseException {
////        CiLoginLogEntity ciLoginLogEntity = new CiLoginLogEntity();
////        ciLoginLogEntity.setLoginTime(BillCodeUtil.newDate());
////        ciLoginLogEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
////        Map<String, String> map = UserAgentUtil.getOs(request.getHeader("User-Agent"));
////        ciLoginLogEntity.setBrowserName(map.get("browser"));
////        ciLoginLogEntity.setHostSystem(map.get("os"));
////        ciLoginLogEntity.setIpAddr(UserAgentUtil.getIpAddr(request));
////        ciLoginLogEntity.setLoginLocation(AddressUtils.getRealAddressByIP(UserAgentUtil.getIpAddr(request)));
////        ciLoginLogEntity.setUserid(loginUser.getUserid());
////        ciLoginLogEntity.setUserName(loginUser.getUsername());
////        ciLoginLogEntity.setRealName(loginUser.getRealname());
////        ciLoginLogEntity.setDirection("登出");
////        ciLoginLogEntity.setLoginStatus(1L);
////        ciLoginLogEntity.setLoginMsg("Dms用户：" + loginUser.getUsername() + "退出系统,退出时间："
////                + BillCodeUtil.forMatDate(ciLoginLogEntity.getLoginTime()) + ",操作系统："
////                + ciLoginLogEntity.getHostSystem() + ",操作浏览器："
////                + ciLoginLogEntity.getBrowserName() + ",操作地址："
////                + ciLoginLogEntity.getLoginLocation());
////        ciLoginLogMapper.insert(ciLoginLogEntity);
//    }
//
//    /**
//     * @return Map<Object>
//     * @Description
//     * <AUTHOR>
//     * @param[1] openid  前端传过来的openid
//     * @param[2] key redis中的key
//     * @param[3] request 读取ip
//     * @time 2023/4/21 22:08
//     */
//    @Override
//    public LoginUser scanLogin(String openid, String key, HttpServletRequest request) throws ParseException {
//        //构建登陆日志对象
////        CiLoginLogEntity ciLoginLogEntity = new CiLoginLogEntity();
////        ciLoginLogEntity.setLoginTime(BillCodeUtil.newDate());
////        ciLoginLogEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
////        Map<String, String> map = UserAgentUtil.getOs(request.getHeader("User-Agent"));
////        ciLoginLogEntity.setBrowserName(map.get("browser"));
////        ciLoginLogEntity.setHostSystem(map.get("os"));
////        String ip = UserAgentUtil.getIpAddr(request);
////        String address = UserAgentUtil.getRealAddressByIP(ip);
////        ciLoginLogEntity.setIpAddr(ip);
////        ciLoginLogEntity.setLoginLocation(address);
//
//
//       //开始扫码登录(dms直接传入了openid)
//      //通过openid获取是否有用户信息
//        List<SaDmsuserPojo> pidmsuserPojoList = sysDmsLogingMapper.getListByOpenid(openid);
////        List<String> tidList = pidmsuserPojoList.stream()
////                .map(SaDmsuserPojo::getTenantid)
////                .collect(Collectors.toList());
//        if (pidmsuserPojoList != null) {
//            //登录成功-日志
////            ciLoginLogEntity.setUserid(piadmin.getAdminid());
////            ciLoginLogEntity.setUserName(piadmin.getUsername());
////            ciLoginLogEntity.setRealName(piadmin.getRealname());
////            ciLoginLogEntity.setDirection("登录");
////            ciLoginLogEntity.setLoginStatus(0L);
////            ciLoginLogEntity.setLoginMsg("管理员：" + piadmin.getUsername() + "登录成功,登录时间："
////                    + BillCodeUtil.forMatDate(ciLoginLogEntity.getLoginTime()) + ",登录系统："
////                    + ciLoginLogEntity.getHostSystem() + ",操作浏览器："
////                    + ciLoginLogEntity.getBrowserName() + ",登录地址："
////                    + ciLoginLogEntity.getLoginLocation());
////            ciLoginLogMapper.insert(ciLoginLogEntity);
//        } else {
//            //piadmin为空，说明用户openid不在PiAdmin表中
//            Map<String, Object> missionMsg = new HashMap<>();
//            missionMsg.put("code", "403");
//            missionMsg.put("msg", "openid下无绑定租户");
//            this.saRedisService.setCacheMapValue(SCANLOGIN_CODE, key, missionMsg);
//            saRedisService.expire(SCANLOGIN_CODE, 60 * 6);
////            //登录失败-日志
////            ciLoginLogEntity.setUserid("");
////            //ciLoginLogEntity.setUserName(username);
////            ciLoginLogEntity.setRealName("");
////            ciLoginLogEntity.setLoginStatus(1L);
////            ciLoginLogEntity.setDirection("登录");
////            ciLoginLogEntity.setLoginMsg("openid:" + openid + "登录失败,登录时间："
////                    + BillCodeUtil.forMatDate(ciLoginLogEntity.getLoginTime()) + ",原因：openid下无绑定租户" + ",登录系统："
////                    + ciLoginLogEntity.getHostSystem() + ",操作浏览器："
////                    + ciLoginLogEntity.getBrowserName() + ",登录地址："
////                    + ciLoginLogEntity.getLoginLocation());
////            ciLoginLogMapper.insert(ciLoginLogEntity);
//            throw new BaseBusinessException("openid不存在");
//        }
//        LoginUser loginUser = new LoginUser();
//        loginUser.setUserid(pidmsuserPojoList.get(0).getUserid());
//        loginUser.setUsername(pidmsuserPojoList.get(0).getUsername());
//        loginUser.setRealname(pidmsuserPojoList.get(0).getRealname());
//        return loginUser;
//    }
//}
