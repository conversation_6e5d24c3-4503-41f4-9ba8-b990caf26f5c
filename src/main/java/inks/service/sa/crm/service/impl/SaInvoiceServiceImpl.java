package inks.service.sa.crm.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.crm.domain.SaInvoiceEntity;
import inks.service.sa.crm.domain.pojo.SaInvoicePojo;
import inks.service.sa.crm.mapper.SaInvoiceMapper;
import inks.service.sa.crm.service.SaInvoiceService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 发票管理(SaInvoice)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-03-25 16:27:48
 */
@Service("saInvoiceService")
public class SaInvoiceServiceImpl implements SaInvoiceService {
    @Resource
    private SaInvoiceMapper saInvoiceMapper;


    @Override
    public SaInvoicePojo getEntity(String key) {
        return this.saInvoiceMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaInvoicePojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaInvoicePojo> lst = saInvoiceMapper.getPageList(queryParam);
            PageInfo<SaInvoicePojo> pageInfo = new PageInfo<SaInvoicePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saInvoicePojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaInvoicePojo insert(SaInvoicePojo saInvoicePojo) {
        //初始化NULL字段
        if (saInvoicePojo.getRefno() == null) saInvoicePojo.setRefno("");
        if (saInvoicePojo.getBilltype() == null) saInvoicePojo.setBilltype("");
        if (saInvoicePojo.getBilltitle() == null) saInvoicePojo.setBilltitle("");
        if (saInvoicePojo.getBilldate() == null) saInvoicePojo.setBilldate(new Date());
        if (saInvoicePojo.getOrderid() == null) saInvoicePojo.setOrderid("");
        if (saInvoicePojo.getOrderuid() == null) saInvoicePojo.setOrderuid("");
        if(saInvoicePojo.getBusinessid()==null) saInvoicePojo.setBusinessid("");
        if(saInvoicePojo.getReceiptid()==null) saInvoicePojo.setReceiptid("");
        if (saInvoicePojo.getInvotype() == null) saInvoicePojo.setInvotype("");
        if (saInvoicePojo.getInvocode() == null) saInvoicePojo.setInvocode("");
        if (saInvoicePojo.getInvodate() == null) saInvoicePojo.setInvodate(new Date());
        if (saInvoicePojo.getGroupid() == null) saInvoicePojo.setGroupid("");
        if (saInvoicePojo.getCustname() == null) saInvoicePojo.setCustname("");
        if (saInvoicePojo.getAmount() == null) saInvoicePojo.setAmount(0D);
        if (saInvoicePojo.getContact() == null) saInvoicePojo.setContact("");
        if (saInvoicePojo.getContactway() == null) saInvoicePojo.setContactway("");
        if (saInvoicePojo.getAddress() == null) saInvoicePojo.setAddress("");
        if (saInvoicePojo.getOperatorid() == null) saInvoicePojo.setOperatorid("");
        if (saInvoicePojo.getOperator() == null) saInvoicePojo.setOperator("");
        if (saInvoicePojo.getRownum() == null) saInvoicePojo.setRownum(0);
        if (saInvoicePojo.getRemark() == null) saInvoicePojo.setRemark("");
        if (saInvoicePojo.getCreateby() == null) saInvoicePojo.setCreateby("");
        if (saInvoicePojo.getCreatebyid() == null) saInvoicePojo.setCreatebyid("");
        if (saInvoicePojo.getCreatedate() == null) saInvoicePojo.setCreatedate(new Date());
        if (saInvoicePojo.getLister() == null) saInvoicePojo.setLister("");
        if (saInvoicePojo.getListerid() == null) saInvoicePojo.setListerid("");
        if (saInvoicePojo.getModifydate() == null) saInvoicePojo.setModifydate(new Date());
        if (saInvoicePojo.getAssessor() == null) saInvoicePojo.setAssessor("");
        if (saInvoicePojo.getAssessorid() == null) saInvoicePojo.setAssessorid("");
        if (saInvoicePojo.getAssessdate() == null) saInvoicePojo.setAssessdate(new Date());
        if (saInvoicePojo.getCustom1() == null) saInvoicePojo.setCustom1("");
        if (saInvoicePojo.getCustom2() == null) saInvoicePojo.setCustom2("");
        if (saInvoicePojo.getCustom3() == null) saInvoicePojo.setCustom3("");
        if (saInvoicePojo.getCustom4() == null) saInvoicePojo.setCustom4("");
        if (saInvoicePojo.getCustom5() == null) saInvoicePojo.setCustom5("");
        if (saInvoicePojo.getCustom6() == null) saInvoicePojo.setCustom6("");
        if (saInvoicePojo.getCustom7() == null) saInvoicePojo.setCustom7("");
        if (saInvoicePojo.getCustom8() == null) saInvoicePojo.setCustom8("");
        if (saInvoicePojo.getCustom9() == null) saInvoicePojo.setCustom9("");
        if (saInvoicePojo.getCustom10() == null) saInvoicePojo.setCustom10("");
        if (saInvoicePojo.getTenantid() == null) saInvoicePojo.setTenantid("");
        if (saInvoicePojo.getTenantname() == null) saInvoicePojo.setTenantname("");
        if (saInvoicePojo.getRevision() == null) saInvoicePojo.setRevision(0);
        SaInvoiceEntity saInvoiceEntity = new SaInvoiceEntity();
        BeanUtils.copyProperties(saInvoicePojo, saInvoiceEntity);

        saInvoiceEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saInvoiceEntity.setRevision(1);  //乐观锁
        this.saInvoiceMapper.insert(saInvoiceEntity);
        return this.getEntity(saInvoiceEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saInvoicePojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaInvoicePojo update(SaInvoicePojo saInvoicePojo) {
        SaInvoiceEntity saInvoiceEntity = new SaInvoiceEntity();
        BeanUtils.copyProperties(saInvoicePojo, saInvoiceEntity);
        this.saInvoiceMapper.update(saInvoiceEntity);
        return this.getEntity(saInvoiceEntity.getId());
    }


    @Override
    public int delete(String key) {
        return this.saInvoiceMapper.delete(key);
    }

    /**
     * 审核数据
     *
     * @param saInvoicePojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public SaInvoicePojo approval(SaInvoicePojo saInvoicePojo) {
        //主表更改
        SaInvoiceEntity saInvoiceEntity = new SaInvoiceEntity();
        BeanUtils.copyProperties(saInvoicePojo, saInvoiceEntity);
        this.saInvoiceMapper.approval(saInvoiceEntity);
        //返回Bill实例
        return this.getEntity(saInvoiceEntity.getId());
    }

}
