package inks.service.sa.crm.service;


import inks.common.core.domain.LoginUser;

import javax.servlet.http.HttpServletRequest;
import java.text.ParseException;

/**
 * <AUTHOR>
 * @date 2023年04月06日 16:54
 */
public interface SysDmsLogingService {
    LoginUser login(String username, String password, HttpServletRequest request) throws Exception;

    void record(LoginUser loginUser, HttpServletRequest request) throws ParseException;

    LoginUser scanLogin(String openid, String key, HttpServletRequest request) throws ParseException;
}
