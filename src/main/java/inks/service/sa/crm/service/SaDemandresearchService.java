package inks.service.sa.crm.service;

import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.pojo.SaDemandresearchPojo;
import inks.service.sa.crm.domain.pojo.SaDemandresearchitemdetailPojo;
import inks.service.sa.crm.domain.SaDemandresearchEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import com.github.pagehelper.PageInfo;

/**
 * 需求调研(SaDemandresearch)表服务接口
 *
 * <AUTHOR>
 * @since 2024-10-11 10:15:06
 */
public interface SaDemandresearchService {


    SaDemandresearchPojo getEntity(String key);

    PageInfo<SaDemandresearchitemdetailPojo> getPageList(QueryParam queryParam);

    SaDemandresearchPojo getBillEntity(String key);

    PageInfo<SaDemandresearchPojo> getBillList(QueryParam queryParam);

    PageInfo<SaDemandresearchPojo> getPageTh(QueryParam queryParam);

    SaDemandresearchPojo insert(SaDemandresearchPojo saDemandresearchPojo);

    SaDemandresearchPojo update(SaDemandresearchPojo saDemandresearchpojo);

    int delete(String key);


     SaDemandresearchPojo approval(SaDemandresearchPojo saDemandresearchPojo);
}
