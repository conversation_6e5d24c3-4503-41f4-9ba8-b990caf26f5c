package inks.service.sa.crm.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.pojo.SaContractPojo;
import inks.service.sa.crm.domain.pojo.SaContractitemdetailPojo;

/**
 * 合同管理(SaContract)表服务接口
 *
 * <AUTHOR>
 * @since 2023-03-06 12:58:34
 */
public interface SaContractService {


    SaContractPojo getEntity(String key);


    PageInfo<SaContractitemdetailPojo> getPageList(QueryParam queryParam);


    SaContractPojo getBillEntity(String key);


    PageInfo<SaContractPojo> getBillList(QueryParam queryParam);


    PageInfo<SaContractPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saContractPojo 实例对象
     * @return 实例对象
     */
    SaContractPojo insert(SaContractPojo saContractPojo, LoginUser loginUser);

    /**
     * 修改数据
     *
     * @param saContractpojo 实例对象
     * @return 实例对象
     */
    SaContractPojo update(SaContractPojo saContractpojo, LoginUser loginUser);


    int delete(String key, LoginUser loginUser);

    /**
     * 审核数据
     *
     * @param saContractPojo 实例对象
     * @return 实例对象
     */
    SaContractPojo approval(SaContractPojo saContractPojo);

    //SaContractPojo toContract(SaContractPojo saContractPojo);
}
