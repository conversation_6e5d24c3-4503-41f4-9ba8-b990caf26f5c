package inks.service.sa.crm.service;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.pojo.SaReceiptplanPojo;
import inks.service.sa.crm.domain.pojo.SaSmpdeliPojo;
import inks.service.sa.crm.domain.pojo.SaSmpdeliitemPojo;
import inks.service.sa.crm.domain.pojo.SaSmpdeliitemdetailPojo;
import inks.service.sa.crm.domain.SaSmpdeliEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * 样品发货(SaSmpdeli)表服务接口
 *
 * <AUTHOR>
 * @since 2024-09-03 16:20:28
 */
public interface SaSmpdeliService {


    SaSmpdeliPojo getEntity(String key);

    PageInfo<SaSmpdeliitemdetailPojo> getPageList(QueryParam queryParam);

    SaSmpdeliPojo getBillEntity(String key);

    PageInfo<SaSmpdeliPojo> getBillList(QueryParam queryParam);

    PageInfo<SaSmpdeliPojo> getPageTh(QueryParam queryParam);

    SaSmpdeliPojo insert(SaSmpdeliPojo saSmpdeliPojo);

    SaSmpdeliPojo update(SaSmpdeliPojo saSmpdelipojo);

    int delete(String key);


     SaSmpdeliPojo approval(SaSmpdeliPojo saSmpdeliPojo);

    SaSmpdeliPojo closed(List<SaSmpdeliitemPojo> lst, Integer type, LoginUser loginUser);

    SaSmpdeliPojo disannul(List<SaSmpdeliitemPojo> lst, Integer type, LoginUser loginUser);
}
