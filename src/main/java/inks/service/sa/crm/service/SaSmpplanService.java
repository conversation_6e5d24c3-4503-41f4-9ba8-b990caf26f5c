package inks.service.sa.crm.service;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.pojo.SaSmpplanPojo;
import inks.service.sa.crm.domain.pojo.SaSmpplanitemPojo;
import inks.service.sa.crm.domain.pojo.SaSmpplanitemdetailPojo;
import inks.service.sa.crm.domain.SaSmpplanEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * 样品需求(SaSmpplan)表服务接口
 *
 * <AUTHOR>
 * @since 2024-09-03 15:17:54
 */
public interface SaSmpplanService {


    SaSmpplanPojo getEntity(String key);

    PageInfo<SaSmpplanitemdetailPojo> getPageList(QueryParam queryParam);

    SaSmpplanPojo getBillEntity(String key);

    PageInfo<SaSmpplanPojo> getBillList(QueryParam queryParam);

    PageInfo<SaSmpplanPojo> getPageTh(QueryParam queryParam);

    SaSmpplanPojo insert(SaSmpplanPojo saSmpplanPojo);

    SaSmpplanPojo update(SaSmpplanPojo saSmpplanpojo);

    int delete(String key);


     SaSmpplanPojo approval(SaSmpplanPojo saSmpplanPojo);

    SaSmpplanPojo closed(List<SaSmpplanitemPojo> lst, Integer type, LoginUser loginUser);

    void updateOaflowmark(SaSmpplanPojo billEntity);
}
