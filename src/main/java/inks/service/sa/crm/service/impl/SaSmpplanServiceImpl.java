package inks.service.sa.crm.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.crm.domain.SaSmpplanEntity;
import inks.service.sa.crm.domain.SaSmpplanitemEntity;
import inks.service.sa.crm.domain.pojo.SaSmpplanPojo;
import inks.service.sa.crm.domain.pojo.SaSmpplanitemPojo;
import inks.service.sa.crm.domain.pojo.SaSmpplanitemdetailPojo;
import inks.service.sa.crm.mapper.SaSmpplanMapper;
import inks.service.sa.crm.mapper.SaSmpplanitemMapper;
import inks.service.sa.crm.service.SaSmpplanService;
import inks.service.sa.crm.service.SaSmpplanitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 样品需求(SaSmpplan)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-09-03 15:17:54
 */
@Service("saSmpplanService")
public class SaSmpplanServiceImpl implements SaSmpplanService {
    @Resource
    private SaSmpplanMapper saSmpplanMapper;

    @Resource
    private SaSmpplanitemMapper saSmpplanitemMapper;


    @Resource
    private SaSmpplanitemService saSmpplanitemService;


    @Override
    public SaSmpplanPojo getEntity(String key) {
        return this.saSmpplanMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaSmpplanitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaSmpplanitemdetailPojo> lst = saSmpplanMapper.getPageList(queryParam);
            PageInfo<SaSmpplanitemdetailPojo> pageInfo = new PageInfo<SaSmpplanitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public SaSmpplanPojo getBillEntity(String key) {
        try {
            //读取主表
            SaSmpplanPojo saSmpplanPojo = this.saSmpplanMapper.getEntity(key);
            //查询某个 Businessid（如一个商机 ID）在 当前时间之前 ，已经创建过多少条记录，然后 加 1 ，表示“这是第几次转到样品需求”。
            int busToSmpTimes = saSmpplanMapper.getBusToSmpTimes(saSmpplanPojo.getBusinessid(), saSmpplanPojo.getCreatedate());
            saSmpplanPojo.setBustosmptimes(busToSmpTimes);
            //读取子表
            saSmpplanPojo.setItem(saSmpplanitemMapper.getList(saSmpplanPojo.getId()));
            return saSmpplanPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public PageInfo<SaSmpplanPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaSmpplanPojo> lst = saSmpplanMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(saSmpplanitemMapper.getList(lst.get(i).getId()));
            }
            PageInfo<SaSmpplanPojo> pageInfo = new PageInfo<SaSmpplanPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public PageInfo<SaSmpplanPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaSmpplanPojo> lst = saSmpplanMapper.getPageTh(queryParam);
            PageInfo<SaSmpplanPojo> pageInfo = new PageInfo<SaSmpplanPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    @Transactional
    public SaSmpplanPojo insert(SaSmpplanPojo saSmpplanPojo) {
        //初始化NULL字段
        cleanNull(saSmpplanPojo);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        SaSmpplanEntity saSmpplanEntity = new SaSmpplanEntity();
        BeanUtils.copyProperties(saSmpplanPojo, saSmpplanEntity);
        //设置id和新建日期
        saSmpplanEntity.setId(id);
        saSmpplanEntity.setRevision(1);  //乐观锁
        //插入主表
        this.saSmpplanMapper.insert(saSmpplanEntity);
        //Item子表处理
        List<SaSmpplanitemPojo> lst = saSmpplanPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                //初始化item的NULL
                SaSmpplanitemPojo itemPojo = this.saSmpplanitemService.clearNull(lst.get(i));
                SaSmpplanitemEntity saSmpplanitemEntity = new SaSmpplanitemEntity();
                BeanUtils.copyProperties(itemPojo, saSmpplanitemEntity);
                //设置id和Pid
                saSmpplanitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                saSmpplanitemEntity.setPid(id);
                saSmpplanitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.saSmpplanitemMapper.insert(saSmpplanitemEntity);
            }
        }
        //返回Bill实例
        return this.getBillEntity(saSmpplanEntity.getId());
    }


    @Override
    @Transactional
    public SaSmpplanPojo update(SaSmpplanPojo saSmpplanPojo) {
        //主表更改
        SaSmpplanEntity saSmpplanEntity = new SaSmpplanEntity();
        BeanUtils.copyProperties(saSmpplanPojo, saSmpplanEntity);
        this.saSmpplanMapper.update(saSmpplanEntity);
        if (saSmpplanPojo.getItem() != null) {
            //Item子表处理
            List<SaSmpplanitemPojo> lst = saSmpplanPojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = saSmpplanMapper.getDelItemIds(saSmpplanPojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (int i = 0; i < lstDelIds.size(); i++) {
                    this.saSmpplanitemMapper.delete(lstDelIds.get(i));
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (int i = 0; i < lst.size(); i++) {
                    SaSmpplanitemEntity saSmpplanitemEntity = new SaSmpplanitemEntity();
                    if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null) {
                        //初始化item的NULL
                        SaSmpplanitemPojo itemPojo = this.saSmpplanitemService.clearNull(lst.get(i));
                        BeanUtils.copyProperties(itemPojo, saSmpplanitemEntity);
                        //设置id和Pid
                        saSmpplanitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        saSmpplanitemEntity.setPid(saSmpplanEntity.getId());  // 主表 id
                        saSmpplanitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.saSmpplanitemMapper.insert(saSmpplanitemEntity);
                    } else {
                        BeanUtils.copyProperties(lst.get(i), saSmpplanitemEntity);
                        this.saSmpplanitemMapper.update(saSmpplanitemEntity);
                    }
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(saSmpplanEntity.getId());
    }


    @Override
    @Transactional
    public int delete(String key) {
        SaSmpplanPojo saSmpplanPojo = this.getBillEntity(key);
        //Item子表处理
        List<SaSmpplanitemPojo> lst = saSmpplanPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (int i = 0; i < lst.size(); i++) {
                this.saSmpplanitemMapper.delete(lst.get(i).getId());
            }
        }
        return this.saSmpplanMapper.delete(key);
    }


    @Override
    @Transactional
    public SaSmpplanPojo approval(SaSmpplanPojo saSmpplanPojo) {
        //主表更改
        SaSmpplanEntity saSmpplanEntity = new SaSmpplanEntity();
        BeanUtils.copyProperties(saSmpplanPojo, saSmpplanEntity);
        this.saSmpplanMapper.approval(saSmpplanEntity);
        //返回Bill实例
        return this.getBillEntity(saSmpplanEntity.getId());
    }

    private static void cleanNull(SaSmpplanPojo saSmpplanPojo) {
        if (saSmpplanPojo.getRefno() == null) saSmpplanPojo.setRefno("");
        if (saSmpplanPojo.getBilltype() == null) saSmpplanPojo.setBilltype("");
        if (saSmpplanPojo.getBilldate() == null) saSmpplanPojo.setBilldate(new Date());
        if (saSmpplanPojo.getBilltitle() == null) saSmpplanPojo.setBilltitle("");
        if (saSmpplanPojo.getGroupid() == null) saSmpplanPojo.setGroupid("");
        if (saSmpplanPojo.getBusinessid() == null) saSmpplanPojo.setBusinessid("");
        if (saSmpplanPojo.getOperator() == null) saSmpplanPojo.setOperator("");
        if(saSmpplanPojo.getOperatorid()==null) saSmpplanPojo.setOperatorid("");
        if(saSmpplanPojo.getSummary()==null) saSmpplanPojo.setSummary("");
        if (saSmpplanPojo.getCreateby() == null) saSmpplanPojo.setCreateby("");
        if (saSmpplanPojo.getCreatebyid() == null) saSmpplanPojo.setCreatebyid("");
        if (saSmpplanPojo.getCreatedate() == null) saSmpplanPojo.setCreatedate(new Date());
        if (saSmpplanPojo.getLister() == null) saSmpplanPojo.setLister("");
        if (saSmpplanPojo.getListerid() == null) saSmpplanPojo.setListerid("");
        if (saSmpplanPojo.getModifydate() == null) saSmpplanPojo.setModifydate(new Date());
        if (saSmpplanPojo.getAssessor() == null) saSmpplanPojo.setAssessor("");
        if (saSmpplanPojo.getAssessorid() == null) saSmpplanPojo.setAssessorid("");
        if (saSmpplanPojo.getAssessdate() == null) saSmpplanPojo.setAssessdate(new Date());
        if (saSmpplanPojo.getBillstatecode() == null) saSmpplanPojo.setBillstatecode("");
        if (saSmpplanPojo.getBillstatedate() == null) saSmpplanPojo.setBillstatedate(new Date());
        if (saSmpplanPojo.getBillstartdate() == null) saSmpplanPojo.setBillstartdate(new Date());
        if (saSmpplanPojo.getBillplandate() == null) saSmpplanPojo.setBillplandate(new Date());
        if (saSmpplanPojo.getItemcount() == null) saSmpplanPojo.setItemcount(0);
        if (saSmpplanPojo.getMrpcount() == null) saSmpplanPojo.setMrpcount(0);
        if (saSmpplanPojo.getStartcount() == null) saSmpplanPojo.setStartcount(0);
        if (saSmpplanPojo.getDisannulcount() == null) saSmpplanPojo.setDisannulcount(0);
        if (saSmpplanPojo.getFinishcount() == null) saSmpplanPojo.setFinishcount(0);
        if (saSmpplanPojo.getPrintcount() == null) saSmpplanPojo.setPrintcount(0);
        if (saSmpplanPojo.getOaflowmark() == null) saSmpplanPojo.setOaflowmark(0);
        if (saSmpplanPojo.getBillwkwpid() == null) saSmpplanPojo.setBillwkwpid("");
        if (saSmpplanPojo.getBillwkwpcode() == null) saSmpplanPojo.setBillwkwpcode("");
        if (saSmpplanPojo.getBillwkwpname() == null) saSmpplanPojo.setBillwkwpname("");
        if (saSmpplanPojo.getCustom1() == null) saSmpplanPojo.setCustom1("");
        if (saSmpplanPojo.getCustom2() == null) saSmpplanPojo.setCustom2("");
        if (saSmpplanPojo.getCustom3() == null) saSmpplanPojo.setCustom3("");
        if (saSmpplanPojo.getCustom4() == null) saSmpplanPojo.setCustom4("");
        if (saSmpplanPojo.getCustom5() == null) saSmpplanPojo.setCustom5("");
        if (saSmpplanPojo.getCustom6() == null) saSmpplanPojo.setCustom6("");
        if (saSmpplanPojo.getCustom7() == null) saSmpplanPojo.setCustom7("");
        if (saSmpplanPojo.getCustom8() == null) saSmpplanPojo.setCustom8("");
        if (saSmpplanPojo.getCustom9() == null) saSmpplanPojo.setCustom9("");
        if (saSmpplanPojo.getCustom10() == null) saSmpplanPojo.setCustom10("");
        if(saSmpplanPojo.getDeptid()==null) saSmpplanPojo.setDeptid("");
        if(saSmpplanPojo.getTenantid()==null) saSmpplanPojo.setTenantid("");
        if (saSmpplanPojo.getTenantname() == null) saSmpplanPojo.setTenantname("");
        if (saSmpplanPojo.getRevision() == null) saSmpplanPojo.setRevision(0);
    }

    @Override
    public SaSmpplanPojo closed(List<SaSmpplanitemPojo> lst, Integer type, LoginUser loginUser) {
        int disNum = 0;
        String tid = loginUser.getTenantid();
        String Pid = "";
        String strType = type == 1 ? "关闭" : "开启";
        for (int i = 0; i < lst.size(); i++) {
            SaSmpplanitemPojo Pojo = lst.get(i);
            SaSmpplanitemPojo dbPojo = this.saSmpplanitemMapper.getEntity(Pojo.getId());
            if (dbPojo != null) {
                if (!Objects.equals(dbPojo.getClosed(), type)) {
                    //if (Pid.isEmpty()) Pid = dbPojo.getPid();
                    //if (dbPojo.getDisannulmark() == 1) {
                    //    throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已作废,禁止操作");
                    //}
                    SaSmpplanitemEntity entity = new SaSmpplanitemEntity();
                    entity.setId(dbPojo.getId());
                    entity.setClosed(type);
                    entity.setTenantid(loginUser.getTenantid());
                    this.saSmpplanitemMapper.update(entity);
                    disNum++;
                } else {
                    throw new RuntimeException(i + 1 + "行," + dbPojo.getItemname() + "已" + strType + ",无需操作");
                }
            }
        }

        ////--> lst.stream()拿到去重的goodsid Set集合
        //Set<String> goodsidLstSet = lst.stream().map(BusMachiningitemPojo::getGoodsid).collect(Collectors.toSet());
        //// 同步货品数量 SQL替代MQ
        //goodsidLstSet.forEach(goodsid -> {
        //    syncMapper.updateGoodsBusRemQty(goodsid, tid);
        //});
        //// 同步客户余额(来自同一个客户) SQL替代MQ
        //String groupid = busMachiningitemMapper.getGroupidByItemid(lst.get(0).getId(), tid);
        //syncMapper.updateWorkgroupBusMachRemAmt(groupid, tid);

        if (disNum > 0) {
            this.saSmpplanMapper.updateFinishCount(Pid, tid);
            //主表更改
            SaSmpplanEntity saSmpplanEntity = new SaSmpplanEntity();
            saSmpplanEntity.setId(Pid);
            saSmpplanEntity.setLister(loginUser.getRealname());
            saSmpplanEntity.setListerid(loginUser.getUserid());
            saSmpplanEntity.setModifydate(new Date());
            saSmpplanEntity.setTenantid(loginUser.getTenantid());
            this.saSmpplanMapper.update(saSmpplanEntity);
            //返回Bill实例
            return this.getBillEntity(saSmpplanEntity.getId());
        } else {
            throw new RuntimeException("未查到可更改的记录");
        }
    }

    @Override
    public void updateOaflowmark(SaSmpplanPojo bill) {
        saSmpplanMapper.updateOaflowmark(bill);
    }
}
