package inks.service.sa.crm.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.crm.domain.SaLeadsitemEntity;
import inks.service.sa.crm.domain.pojo.SaLeadsitemPojo;
import inks.service.sa.crm.mapper.SaLeadsitemMapper;
import inks.service.sa.crm.service.SaLeadsitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 线索记录(SaLeadsitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-03-06 12:46:24
 */
@Service("saLeadsitemService")
public class SaLeadsitemServiceImpl implements SaLeadsitemService {
    @Resource
    private SaLeadsitemMapper saLeadsitemMapper;


    @Override
    public SaLeadsitemPojo getEntity(String key) {
        return this.saLeadsitemMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaLeadsitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaLeadsitemPojo> lst = saLeadsitemMapper.getPageList(queryParam);
            PageInfo<SaLeadsitemPojo> pageInfo = new PageInfo<SaLeadsitemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public List<SaLeadsitemPojo> getList(String Pid) {
        try {
            List<SaLeadsitemPojo> lst = saLeadsitemMapper.getList(Pid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param saLeadsitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaLeadsitemPojo insert(SaLeadsitemPojo saLeadsitemPojo) {
        //初始化item的NULL
        SaLeadsitemPojo itempojo = this.clearNull(saLeadsitemPojo);
        SaLeadsitemEntity saLeadsitemEntity = new SaLeadsitemEntity();
        BeanUtils.copyProperties(itempojo, saLeadsitemEntity);

        saLeadsitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saLeadsitemEntity.setRevision(1);  //乐观锁
        this.saLeadsitemMapper.insert(saLeadsitemEntity);
        return this.getEntity(saLeadsitemEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saLeadsitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaLeadsitemPojo update(SaLeadsitemPojo saLeadsitemPojo) {
        SaLeadsitemEntity saLeadsitemEntity = new SaLeadsitemEntity();
        BeanUtils.copyProperties(saLeadsitemPojo, saLeadsitemEntity);
        this.saLeadsitemMapper.update(saLeadsitemEntity);
        return this.getEntity(saLeadsitemEntity.getId());
    }


    @Override
    public int delete(String key) {
        return this.saLeadsitemMapper.delete(key);
    }

    /**
     * 修改数据
     *
     * @param saLeadsitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaLeadsitemPojo clearNull(SaLeadsitemPojo saLeadsitemPojo) {
        //初始化NULL字段
        if (saLeadsitemPojo.getPid() == null) saLeadsitemPojo.setPid("");
        if (saLeadsitemPojo.getItemtype() == null) saLeadsitemPojo.setItemtype("");
        if (saLeadsitemPojo.getItemcontent() == null) saLeadsitemPojo.setItemcontent("");
        if (saLeadsitemPojo.getPhotourl1() == null) saLeadsitemPojo.setPhotourl1("");
        if (saLeadsitemPojo.getPhotourl2() == null) saLeadsitemPojo.setPhotourl2("");
        if (saLeadsitemPojo.getPhotourl3() == null) saLeadsitemPojo.setPhotourl3("");
        if (saLeadsitemPojo.getPhotoname1() == null) saLeadsitemPojo.setPhotoname1("");
        if (saLeadsitemPojo.getPhotoname2() == null) saLeadsitemPojo.setPhotoname2("");
        if (saLeadsitemPojo.getPhotoname3() == null) saLeadsitemPojo.setPhotoname3("");
        if (saLeadsitemPojo.getRownum() == null) saLeadsitemPojo.setRownum(0);
        if (saLeadsitemPojo.getNextdate() == null) saLeadsitemPojo.setNextdate(new Date());
        if (saLeadsitemPojo.getRemark() == null) saLeadsitemPojo.setRemark("");
        if (saLeadsitemPojo.getLister() == null) saLeadsitemPojo.setLister("");
        if (saLeadsitemPojo.getCreatedate() == null) saLeadsitemPojo.setCreatedate(new Date());
        if (saLeadsitemPojo.getModifydate() == null) saLeadsitemPojo.setModifydate(new Date());
        if (saLeadsitemPojo.getCustom1() == null) saLeadsitemPojo.setCustom1("");
        if (saLeadsitemPojo.getCustom2() == null) saLeadsitemPojo.setCustom2("");
        if (saLeadsitemPojo.getCustom3() == null) saLeadsitemPojo.setCustom3("");
        if (saLeadsitemPojo.getCustom4() == null) saLeadsitemPojo.setCustom4("");
        if (saLeadsitemPojo.getCustom5() == null) saLeadsitemPojo.setCustom5("");
        if (saLeadsitemPojo.getCustom6() == null) saLeadsitemPojo.setCustom6("");
        if (saLeadsitemPojo.getCustom7() == null) saLeadsitemPojo.setCustom7("");
        if (saLeadsitemPojo.getCustom8() == null) saLeadsitemPojo.setCustom8("");
        if (saLeadsitemPojo.getCustom9() == null) saLeadsitemPojo.setCustom9("");
        if (saLeadsitemPojo.getCustom10() == null) saLeadsitemPojo.setCustom10("");
        if (saLeadsitemPojo.getTenantid() == null) saLeadsitemPojo.setTenantid("");
        if (saLeadsitemPojo.getTenantname() == null) saLeadsitemPojo.setTenantname("");
        if (saLeadsitemPojo.getRevision() == null) saLeadsitemPojo.setRevision(0);
        return saLeadsitemPojo;
    }
}
