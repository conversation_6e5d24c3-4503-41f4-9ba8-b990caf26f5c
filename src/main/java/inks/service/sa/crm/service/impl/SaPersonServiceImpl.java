package inks.service.sa.crm.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.crm.domain.SaPersonEntity;
import inks.service.sa.crm.domain.pojo.SaPersonPojo;
import inks.service.sa.crm.mapper.SaPersonMapper;
import inks.service.sa.crm.service.SaPersonService;
import inks.service.sa.crm.service.SaPersongroupService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 联系人(SaPerson)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-03-06 12:57:02
 */
@Service("saPersonService")
public class SaPersonServiceImpl implements SaPersonService {
    @Resource
    private SaPersonMapper saPersonMapper;
    @Resource
    private SaPersongroupService saPersongroupService;


    @Override
    public SaPersonPojo getEntity(String key) {
        return this.saPersonMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaPersonPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaPersonPojo> lst = saPersonMapper.getPageList(queryParam);
            PageInfo<SaPersonPojo> pageInfo = new PageInfo<SaPersonPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saPersonPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaPersonPojo insert(SaPersonPojo saPersonPojo) {
        //初始化NULL字段
        if (saPersonPojo.getGroupid() == null) saPersonPojo.setGroupid("");
        if (saPersonPojo.getPersonname() == null) saPersonPojo.setPersonname("");
        if (saPersonPojo.getTelephone() == null) saPersonPojo.setTelephone("");
        if (saPersonPojo.getMobile() == null) saPersonPojo.setMobile("");
        if (saPersonPojo.getPosition() == null) saPersonPojo.setPosition("");
        if (saPersonPojo.getWechat() == null) saPersonPojo.setWechat("");
        if (saPersonPojo.getEmail() == null) saPersonPojo.setEmail("");
        if (saPersonPojo.getDecisionmark() == null) saPersonPojo.setDecisionmark(0);
        if (saPersonPojo.getOperatorid() == null) saPersonPojo.setOperatorid("");
        if (saPersonPojo.getOperator() == null) saPersonPojo.setOperator("");
        if (saPersonPojo.getDepartname() == null) saPersonPojo.setDepartname("");
        if (saPersonPojo.getEnabledmark() == null) saPersonPojo.setEnabledmark(0);
        if (saPersonPojo.getStatenum() == null) saPersonPojo.setStatenum(0);
        if (saPersonPojo.getStatedate() == null) saPersonPojo.setStatedate(new Date());
        if (saPersonPojo.getRownum() == null) saPersonPojo.setRownum(0);
        if (saPersonPojo.getRemark() == null) saPersonPojo.setRemark("");
        if (saPersonPojo.getCreateby() == null) saPersonPojo.setCreateby("");
        if (saPersonPojo.getCreatebyid() == null) saPersonPojo.setCreatebyid("");
        if (saPersonPojo.getCreatedate() == null) saPersonPojo.setCreatedate(new Date());
        if (saPersonPojo.getLister() == null) saPersonPojo.setLister("");
        if (saPersonPojo.getListerid() == null) saPersonPojo.setListerid("");
        if (saPersonPojo.getModifydate() == null) saPersonPojo.setModifydate(new Date());
        if (saPersonPojo.getCustom1() == null) saPersonPojo.setCustom1("");
        if (saPersonPojo.getCustom2() == null) saPersonPojo.setCustom2("");
        if (saPersonPojo.getCustom3() == null) saPersonPojo.setCustom3("");
        if (saPersonPojo.getCustom4() == null) saPersonPojo.setCustom4("");
        if (saPersonPojo.getCustom5() == null) saPersonPojo.setCustom5("");
        if (saPersonPojo.getCustom6() == null) saPersonPojo.setCustom6("");
        if (saPersonPojo.getCustom7() == null) saPersonPojo.setCustom7("");
        if (saPersonPojo.getCustom8() == null) saPersonPojo.setCustom8("");
        if (saPersonPojo.getDeptid() == null) saPersonPojo.setDeptid("");
        if (saPersonPojo.getTenantid() == null) saPersonPojo.setTenantid("");
        if (saPersonPojo.getTenantname() == null) saPersonPojo.setTenantname("");
        if (saPersonPojo.getRevision() == null) saPersonPojo.setRevision(0);
        SaPersonEntity saPersonEntity = new SaPersonEntity();
        BeanUtils.copyProperties(saPersonPojo, saPersonEntity);

        saPersonEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saPersonEntity.setRevision(1);  //乐观锁
        this.saPersonMapper.insert(saPersonEntity);
        return this.getEntity(saPersonEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saPersonPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaPersonPojo update(SaPersonPojo saPersonPojo) {
        SaPersonEntity saPersonEntity = new SaPersonEntity();
        BeanUtils.copyProperties(saPersonPojo, saPersonEntity);
        this.saPersonMapper.update(saPersonEntity);
        return this.getEntity(saPersonEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键 联系人id
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.saPersonMapper.delete(key);
//        saPersongroupService.deleteByKey(key);
    }

    @Override
    public List<SaPersonPojo> getListByLeads(String id) {
        return this.saPersonMapper.getListByLeads(id);
    }

    @Override
    public List<SaPersonPojo> getListByGroup(String id) {
        return this.saPersonMapper.getListByGroup(id);
    }
}
