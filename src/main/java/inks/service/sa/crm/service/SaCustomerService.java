package inks.service.sa.crm.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.pojo.SaCustomerPojo;

/**
 * 客户(SaCustomer)表服务接口
 *
 * <AUTHOR>
 * @since 2023-03-06 14:17:22
 */
public interface SaCustomerService {


    SaCustomerPojo getEntity(String key);


    PageInfo<SaCustomerPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saCustomerPojo 实例对象
     * @return 实例对象
     */
    SaCustomerPojo insert(SaCustomerPojo saCustomerPojo, LoginUser loginUser);

    /**
     * 修改数据
     *
     * @param saCustomerpojo 实例对象
     * @return 实例对象
     */
    SaCustomerPojo update(SaCustomerPojo saCustomerpojo, LoginUser loginUser);


    int delete(String key, LoginUser loginUser);

    /**
     * ( 不)通过type，Get下一个Cod
     *
     * @return 实例对象
     */
    String getNextCode();

    SaCustomerPojo getEntityByCustname(String custname);
}
