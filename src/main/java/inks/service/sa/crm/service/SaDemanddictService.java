package inks.service.sa.crm.service;

import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.pojo.SaDemanddictPojo;
import inks.service.sa.crm.domain.SaDemanddictEntity;

import com.github.pagehelper.PageInfo;

/**
 * 需求字典(SaDemanddict)表服务接口
 *
 * <AUTHOR>
 * @since 2024-10-11 10:02:22
 */
public interface SaDemanddictService {


    SaDemanddictPojo getEntity(String key);

    PageInfo<SaDemanddictPojo> getPageList(QueryParam queryParam);

    SaDemanddictPojo insert(SaDemanddictPojo saDemanddictPojo);

    SaDemanddictPojo update(SaDemanddictPojo saDemanddictpojo);

    int delete(String key);
}
