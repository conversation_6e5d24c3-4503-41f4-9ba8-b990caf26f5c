package inks.service.sa.crm.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.pojo.SaCrmgoodsPojo;

/**
 * 产品(SaCrmgoods)表服务接口
 *
 * <AUTHOR>
 * @since 2023-03-06 13:00:46
 */
public interface SaCrmgoodsService {


    SaCrmgoodsPojo getEntity(String key);


    PageInfo<SaCrmgoodsPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saCrmgoodsPojo 实例对象
     * @return 实例对象
     */
    SaCrmgoodsPojo insert(SaCrmgoodsPojo saCrmgoodsPojo);

    /**
     * 修改数据
     *
     * @param saCrmgoodspojo 实例对象
     * @return 实例对象
     */
    SaCrmgoodsPojo update(SaCrmgoodsPojo saCrmgoodspojo);


    int delete(String key);
}
