package inks.service.sa.crm.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.crm.domain.SaQuotationitemEntity;
import inks.service.sa.crm.domain.pojo.SaQuotationitemPojo;
import inks.service.sa.crm.mapper.SaQuotationitemMapper;
import inks.service.sa.crm.service.SaQuotationitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 商机项目(SaQuotationitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-04-14 10:50:42
 */
@Service("saQuotationitemService")
public class SaQuotationitemServiceImpl implements SaQuotationitemService {
    @Resource
    private SaQuotationitemMapper saQuotationitemMapper;


    @Override
    public SaQuotationitemPojo getEntity(String key) {
        return this.saQuotationitemMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaQuotationitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaQuotationitemPojo> lst = saQuotationitemMapper.getPageList(queryParam);
            PageInfo<SaQuotationitemPojo> pageInfo = new PageInfo<SaQuotationitemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public List<SaQuotationitemPojo> getList(String Pid) {
        try {
            List<SaQuotationitemPojo> lst = saQuotationitemMapper.getList(Pid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param saQuotationitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaQuotationitemPojo insert(SaQuotationitemPojo saQuotationitemPojo) {
        //初始化item的NULL
        SaQuotationitemPojo itempojo = this.clearNull(saQuotationitemPojo);
        SaQuotationitemEntity saQuotationitemEntity = new SaQuotationitemEntity();
        BeanUtils.copyProperties(itempojo, saQuotationitemEntity);
        //生成雪花id
        saQuotationitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saQuotationitemEntity.setRevision(1);  //乐观锁
        this.saQuotationitemMapper.insert(saQuotationitemEntity);
        return this.getEntity(saQuotationitemEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saQuotationitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaQuotationitemPojo update(SaQuotationitemPojo saQuotationitemPojo) {
        SaQuotationitemEntity saQuotationitemEntity = new SaQuotationitemEntity();
        BeanUtils.copyProperties(saQuotationitemPojo, saQuotationitemEntity);
        this.saQuotationitemMapper.update(saQuotationitemEntity);
        return this.getEntity(saQuotationitemEntity.getId());
    }


    @Override
    public int delete(String key) {
        return this.saQuotationitemMapper.delete(key);
    }

    /**
     * 修改数据
     *
     * @param saQuotationitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaQuotationitemPojo clearNull(SaQuotationitemPojo saQuotationitemPojo) {
        //初始化NULL字段
        if (saQuotationitemPojo.getPid() == null) saQuotationitemPojo.setPid("");
        if (saQuotationitemPojo.getGoodsid() == null) saQuotationitemPojo.setGoodsid("");
     if(saQuotationitemPojo.getItemcode()==null) saQuotationitemPojo.setItemcode("");
        if (saQuotationitemPojo.getItemname() == null) saQuotationitemPojo.setItemname("");
        if (saQuotationitemPojo.getItemspec() == null) saQuotationitemPojo.setItemspec("");
        if (saQuotationitemPojo.getItemunit() == null) saQuotationitemPojo.setItemunit("");
        if (saQuotationitemPojo.getQuantity() == null) saQuotationitemPojo.setQuantity(0D);
        if (saQuotationitemPojo.getPrice() == null) saQuotationitemPojo.setPrice(0D);
        if (saQuotationitemPojo.getAmount() == null) saQuotationitemPojo.setAmount(0D);
        if (saQuotationitemPojo.getItemtaxrate() == null) saQuotationitemPojo.setItemtaxrate(0);
        if (saQuotationitemPojo.getTaxtotal() == null) saQuotationitemPojo.setTaxtotal(0D);
        if (saQuotationitemPojo.getTaxprice() == null) saQuotationitemPojo.setTaxprice(0D);
        if (saQuotationitemPojo.getTaxamount() == null) saQuotationitemPojo.setTaxamount(0D);
        if (saQuotationitemPojo.getRemark() == null) saQuotationitemPojo.setRemark("");
     if(saQuotationitemPojo.getRownum()==null) saQuotationitemPojo.setRownum(0);
     if(saQuotationitemPojo.getAttributejson()==null) saQuotationitemPojo.setAttributejson("");
     if(saQuotationitemPojo.getAttributestr()==null) saQuotationitemPojo.setAttributestr("");
        if (saQuotationitemPojo.getCustom1() == null) saQuotationitemPojo.setCustom1("");
        if (saQuotationitemPojo.getCustom2() == null) saQuotationitemPojo.setCustom2("");
        if (saQuotationitemPojo.getCustom3() == null) saQuotationitemPojo.setCustom3("");
        if (saQuotationitemPojo.getCustom4() == null) saQuotationitemPojo.setCustom4("");
        if (saQuotationitemPojo.getCustom5() == null) saQuotationitemPojo.setCustom5("");
        if (saQuotationitemPojo.getCustom6() == null) saQuotationitemPojo.setCustom6("");
        if (saQuotationitemPojo.getCustom7() == null) saQuotationitemPojo.setCustom7("");
        if (saQuotationitemPojo.getCustom8() == null) saQuotationitemPojo.setCustom8("");
        if (saQuotationitemPojo.getCustom9() == null) saQuotationitemPojo.setCustom9("");
        if (saQuotationitemPojo.getCustom10() == null) saQuotationitemPojo.setCustom10("");
        if (saQuotationitemPojo.getTenantid() == null) saQuotationitemPojo.setTenantid("");
        if (saQuotationitemPojo.getRevision() == null) saQuotationitemPojo.setRevision(0);
        return saQuotationitemPojo;
    }
}
