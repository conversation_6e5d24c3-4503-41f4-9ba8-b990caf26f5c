package inks.service.sa.crm.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.pojo.SaFollowviewPojo;

/**
 * 跟踪记录(SaFollowview)表服务接口
 *
 * <AUTHOR>
 * @since 2024-07-26 09:51:37
 */
public interface SaFollowviewService {


    SaFollowviewPojo getEntity(String key);

    PageInfo<SaFollowviewPojo> getPageList(QueryParam queryParam);

    SaFollowviewPojo insert(SaFollowviewPojo saFollowviewPojo);

    SaFollowviewPojo update(SaFollowviewPojo saFollowviewpojo);

    int delete(String key);
}
