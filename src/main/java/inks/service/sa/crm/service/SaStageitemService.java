package inks.service.sa.crm.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.pojo.SaStageitemPojo;

import java.util.List;

/**
 * 阶段子表(SaStageitem)表服务接口
 *
 * <AUTHOR>
 * @since 2024-02-20 09:09:40
 */
public interface SaStageitemService {


    SaStageitemPojo getEntity(String key);


    PageInfo<SaStageitemPojo> getPageList(QueryParam queryParam);

    List<SaStageitemPojo> getList(String Pid);

    /**
     * 新增数据
     *
     * @param saStageitemPojo 实例对象
     * @return 实例对象
     */
    SaStageitemPojo insert(SaStageitemPojo saStageitemPojo);

    /**
     * 修改数据
     *
     * @param saStageitempojo 实例对象
     * @return 实例对象
     */
    SaStageitemPojo update(SaStageitemPojo saStageitempojo);


    int delete(String key);

    /**
     * 修改数据
     *
     * @param saStageitempojo 实例对象
     * @return 实例对象
     */
    SaStageitemPojo clearNull(SaStageitemPojo saStageitempojo);
}
