package inks.service.sa.crm.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.crm.domain.SaDmslicensekeyEntity;
import inks.service.sa.crm.domain.pojo.SaDmslicensekeyPojo;
import inks.service.sa.crm.mapper.SaDmslicensekeyMapper;
import inks.service.sa.crm.service.SaDmslicensekeyService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * DMS用户-授权码关联表(SaDmslicensekey)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-12-09 09:56:37
 */
@Service("saDmslicensekeyService")
public class SaDmslicensekeyServiceImpl implements SaDmslicensekeyService {
    @Resource
    private SaDmslicensekeyMapper saDmslicensekeyMapper;


    @Override
    public SaDmslicensekeyPojo getEntity(String key) {
        return this.saDmslicensekeyMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaDmslicensekeyPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaDmslicensekeyPojo> lst = saDmslicensekeyMapper.getPageList(queryParam);
            PageInfo<SaDmslicensekeyPojo> pageInfo = new PageInfo<SaDmslicensekeyPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saDmslicensekeyPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaDmslicensekeyPojo insert(SaDmslicensekeyPojo saDmslicensekeyPojo) {
        //初始化NULL字段
        if (saDmslicensekeyPojo.getDmsuserid() == null) saDmslicensekeyPojo.setDmsuserid("");
        if (saDmslicensekeyPojo.getLicensekey() == null) saDmslicensekeyPojo.setLicensekey("");
        if (saDmslicensekeyPojo.getSoftware() == null) saDmslicensekeyPojo.setSoftware("");
        if (saDmslicensekeyPojo.getSoftwareperiod() == null) saDmslicensekeyPojo.setSoftwareperiod(0);
        if (saDmslicensekeyPojo.getMaxonlineuser() == null) saDmslicensekeyPojo.setMaxonlineuser(0);
        if (saDmslicensekeyPojo.getMaxsupporthard() == null) saDmslicensekeyPojo.setMaxsupporthard(0);
        if (saDmslicensekeyPojo.getIslimited() == null) saDmslicensekeyPojo.setIslimited(0);
        if (saDmslicensekeyPojo.getKeyquantity() == null) saDmslicensekeyPojo.setKeyquantity(0);
        if (saDmslicensekeyPojo.getStatecode() == null) saDmslicensekeyPojo.setStatecode("");
        if (saDmslicensekeyPojo.getRemark() == null) saDmslicensekeyPojo.setRemark("");
        if (saDmslicensekeyPojo.getCreateby() == null) saDmslicensekeyPojo.setCreateby("");
        if (saDmslicensekeyPojo.getCreatebyid() == null) saDmslicensekeyPojo.setCreatebyid("");
        if (saDmslicensekeyPojo.getCreatedate() == null) saDmslicensekeyPojo.setCreatedate(new Date());
        if (saDmslicensekeyPojo.getLister() == null) saDmslicensekeyPojo.setLister("");
        if (saDmslicensekeyPojo.getListerid() == null) saDmslicensekeyPojo.setListerid("");
        if (saDmslicensekeyPojo.getModifydate() == null) saDmslicensekeyPojo.setModifydate(new Date());
        if (saDmslicensekeyPojo.getTenantid() == null) saDmslicensekeyPojo.setTenantid("");
        if (saDmslicensekeyPojo.getTenantname() == null) saDmslicensekeyPojo.setTenantname("");
        if (saDmslicensekeyPojo.getRevision() == null) saDmslicensekeyPojo.setRevision(0);
        SaDmslicensekeyEntity saDmslicensekeyEntity = new SaDmslicensekeyEntity();
        BeanUtils.copyProperties(saDmslicensekeyPojo, saDmslicensekeyEntity);
        //生成雪花id
        saDmslicensekeyEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saDmslicensekeyEntity.setRevision(1);  //乐观锁
        this.saDmslicensekeyMapper.insert(saDmslicensekeyEntity);
        return this.getEntity(saDmslicensekeyEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saDmslicensekeyPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaDmslicensekeyPojo update(SaDmslicensekeyPojo saDmslicensekeyPojo) {
        SaDmslicensekeyEntity saDmslicensekeyEntity = new SaDmslicensekeyEntity();
        BeanUtils.copyProperties(saDmslicensekeyPojo, saDmslicensekeyEntity);
        this.saDmslicensekeyMapper.update(saDmslicensekeyEntity);
        return this.getEntity(saDmslicensekeyEntity.getId());
    }


    @Override
    public int delete(String key) {
        return this.saDmslicensekeyMapper.delete(key);
    }


}
