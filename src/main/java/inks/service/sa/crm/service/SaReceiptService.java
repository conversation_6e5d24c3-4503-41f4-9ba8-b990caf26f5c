package inks.service.sa.crm.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.pojo.SaReceiptPojo;

/**
 * 收款单(SaReceipt)表服务接口
 *
 * <AUTHOR>
 * @since 2023-03-25 10:48:44
 */
public interface SaReceiptService {


    SaReceiptPojo getEntity(String key);


    PageInfo<SaReceiptPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saReceiptPojo 实例对象
     * @return 实例对象
     */
    SaReceiptPojo insert(SaReceiptPojo saReceiptPojo);

    /**
     * 修改数据
     *
     * @param saReceiptpojo 实例对象
     * @return 实例对象
     */
    SaReceiptPojo update(SaReceiptPojo saReceiptpojo);


    int delete(String key);


    SaReceiptPojo approval(SaReceiptPojo saReceiptPojo);
}
