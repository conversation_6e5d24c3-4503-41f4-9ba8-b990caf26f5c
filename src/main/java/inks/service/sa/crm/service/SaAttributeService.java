package inks.service.sa.crm.service;

import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.pojo.SaAttributePojo;
import inks.service.sa.crm.domain.SaAttributeEntity;

import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * SPU属性表(SaAttribute)表服务接口
 *
 * <AUTHOR>
 * @since 2024-10-25 17:09:06
 */
public interface SaAttributeService {


    SaAttributePojo getEntity(String key);

    PageInfo<SaAttributePojo> getPageList(QueryParam queryParam);

    SaAttributePojo insert(SaAttributePojo saAttributePojo);

    SaAttributePojo update(SaAttributePojo saAttributepojo);

    int delete(String key);

    List<SaAttributePojo> getListByShow(String tenantid);
}
