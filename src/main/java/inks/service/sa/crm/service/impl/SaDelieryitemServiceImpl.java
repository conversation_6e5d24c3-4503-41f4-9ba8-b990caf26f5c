package inks.service.sa.crm.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.crm.domain.SaDelieryitemEntity;
import inks.service.sa.crm.domain.pojo.SaDelieryitemPojo;
import inks.service.sa.crm.mapper.SaDelieryitemMapper;
import inks.service.sa.crm.service.SaDelieryitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 发货明细(SaDelieryitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-04-05 16:52:14
 */
@Service("saDelieryitemService")
public class SaDelieryitemServiceImpl implements SaDelieryitemService {
    @Resource
    private SaDelieryitemMapper saDelieryitemMapper;


    @Override
    public SaDelieryitemPojo getEntity(String key) {
        return this.saDelieryitemMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaDelieryitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaDelieryitemPojo> lst = saDelieryitemMapper.getPageList(queryParam);
            PageInfo<SaDelieryitemPojo> pageInfo = new PageInfo<SaDelieryitemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public List<SaDelieryitemPojo> getList(String Pid) {
        try {
            List<SaDelieryitemPojo> lst = saDelieryitemMapper.getList(Pid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param saDelieryitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaDelieryitemPojo insert(SaDelieryitemPojo saDelieryitemPojo) {
        //初始化item的NULL
        SaDelieryitemPojo itempojo = this.clearNull(saDelieryitemPojo);
        SaDelieryitemEntity saDelieryitemEntity = new SaDelieryitemEntity();
        BeanUtils.copyProperties(itempojo, saDelieryitemEntity);
        //生成雪花id
        saDelieryitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saDelieryitemEntity.setRevision(1);  //乐观锁
        this.saDelieryitemMapper.insert(saDelieryitemEntity);
        return this.getEntity(saDelieryitemEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saDelieryitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaDelieryitemPojo update(SaDelieryitemPojo saDelieryitemPojo) {
        SaDelieryitemEntity saDelieryitemEntity = new SaDelieryitemEntity();
        BeanUtils.copyProperties(saDelieryitemPojo, saDelieryitemEntity);
        this.saDelieryitemMapper.update(saDelieryitemEntity);
        return this.getEntity(saDelieryitemEntity.getId());
    }


    @Override
    public int delete(String key) {
        return this.saDelieryitemMapper.delete(key);
    }

    /**
     * 修改数据
     *
     * @param saDelieryitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaDelieryitemPojo clearNull(SaDelieryitemPojo saDelieryitemPojo) {
        //初始化NULL字段
        if (saDelieryitemPojo.getPid() == null) saDelieryitemPojo.setPid("");
        if (saDelieryitemPojo.getGoodsid() == null) saDelieryitemPojo.setGoodsid("");
        if (saDelieryitemPojo.getItemcode() == null) saDelieryitemPojo.setItemcode("");
        if (saDelieryitemPojo.getItemname() == null) saDelieryitemPojo.setItemname("");
        if (saDelieryitemPojo.getItemspec() == null) saDelieryitemPojo.setItemspec("");
        if (saDelieryitemPojo.getItemunit() == null) saDelieryitemPojo.setItemunit("");
        if (saDelieryitemPojo.getQuantity() == null) saDelieryitemPojo.setQuantity(0D);
        if (saDelieryitemPojo.getTaxprice() == null) saDelieryitemPojo.setTaxprice(0D);
        if (saDelieryitemPojo.getTaxamount() == null) saDelieryitemPojo.setTaxamount(0D);
        if (saDelieryitemPojo.getPrice() == null) saDelieryitemPojo.setPrice(0D);
        if (saDelieryitemPojo.getAmount() == null) saDelieryitemPojo.setAmount(0D);
        if (saDelieryitemPojo.getItemtaxrate() == null) saDelieryitemPojo.setItemtaxrate(0);
        if (saDelieryitemPojo.getTaxtotal() == null) saDelieryitemPojo.setTaxtotal(0D);
        if (saDelieryitemPojo.getStdprice() == null) saDelieryitemPojo.setStdprice(0D);
        if (saDelieryitemPojo.getStdamount() == null) saDelieryitemPojo.setStdamount(0D);
        if (saDelieryitemPojo.getRebate() == null) saDelieryitemPojo.setRebate(0);
        if (saDelieryitemPojo.getFreeqty() == null) saDelieryitemPojo.setFreeqty(0D);
        if (saDelieryitemPojo.getPickqty() == null) saDelieryitemPojo.setPickqty(0D);
        if (saDelieryitemPojo.getFinishqty() == null) saDelieryitemPojo.setFinishqty(0D);
        if (saDelieryitemPojo.getFinishclosed() == null) saDelieryitemPojo.setFinishclosed(0);
        if (saDelieryitemPojo.getRownum() == null) saDelieryitemPojo.setRownum(0);
        if (saDelieryitemPojo.getRemark() == null) saDelieryitemPojo.setRemark("");
        if (saDelieryitemPojo.getCiteuid() == null) saDelieryitemPojo.setCiteuid("");
        if (saDelieryitemPojo.getCiteitemid() == null) saDelieryitemPojo.setCiteitemid("");
        if (saDelieryitemPojo.getCustpo() == null) saDelieryitemPojo.setCustpo("");
        if (saDelieryitemPojo.getStatecode() == null) saDelieryitemPojo.setStatecode("");
        if (saDelieryitemPojo.getStatedate() == null) saDelieryitemPojo.setStatedate(new Date());
        if (saDelieryitemPojo.getBussqty() == null) saDelieryitemPojo.setBussqty(0D);
        if (saDelieryitemPojo.getBussclosed() == null) saDelieryitemPojo.setBussclosed(0);
        if (saDelieryitemPojo.getMachtype() == null) saDelieryitemPojo.setMachtype("");
        if (saDelieryitemPojo.getInvoqty() == null) saDelieryitemPojo.setInvoqty(0D);
        if (saDelieryitemPojo.getInvoclosed() == null) saDelieryitemPojo.setInvoclosed(0);
        if (saDelieryitemPojo.getReturnqty() == null) saDelieryitemPojo.setReturnqty(0D);
        if (saDelieryitemPojo.getReturnmatqty() == null) saDelieryitemPojo.setReturnmatqty(0D);
        if (saDelieryitemPojo.getReturnclosed() == null) saDelieryitemPojo.setReturnclosed(0);
        if (saDelieryitemPojo.getSalescost() == null) saDelieryitemPojo.setSalescost(0D);
        if (saDelieryitemPojo.getVirtualitem() == null) saDelieryitemPojo.setVirtualitem(0);
        if (saDelieryitemPojo.getLocation() == null) saDelieryitemPojo.setLocation("");
        if (saDelieryitemPojo.getBatchno() == null) saDelieryitemPojo.setBatchno("");
        if (saDelieryitemPojo.getMachuid() == null) saDelieryitemPojo.setMachuid("");
        if (saDelieryitemPojo.getMachitemid() == null) saDelieryitemPojo.setMachitemid("");
        if (saDelieryitemPojo.getDisannulmark() == null) saDelieryitemPojo.setDisannulmark(0);
        if (saDelieryitemPojo.getDisannullisterid() == null) saDelieryitemPojo.setDisannullisterid("");
        if (saDelieryitemPojo.getDisannullister() == null) saDelieryitemPojo.setDisannullister("");
        if (saDelieryitemPojo.getDisannuldate() == null) saDelieryitemPojo.setDisannuldate(new Date());
        if (saDelieryitemPojo.getBfitemid() == null) saDelieryitemPojo.setBfitemid(0);
        if (saDelieryitemPojo.getAttributejson() == null) saDelieryitemPojo.setAttributejson("");
        if (saDelieryitemPojo.getAttributestr() == null) saDelieryitemPojo.setAttributestr("");
        if (saDelieryitemPojo.getMachdate() == null) saDelieryitemPojo.setMachdate(new Date());
        if (saDelieryitemPojo.getCostitemjson() == null) saDelieryitemPojo.setCostitemjson("");
        if (saDelieryitemPojo.getCostgroupjson() == null) saDelieryitemPojo.setCostgroupjson("");
        if (saDelieryitemPojo.getSourcetype() == null) saDelieryitemPojo.setSourcetype(0);
        if (saDelieryitemPojo.getCustom1() == null) saDelieryitemPojo.setCustom1("");
        if (saDelieryitemPojo.getCustom2() == null) saDelieryitemPojo.setCustom2("");
        if (saDelieryitemPojo.getCustom3() == null) saDelieryitemPojo.setCustom3("");
        if (saDelieryitemPojo.getCustom4() == null) saDelieryitemPojo.setCustom4("");
        if (saDelieryitemPojo.getCustom5() == null) saDelieryitemPojo.setCustom5("");
        if (saDelieryitemPojo.getCustom6() == null) saDelieryitemPojo.setCustom6("");
        if (saDelieryitemPojo.getCustom7() == null) saDelieryitemPojo.setCustom7("");
        if (saDelieryitemPojo.getCustom8() == null) saDelieryitemPojo.setCustom8("");
        if (saDelieryitemPojo.getCustom9() == null) saDelieryitemPojo.setCustom9("");
        if (saDelieryitemPojo.getCustom10() == null) saDelieryitemPojo.setCustom10("");
        if (saDelieryitemPojo.getCustom11() == null) saDelieryitemPojo.setCustom11("");
        if (saDelieryitemPojo.getCustom12() == null) saDelieryitemPojo.setCustom12("");
        if (saDelieryitemPojo.getCustom13() == null) saDelieryitemPojo.setCustom13("");
        if (saDelieryitemPojo.getCustom14() == null) saDelieryitemPojo.setCustom14("");
        if (saDelieryitemPojo.getCustom15() == null) saDelieryitemPojo.setCustom15("");
        if (saDelieryitemPojo.getCustom16() == null) saDelieryitemPojo.setCustom16("");
        if (saDelieryitemPojo.getCustom17() == null) saDelieryitemPojo.setCustom17("");
        if (saDelieryitemPojo.getCustom18() == null) saDelieryitemPojo.setCustom18("");
        if (saDelieryitemPojo.getTenantid() == null) saDelieryitemPojo.setTenantid("");
        if (saDelieryitemPojo.getRevision() == null) saDelieryitemPojo.setRevision(0);
        return saDelieryitemPojo;
    }
}
