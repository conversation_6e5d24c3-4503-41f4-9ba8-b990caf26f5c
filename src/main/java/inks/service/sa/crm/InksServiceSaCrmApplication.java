package inks.service.sa.crm;

import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.Environment;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.Date;

@SpringBootApplication(scanBasePackages = {"inks.sa.common.core","inks.common.core", "inks.service.sa.crm"})
@MapperScan({"inks.service.sa.crm.mapper", "inks.sa.common.core.mapper"})
@EnableFeignClients(basePackages = {"inks.sa.common.core.feign"})
@EnableSwagger2
public class InksServiceSaCrmApplication {
    private static final Logger logger = LoggerFactory.getLogger(InksServiceSaCrmApplication.class);

    public static void main(String[] args) throws UnknownHostException {
        ConfigurableApplicationContext application = SpringApplication.run(InksServiceSaCrmApplication.class, args);
        Environment env = application.getEnvironment();
        String ip = InetAddress.getLocalHost().getHostAddress();
        String port = env.getProperty("server.port");
        String property = env.getProperty("server.servlet.context-path");
        String path = property == null ? "" : property;
        System.out.println(
                "\n\t" +
                        "----------------------------------------------------------\n\t" +
                        "Application Sailrui-Boot is running! Access URLs:\n\t" +
                        "Local: \t\thttp://inks:8866@localhost:" + port + path + "/swagger-ui.html\n\t" +
                        "External: \thttp://inks:8866@" + ip + ":" + port + path + "/swagger-ui.html\n\t" +
                        "Nacos: \t\thttp://inks:<EMAIL>:" + port + path + "/swagger-ui.html\n\t" +
                        "------------------------------------------------------------");
        log();
    }
    public static void log() {
        // 生成各级别的日志
        Date date = new Date();
        logger.debug("This is a DEBUG log{}", date);
        logger.trace("This is a TRACE log{}", date);
        logger.info("This is an INFO log{}", date);
        logger.warn("This is a WARN log{}", date);
        logger.error("This is an ERROR log{}", date);
        // 其他项目启动时的初始化逻辑
        System.out.println("Application started and logs generated.");
    }
}
