package inks.service.sa.crm.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.SaSalesmanEntity;
import inks.service.sa.crm.domain.pojo.SaSalesmanPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 业务员(SaSalesman)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-10-31 16:22:15
 */
@Mapper
public interface SaSalesmanMapper {


    SaSalesmanPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaSalesmanPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param saSalesmanEntity 实例对象
     * @return 影响行数
     */
    int insert(SaSalesmanEntity saSalesmanEntity);


    /**
     * 修改数据
     *
     * @param saSalesmanEntity 实例对象
     * @return 影响行数
     */
    int update(SaSalesmanEntity saSalesmanEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

}

