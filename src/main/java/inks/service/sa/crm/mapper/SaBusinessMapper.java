package inks.service.sa.crm.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.SaBusinessEntity;
import inks.service.sa.crm.domain.pojo.SaBusinessPojo;
import inks.service.sa.crm.domain.pojo.SaBusinessitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 商机(SaBusiness)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-02-20 09:08:46
 */
@Mapper
public interface SaBusinessMapper {


    SaBusinessPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaBusinessitemdetailPojo> getPageList(QueryParam queryParam);


    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaBusinessPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saBusinessEntity 实例对象
     * @return 影响行数
     */
    int insert(SaBusinessEntity saBusinessEntity);


    /**
     * 修改数据
     *
     * @param saBusinessEntity 实例对象
     * @return 影响行数
     */
    int update(SaBusinessEntity saBusinessEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    /**
     * 查询 被删除的Item
     *
     * @param saBusinessPojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelItemIds(SaBusinessPojo saBusinessPojo);

    /**
     * 修改数据
     *
     * @param saBusinessEntity 实例对象
     * @return 影响行数
     */
    int approval(SaBusinessEntity saBusinessEntity);

    List<String> getDelStageIds(SaBusinessPojo saBusinessPojo);

    void updateOaflowmark(SaBusinessPojo billEntity);
}

