package inks.service.sa.crm.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.SaReceiptplanEntity;
import inks.service.sa.crm.domain.pojo.SaReceiptplanPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 收款计划(SaReceiptplan)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-03-25 10:48:42
 */
@Mapper
public interface SaReceiptplanMapper {


    SaReceiptplanPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaReceiptplanPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param saReceiptplanEntity 实例对象
     * @return 影响行数
     */
    int insert(SaReceiptplanEntity saReceiptplanEntity);


    /**
     * 修改数据
     *
     * @param saReceiptplanEntity 实例对象
     * @return 影响行数
     */
    int update(SaReceiptplanEntity saReceiptplanEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    /**
     * 修改数据
     *
     * @param saReceiptplanEntity 实例对象
     * @return 影响行数
     */
    int approval(SaReceiptplanEntity saReceiptplanEntity);
}

