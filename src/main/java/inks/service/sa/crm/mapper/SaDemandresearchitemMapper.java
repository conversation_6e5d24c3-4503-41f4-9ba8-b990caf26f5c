package inks.service.sa.crm.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.pojo.SaDemandresearchitemPojo;
import inks.service.sa.crm.domain.SaDemandresearchitemEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 需求调研子表(SaDemandresearchitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-10-11 10:15:14
 */
 @Mapper
public interface SaDemandresearchitemMapper {

    SaDemandresearchitemPojo getEntity(@Param("key") String key);

    List<SaDemandresearchitemPojo> getPageList(QueryParam queryParam);

    List<SaDemandresearchitemPojo> getList(@Param("Pid") String Pid);    

    int insert(SaDemandresearchitemEntity saDemandresearchitemEntity);

    int update(SaDemandresearchitemEntity saDemandresearchitemEntity);

    int delete(@Param("key") String key);

}

