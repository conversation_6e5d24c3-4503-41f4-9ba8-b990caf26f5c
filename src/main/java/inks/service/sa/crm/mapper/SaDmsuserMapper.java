package inks.service.sa.crm.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.SaDmsuserEntity;
import inks.service.sa.crm.domain.pojo.SaDmsuserPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * DMS用户(SaDmsuser)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-12-08 16:42:43
 */
@Mapper
public interface SaDmsuserMapper {


    SaDmsuserPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaDmsuserPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param saDmsuserEntity 实例对象
     * @return 影响行数
     */
    int insert(SaDmsuserEntity saDmsuserEntity);


    /**
     * 修改数据
     *
     * @param saDmsuserEntity 实例对象
     * @return 影响行数
     */
    int update(SaDmsuserEntity saDmsuserEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    SaDmsuserPojo getEntityByUserName(@Param("username") String username);
}

