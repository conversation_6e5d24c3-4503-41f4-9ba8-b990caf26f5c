package inks.service.sa.crm.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.SaLeadsitemEntity;
import inks.service.sa.crm.domain.pojo.SaLeadsitemPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 线索记录(SaLeadsitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-03-06 12:46:24
 */
@Mapper
public interface SaLeadsitemMapper {


    SaLeadsitemPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaLeadsitemPojo> getPageList(QueryParam queryParam);

    List<SaLeadsitemPojo> getList(@Param("Pid") String Pid);


    /**
     * 新增数据
     *
     * @param saLeadsitemEntity 实例对象
     * @return 影响行数
     */
    int insert(SaLeadsitemEntity saLeadsitemEntity);


    /**
     * 修改数据
     *
     * @param saLeadsitemEntity 实例对象
     * @return 影响行数
     */
    int update(SaLeadsitemEntity saLeadsitemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

}

