package inks.service.sa.crm.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.SaContractitemEntity;
import inks.service.sa.crm.domain.pojo.SaContractitemPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 合同项目(SaContractitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-03-06 11:18:29
 */
@Mapper
public interface SaContractitemMapper {


    SaContractitemPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaContractitemPojo> getPageList(QueryParam queryParam);

    List<SaContractitemPojo> getList(@Param("Pid") String Pid);


    /**
     * 新增数据
     *
     * @param saContractitemEntity 实例对象
     * @return 影响行数
     */
    int insert(SaContractitemEntity saContractitemEntity);


    /**
     * 修改数据
     *
     * @param saContractitemEntity 实例对象
     * @return 影响行数
     */
    int update(SaContractitemEntity saContractitemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

}

