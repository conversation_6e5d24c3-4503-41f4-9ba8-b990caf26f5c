package inks.service.sa.crm.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.SaStageEntity;
import inks.service.sa.crm.domain.pojo.SaStagePojo;
import inks.service.sa.crm.domain.pojo.SaStageitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 阶段(SaStage)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-02-20 09:09:25
 */
@Mapper
public interface SaStageMapper {


    SaStagePojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaStageitemdetailPojo> getPageList(QueryParam queryParam);


    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaStagePojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saStageEntity 实例对象
     * @return 影响行数
     */
    int insert(SaStageEntity saStageEntity);


    /**
     * 修改数据
     *
     * @param saStageEntity 实例对象
     * @return 影响行数
     */
    int update(SaStageEntity saStageEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    /**
     * 查询 被删除的Item
     *
     * @param saStagePojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelItemIds(SaStagePojo saStagePojo);
}

