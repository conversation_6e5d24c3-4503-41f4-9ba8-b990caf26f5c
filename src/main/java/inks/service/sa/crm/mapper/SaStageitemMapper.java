package inks.service.sa.crm.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.SaStageitemEntity;
import inks.service.sa.crm.domain.pojo.SaStageitemPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 阶段子表(SaStageitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-02-20 09:09:40
 */
@Mapper
public interface SaStageitemMapper {


    SaStageitemPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaStageitemPojo> getPageList(QueryParam queryParam);

    List<SaStageitemPojo> getList(@Param("Pid") String Pid);


    /**
     * 新增数据
     *
     * @param saStageitemEntity 实例对象
     * @return 影响行数
     */
    int insert(SaStageitemEntity saStageitemEntity);


    /**
     * 修改数据
     *
     * @param saStageitemEntity 实例对象
     * @return 影响行数
     */
    int update(SaStageitemEntity saStageitemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

}

