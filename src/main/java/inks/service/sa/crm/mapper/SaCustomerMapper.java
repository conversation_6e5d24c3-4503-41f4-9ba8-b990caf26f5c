package inks.service.sa.crm.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.SaCustomerEntity;
import inks.service.sa.crm.domain.pojo.SaCustomerPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 客户(SaCustomer)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-03-06 14:17:21
 */
@Mapper
public interface SaCustomerMapper {


    SaCustomerPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaCustomerPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param saCustomerEntity 实例对象
     * @return 影响行数
     */
    int insert(SaCustomerEntity saCustomerEntity);


    /**
     * 修改数据
     *
     * @param saCustomerEntity 实例对象
     * @return 影响行数
     */
    int update(SaCustomerEntity saCustomerEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    String getMaxCode();

    SaCustomerPojo getEntityByCustname(String custname);

    SaCustomerPojo getEntityByCustuid(String custuid);

    void syncNextDateByFollowview(String custid);
}
