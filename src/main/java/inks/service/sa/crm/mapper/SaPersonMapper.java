package inks.service.sa.crm.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.SaPersonEntity;
import inks.service.sa.crm.domain.pojo.SaPersonPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 联系人(SaPerson)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-03-06 12:57:01
 */
@Mapper
public interface SaPersonMapper {


    SaPersonPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaPersonPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param saPersonEntity 实例对象
     * @return 影响行数
     */
    int insert(SaPersonEntity saPersonEntity);


    /**
     * 修改数据
     *
     * @param saPersonEntity 实例对象
     * @return 影响行数
     */
    int update(SaPersonEntity saPersonEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    List<SaPersonPojo> getListByLeads(String key);

    List<SaPersonPojo> getListByGroup(String key);
}

