package inks.service.sa.crm.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.pojo.SaSmpplanitemPojo;
import inks.service.sa.crm.domain.SaSmpplanitemEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 计划项目(SaSmpplanitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-09-03 15:18:10
 */
 @Mapper
public interface SaSmpplanitemMapper {

    SaSmpplanitemPojo getEntity(@Param("key") String key);

    List<SaSmpplanitemPojo> getPageList(QueryParam queryParam);

    List<SaSmpplanitemPojo> getList(@Param("Pid") String Pid);    

    int insert(SaSmpplanitemEntity saSmpplanitemEntity);

    int update(SaSmpplanitemEntity saSmpplanitemEntity);

    int delete(@Param("key") String key);

}

