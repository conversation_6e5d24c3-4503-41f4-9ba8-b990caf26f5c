package inks.service.sa.crm.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.SaContractEntity;
import inks.service.sa.crm.domain.pojo.SaContractPojo;
import inks.service.sa.crm.domain.pojo.SaContractitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 合同管理(SaContract)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-03-06 12:58:33
 */
@Mapper
public interface SaContractMapper {


    SaContractPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaContractitemdetailPojo> getPageList(QueryParam queryParam);


    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaContractPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saContractEntity 实例对象
     * @return 影响行数
     */
    int insert(SaContractEntity saContractEntity);


    /**
     * 修改数据
     *
     * @param saContractEntity 实例对象
     * @return 影响行数
     */
    int update(SaContractEntity saContractEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    /**
     * 查询 被删除的Item
     *
     * @param saContractPojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelItemIds(SaContractPojo saContractPojo);

    /**
     * 修改数据
     *
     * @param saContractEntity 实例对象
     * @return 影响行数
     */
    int approval(SaContractEntity saContractEntity);

    void syncBussinessContractAmount(String businessid);
}

