package inks.service.sa.crm.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.pojo.SaBusinessitemPojo;
import inks.service.sa.crm.domain.SaBusinessitemEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 商机子表(SaBusinessitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-07-25 16:29:46
 */
@Mapper
public interface SaBusinessitemMapper {

    SaBusinessitemPojo getEntity(@Param("key") String key);

    List<SaBusinessitemPojo> getPageList(QueryParam queryParam);

    List<SaBusinessitemPojo> getList(@Param("Pid") String Pid);

    int insert(SaBusinessitemEntity saBusinessitemEntity);

    int update(SaBusinessitemEntity saBusinessitemEntity);

    int delete(@Param("key") String key);

}

