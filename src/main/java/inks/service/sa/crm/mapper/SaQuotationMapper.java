package inks.service.sa.crm.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.SaQuotationEntity;
import inks.service.sa.crm.domain.pojo.SaQuotationPojo;
import inks.service.sa.crm.domain.pojo.SaQuotationitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 商机(SaQuotation)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-04-14 10:50:27
 */
@Mapper
public interface SaQuotationMapper {


    SaQuotationPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaQuotationitemdetailPojo> getPageList(QueryParam queryParam);


    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaQuotationPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saQuotationEntity 实例对象
     * @return 影响行数
     */
    int insert(SaQuotationEntity saQuotationEntity);


    /**
     * 修改数据
     *
     * @param saQuotationEntity 实例对象
     * @return 影响行数
     */
    int update(SaQuotationEntity saQuotationEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    /**
     * 查询 被删除的Item
     *
     * @param saQuotationPojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelItemIds(SaQuotationPojo saQuotationPojo);

    /**
     * 修改数据
     *
     * @param saQuotationEntity 实例对象
     * @return 影响行数
     */
    int approval(SaQuotationEntity saQuotationEntity);

    void updateOaflowmark(SaQuotationPojo billPojo);
}

