package inks.service.sa.crm.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.SaInvoiceEntity;
import inks.service.sa.crm.domain.pojo.SaInvoicePojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 发票管理(SaInvoice)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-03-25 14:11:06
 */
@Mapper
public interface SaInvoiceMapper {


    SaInvoicePojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaInvoicePojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param saInvoiceEntity 实例对象
     * @return 影响行数
     */
    int insert(SaInvoiceEntity saInvoiceEntity);


    /**
     * 修改数据
     *
     * @param saInvoiceEntity 实例对象
     * @return 影响行数
     */
    int update(SaInvoiceEntity saInvoiceEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    /**
     * 修改数据
     *
     * @param saInvoiceEntity 实例对象
     * @return 影响行数
     */
    int approval(SaInvoiceEntity saInvoiceEntity);
}

