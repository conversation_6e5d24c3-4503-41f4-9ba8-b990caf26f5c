package inks.service.sa.crm.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.pojo.SaAttributePojo;
import inks.service.sa.crm.domain.SaAttributeEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * SPU属性表(SaAttribute)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-10-25 17:09:06
 */
@Mapper
public interface SaAttributeMapper {


    SaAttributePojo getEntity(@Param("key") String key);

    List<SaAttributePojo> getPageList(QueryParam queryParam);

    int insert(SaAttributeEntity saAttributeEntity);

    int update(SaAttributeEntity saAttributeEntity);

    int delete(@Param("key") String key);

    List<SaAttributePojo> getListByShow(String tid);
}

