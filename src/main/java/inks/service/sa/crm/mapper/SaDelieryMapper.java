package inks.service.sa.crm.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.SaDelieryEntity;
import inks.service.sa.crm.domain.pojo.SaDelieryPojo;
import inks.service.sa.crm.domain.pojo.SaDelieryitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 发出商品(SaDeliery)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-04-05 16:51:44
 */
@Mapper
public interface SaDelieryMapper {


    SaDelieryPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaDelieryitemdetailPojo> getPageList(QueryParam queryParam);


    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaDelieryPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saDelieryEntity 实例对象
     * @return 影响行数
     */
    int insert(SaDelieryEntity saDelieryEntity);


    /**
     * 修改数据
     *
     * @param saDelieryEntity 实例对象
     * @return 影响行数
     */
    int update(SaDelieryEntity saDelieryEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    /**
     * 查询 被删除的Item
     *
     * @param saDelieryPojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelItemIds(SaDelieryPojo saDelieryPojo);

    /**
     * 修改数据
     *
     * @param saDelieryEntity 实例对象
     * @return 影响行数
     */
    int approval(SaDelieryEntity saDelieryEntity);
}

