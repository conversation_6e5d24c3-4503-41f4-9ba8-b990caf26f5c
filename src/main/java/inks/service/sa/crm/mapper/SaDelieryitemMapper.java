package inks.service.sa.crm.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.SaDelieryitemEntity;
import inks.service.sa.crm.domain.pojo.SaDelieryitemPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 发货明细(SaDelieryitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-04-05 16:52:14
 */
@Mapper
public interface SaDelieryitemMapper {


    SaDelieryitemPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaDelieryitemPojo> getPageList(QueryParam queryParam);

    List<SaDelieryitemPojo> getList(@Param("Pid") String Pid);


    /**
     * 新增数据
     *
     * @param saDelieryitemEntity 实例对象
     * @return 影响行数
     */
    int insert(SaDelieryitemEntity saDelieryitemEntity);


    /**
     * 修改数据
     *
     * @param saDelieryitemEntity 实例对象
     * @return 影响行数
     */
    int update(SaDelieryitemEntity saDelieryitemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

}

