package inks.service.sa.crm.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.pojo.SaDemanddictPojo;
import inks.service.sa.crm.domain.SaDemanddictEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 需求字典(SaDemanddict)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-10-11 10:02:22
 */
@Mapper
public interface SaDemanddictMapper {


    SaDemanddictPojo getEntity(@Param("key") String key);

    List<SaDemanddictPojo> getPageList(QueryParam queryParam);

    int insert(SaDemanddictEntity saDemanddictEntity);

    int update(SaDemanddictEntity saDemanddictEntity);

    int delete(@Param("key") String key);
    
}

