package inks.service.sa.crm.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.pojo.SaDemandresearchPojo;
import inks.service.sa.crm.domain.pojo.SaDemandresearchitemdetailPojo;
import inks.service.sa.crm.domain.SaDemandresearchEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 需求调研(SaDemandresearch)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-10-11 10:15:06
 */
@Mapper
public interface SaDemandresearchMapper {

    SaDemandresearchPojo getEntity(@Param("key") String key);

    List<SaDemandresearchitemdetailPojo> getPageList(QueryParam queryParam);

    List<SaDemandresearchPojo> getPageTh(QueryParam queryParam);

    int insert(SaDemandresearchEntity saDemandresearchEntity);

    int update(SaDemandresearchEntity saDemandresearchEntity);

    int delete(@Param("key") String key);

     List<String> getDelItemIds(SaDemandresearchPojo saDemandresearchPojo);

    int approval(SaDemandresearchEntity saDemandresearchEntity);
}

