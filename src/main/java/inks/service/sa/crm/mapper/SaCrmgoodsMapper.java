package inks.service.sa.crm.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.SaCrmgoodsEntity;
import inks.service.sa.crm.domain.pojo.SaCrmgoodsPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 产品(SaCrmgoods)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-03-06 13:00:46
 */
@Mapper
public interface SaCrmgoodsMapper {


    SaCrmgoodsPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaCrmgoodsPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param saCrmgoodsEntity 实例对象
     * @return 影响行数
     */
    int insert(SaCrmgoodsEntity saCrmgoodsEntity);


    /**
     * 修改数据
     *
     * @param saCrmgoodsEntity 实例对象
     * @return 影响行数
     */
    int update(SaCrmgoodsEntity saCrmgoodsEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

}

