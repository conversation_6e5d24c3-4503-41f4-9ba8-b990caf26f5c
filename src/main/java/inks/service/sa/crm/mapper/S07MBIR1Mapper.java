package inks.service.sa.crm.mapper;

import inks.common.core.domain.QueryParam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface S07MBIR1Mapper {

    Map<String, Object> getNewCountThisMonth(QueryParam queryParam);

    List<Map<String, Object>> getNewCountEveryDay(QueryParam queryParam);

    List<Map<String, Object>> getCustomerType(String qpfilter);

    List<Map<String, Object>> getCustomerClass();

    Integer countPublicLeads();

    List<String> getLeadIdsByPrincipalid(@Param("queryParam") QueryParam queryParam, @Param("userid") String userid);

    List<String> getCustomerIdsByUserid(@Param("queryParam") QueryParam queryParam, @Param("lesdids") List<String> lesdids);

    List<String> getAllCustomerIdsByUserid(@Param("queryParam") QueryParam queryParam, @Param("lesdids") List<String> lesdids);

    Map<String, Object> getNewCountAndAmtThisMonth(@Param("queryParam") QueryParam queryParam, @Param("groupids") List<String> groupids);

    List<String> getAllLeadIdsByPrincipalid(QueryParam queryParam, String userid);

    List<Map<String, Object>> getBusinessInfo(QueryParam queryParam);

    Map<String, Object> getContractStatistics(@Param("groupid") String groupid);
}
