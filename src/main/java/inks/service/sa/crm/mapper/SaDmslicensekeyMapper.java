package inks.service.sa.crm.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.SaDmslicensekeyEntity;
import inks.service.sa.crm.domain.pojo.SaDmslicensekeyPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * DMS用户-授权码关联表(SaDmslicensekey)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-12-09 09:56:37
 */
@Mapper
public interface SaDmslicensekeyMapper {


    SaDmslicensekeyPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaDmslicensekeyPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param saDmslicensekeyEntity 实例对象
     * @return 影响行数
     */
    int insert(SaDmslicensekeyEntity saDmslicensekeyEntity);


    /**
     * 修改数据
     *
     * @param saDmslicensekeyEntity 实例对象
     * @return 影响行数
     */
    int update(SaDmslicensekeyEntity saDmslicensekeyEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

}

