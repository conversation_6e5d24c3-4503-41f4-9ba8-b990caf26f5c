package inks.service.sa.crm.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.pojo.SaSmpdeliPojo;
import inks.service.sa.crm.domain.pojo.SaSmpdeliitemdetailPojo;
import inks.service.sa.crm.domain.SaSmpdeliEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 样品发货(SaSmpdeli)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-09-03 16:20:28
 */
@Mapper
public interface SaSmpdeliMapper {

    SaSmpdeliPojo getEntity(@Param("key") String key);

    List<SaSmpdeliitemdetailPojo> getPageList(QueryParam queryParam);

    List<SaSmpdeliPojo> getPageTh(QueryParam queryParam);

    int insert(SaSmpdeliEntity saSmpdeliEntity);

    int update(SaSmpdeliEntity saSmpdeliEntity);

    int delete(@Param("key") String key);

     List<String> getDelItemIds(SaSmpdeliPojo saSmpdeliPojo);

    int approval(SaSmpdeliEntity saSmpdeliEntity);

    void updateFinishCount(String pid, String tid);

    void updateDisannulCountAndAmount(String key, String tid);

    void syncPlanItemFinishQty(String citeitemid);

    void syncPlanFinishCount(String citeitemid);
}

