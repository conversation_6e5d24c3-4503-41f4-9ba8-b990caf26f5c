package inks.service.sa.crm.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.SaPersonleadsEntity;
import inks.service.sa.crm.domain.pojo.SaPersonleadsPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * (SaPersonleads)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-04-13 13:14:03
 */
@Mapper
public interface SaPersonleadsMapper {


    SaPersonleadsPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaPersonleadsPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param saPersonleadsEntity 实例对象
     * @return 影响行数
     */
    int insert(SaPersonleadsEntity saPersonleadsEntity);


    /**
     * 修改数据
     *
     * @param saPersonleadsEntity 实例对象
     * @return 影响行数
     */
    int update(SaPersonleadsEntity saPersonleadsEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    int deleteByLeadsId(String key);
}

