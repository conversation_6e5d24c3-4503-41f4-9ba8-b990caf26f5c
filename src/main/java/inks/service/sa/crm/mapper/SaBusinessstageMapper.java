package inks.service.sa.crm.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.pojo.SaBusinessstagePojo;
import inks.service.sa.crm.domain.SaBusinessstageEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 商机阶段子表(SaBusinessstage)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-07-25 16:30:46
 */
 @Mapper
public interface SaBusinessstageMapper {

    SaBusinessstagePojo getEntity(@Param("key") String key);

    List<SaBusinessstagePojo> getPageList(QueryParam queryParam);

    List<SaBusinessstagePojo> getList(@Param("Pid") String Pid);    

    int insert(SaBusinessstageEntity saBusinessstageEntity);

    int update(SaBusinessstageEntity saBusinessstageEntity);

    int delete(@Param("key") String key);

}

