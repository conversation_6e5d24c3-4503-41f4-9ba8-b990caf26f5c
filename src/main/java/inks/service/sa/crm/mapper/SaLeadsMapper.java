package inks.service.sa.crm.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.SaLeadsEntity;
import inks.service.sa.crm.domain.pojo.SaLeadsPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 线索管理(SaLeads)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-03-22 16:13:30
 */
@Mapper
public interface SaLeadsMapper {


    SaLeadsPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaLeadsPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param saLeadsEntity 实例对象
     * @return 影响行数
     */
    int insert(SaLeadsEntity saLeadsEntity);


    /**
     * 修改数据
     *
     * @param saLeadsEntity 实例对象
     * @return 影响行数
     */
    int update(SaLeadsEntity saLeadsEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    SaLeadsPojo getEntityByChanceid(String chanceid);

    SaLeadsPojo getEntityByCustname(String custname);

    void syncNextDateByFollowview(String leadsid);
}

