package inks.service.sa.crm.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.SaQuotationitemEntity;
import inks.service.sa.crm.domain.pojo.SaQuotationitemPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 商机项目(SaQuotationitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-04-14 10:50:42
 */
@Mapper
public interface SaQuotationitemMapper {


    SaQuotationitemPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaQuotationitemPojo> getPageList(QueryParam queryParam);

    List<SaQuotationitemPojo> getList(@Param("Pid") String Pid);


    /**
     * 新增数据
     *
     * @param saQuotationitemEntity 实例对象
     * @return 影响行数
     */
    int insert(SaQuotationitemEntity saQuotationitemEntity);


    /**
     * 修改数据
     *
     * @param saQuotationitemEntity 实例对象
     * @return 影响行数
     */
    int update(SaQuotationitemEntity saQuotationitemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

}

