package inks.service.sa.crm.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.SaPersongroupEntity;
import inks.service.sa.crm.domain.pojo.SaPersongroupPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * (SaPersongroup)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-04-13 13:14:03
 */
@Mapper
public interface SaPersongroupMapper {


    SaPersongroupPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaPersongroupPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param saPersongroupEntity 实例对象
     * @return 影响行数
     */
    int insert(SaPersongroupEntity saPersongroupEntity);


    /**
     * 修改数据
     *
     * @param saPersongroupEntity 实例对象
     * @return 影响行数
     */
    int update(SaPersongroupEntity saPersongroupEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    int deleteByGroupId(String key);
}

