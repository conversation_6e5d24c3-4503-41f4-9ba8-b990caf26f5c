package inks.service.sa.crm.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.pojo.SaFollowlabelPojo;
import inks.service.sa.crm.domain.SaFollowlabelEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 跟进标签(SaFollowlabel)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-04-24 09:39:51
 */
@Mapper
public interface SaFollowlabelMapper {


    SaFollowlabelPojo getEntity(@Param("key") String key);

    List<SaFollowlabelPojo> getPageList(QueryParam queryParam);

    int insert(SaFollowlabelEntity saFollowlabelEntity);

    int update(SaFollowlabelEntity saFollowlabelEntity);

    int delete(@Param("key") String key);
    
}

