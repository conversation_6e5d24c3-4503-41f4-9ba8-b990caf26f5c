package inks.service.sa.crm.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.pojo.SaSmpdeliitemPojo;
import inks.service.sa.crm.domain.SaSmpdeliitemEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 样品发货明细(SaSmpdeliitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-09-03 16:20:55
 */
 @Mapper
public interface SaSmpdeliitemMapper {

    SaSmpdeliitemPojo getEntity(@Param("key") String key);

    List<SaSmpdeliitemPojo> getPageList(QueryParam queryParam);

    List<SaSmpdeliitemPojo> getList(@Param("Pid") String Pid);    

    int insert(SaSmpdeliitemEntity saSmpdeliitemEntity);

    int update(SaSmpdeliitemEntity saSmpdeliitemEntity);

    int delete(@Param("key") String key);

}

