package inks.service.sa.crm.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.pojo.SaSmpplanPojo;
import inks.service.sa.crm.domain.pojo.SaSmpplanitemdetailPojo;
import inks.service.sa.crm.domain.SaSmpplanEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 样品需求(SaSmpplan)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-09-03 15:17:54
 */
@Mapper
public interface SaSmpplanMapper {

    SaSmpplanPojo getEntity(@Param("key") String key);

    List<SaSmpplanitemdetailPojo> getPageList(QueryParam queryParam);

    List<SaSmpplanPojo> getPageTh(QueryParam queryParam);

    int insert(SaSmpplanEntity saSmpplanEntity);

    int update(SaSmpplanEntity saSmpplanEntity);

    int delete(@Param("key") String key);

     List<String> getDelItemIds(SaSmpplanPojo saSmpplanPojo);

    int approval(SaSmpplanEntity saSmpplanEntity);

    void updateFinishCount(String pid, String tid);

    void updateOaflowmark(SaSmpplanPojo bill);

    int getBusToSmpTimes(String businessid, Date createdate);
}

