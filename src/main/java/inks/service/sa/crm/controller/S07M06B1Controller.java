package inks.service.sa.crm.controller;

import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 合同管理(Sa_Contract)表控制层
 *
 * <AUTHOR>
 * @since 2023-03-06 12:58:32
 */
@RestController
@RequestMapping("S07M06B1")
@Api(tags = "S07M06B1:合同管理")
public class S07M06B1Controller extends SaContractController {


    //@ApiOperation(value = "转化为合同", notes = "转化为合同", produces = "application/json")
    //@RequestMapping(value = "/toContract", method = RequestMethod.POST)
    ////@PreAuthorize(hasPermi = "Crm_Contract.Add")
    //public R<SaContractPojo> toContract(@RequestBody String json){
    //    try{
    //        SaContractPojo saContractPojo = JSON.parseObject(json, SaContractPojo.class);
    //        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
    //
    //        //生成单据编码
    //        String yyyyMMdd = DateUtils.parseDateToStr("yyyyMMdd", new Date());
    //        int ends = new Random().nextInt(9999);
    //        String.format("%04d", ends);
    //        saContractPojo.setRefno(yyyyMMdd + ends);
    //        saContractPojo.setTenantid(loginUser.getTenantid());
    //        saContractPojo.setLister(loginUser.getUsername());
    //        saContractPojo.setListerid(loginUser.getUserid());
    //        saContractPojo.setCreateby(loginUser.getUsername());
    //        saContractPojo.setCreatebyid(loginUser.getUserid());
    //        saContractPojo.setCreatedate(new Date());
    //        saContractPojo.setModifydate(new Date());
    //        saContractPojo.setTenantname(loginUser.getTenantinfo().getTenantname());
    //        SaContractPojo crmContract = saContractService.toContract(saContractPojo);
    //        return R.ok(crmContract);
    //    }catch (Exception e){
    //        return R.fail(e.getMessage());
    //    }
    //}
}
