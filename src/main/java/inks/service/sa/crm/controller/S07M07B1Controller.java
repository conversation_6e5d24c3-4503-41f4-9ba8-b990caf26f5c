package inks.service.sa.crm.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.crm.domain.pojo.SaReceiptplanPojo;
import inks.service.sa.crm.service.SaReceiptplanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 回款(收款)计划(Sa_ReceiptPlan)表控制层
 *
 * <AUTHOR>
 * @since 2023-03-25 10:48:41
 */
@RestController
@RequestMapping("S07M07B1")
@Api(tags = "S07M07B1:回款计划")
public class S07M07B1Controller extends SaReceiptplanController {
    @Resource
    private SaReceiptplanService saReceiptplanService;
    @Resource
    private SaRedisService saRedisService;

    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageList", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_ReceiptPlan.List")
    public R<PageInfo<SaReceiptplanPojo>> getOnlinePageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_ReceiptPlan.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = " and Sa_ReceiptPlan.Closed = 0 " +
                    " and Sa_ReceiptPlan.Amount > Sa_ReceiptPlan.FinishAmt ";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saReceiptplanService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "中止回款(收款)计划", notes = "?type=1中止，0为反作废", produces = "application/json")
    @RequestMapping(value = "/closed", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Bus_Invoice.Edit")
    public R<SaReceiptplanPojo> closed(String key, Integer type) {
        try {
            if (type == null) type = 1;
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saReceiptplanService.closed(key, type, loginUser));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}
