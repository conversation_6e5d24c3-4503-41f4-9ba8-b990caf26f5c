package inks.service.sa.crm.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.constant.CacheConstants;
import inks.common.core.domain.ApprrecPojo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.config.oa.OAHandler;
import inks.sa.common.core.domain.vo.ApprovalCallbackDto;
import inks.sa.common.core.domain.vo.ApprovalRequestDto;
import inks.sa.common.core.service.SaRedisService;
import inks.sa.common.core.utils.OAUtil;
import inks.sa.common.core.utils.PrintColor;
import inks.service.sa.crm.domain.pojo.SaBusinessPojo;
import inks.service.sa.crm.service.SaBusinessService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 商机(Sa_Business)表控制层
 *
 * <AUTHOR>
 * @since 2024-02-20 09:08:46
 */
@RestController
@RequestMapping("S07M05B1")
@Api(tags = "S07M05B1:商机管理2024")
public class S07M05B1Controller extends SaBusinessController {
    @Resource
    private SaBusinessService saBusinessService;
    @Resource
    private SaRedisService saRedisService;
    @Resource
    private OAHandler oaHandler;

    @ApiOperation(value = "查询已审核且状态0的商机", notes = "查询已审核且状态0的商机", produces = "application/json")
    @RequestMapping(value = "/getAssBillList", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_Business.List")
    public R<PageInfo<SaBusinessPojo>> getAssBillList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_Business.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = " and Sa_Business.Assessor <>'' and Sa_Business.StateCode = 0";
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saBusinessService.getBillList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "发出第三方审批", notes = "发出第三方审批wex/ding", produces = "application/json")
    @RequestMapping(value = "/sendapprovel", method = RequestMethod.POST)
    public R<ApprrecPojo> sendapprovel(@RequestBody ApprovalRequestDto request) {
        // 步骤 1: 获取当前登录用户信息
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        if (loginUser == null) {
            return R.fail("用户未登录或登录已超时");
        }
        // 步骤 2: 根据请求中的 key，获取业务实体对象
        SaBusinessPojo billEntity = saBusinessService.getBillEntity(request.getKey());
        if (billEntity == null) {
            return R.fail("找不到对应的业务单据，key: " + request.getKey());
        }
        // 步骤 3: 将业务实体转换为通用的 Map 格式，供后续流程使用
        Map<String, Object> billMap = OAUtil.billToOAMap(billEntity);
        // 步骤 4: 调用核心服务，将所有数据和配置传入
        ApprrecPojo result = oaHandler.sendApproval(request, loginUser, billMap, "Sa_Business");
        // 步骤 5: 返回成功响应
        return R.ok(result);
    }

    @ApiOperation(value = "审批回调修改状态", notes = "审批回调修改状态", produces = "application/json")
    @RequestMapping(value = "/justapprovel", method = RequestMethod.GET)
    public R justapprovel(String key, String type, String approved) {
        // 步骤 2: 读取审批记录获取单据ID
        String verifyKey = CacheConstants.APPR_CODES_KEY + key;
        ApprrecPojo apprrecPojo = saRedisService.getCacheObject(verifyKey, ApprrecPojo.class);
        if (apprrecPojo == null) {
            PrintColor.red("审批记录不存在或已过期");
            return R.fail("审批记录不存在或已过期");
        }
        // 步骤 3: 查询业务单据并转换为Map
        SaBusinessPojo billEntity = saBusinessService.getEntity(apprrecPojo.getBillid());
        if (billEntity == null) {
            PrintColor.red("找不到对应的业务单据，billid: " + apprrecPojo.getBillid());
            return R.fail("找不到对应的业务单据");
        }
        Map<String, Object> billMap = OAUtil.billToOAMap(billEntity);
        // 步骤 4: 构建回调参数DTO
        ApprovalCallbackDto callback = new ApprovalCallbackDto();
        callback.setKey(key);
        callback.setType(type);
        callback.setApproved(approved);
        // 步骤 5: 调用Service层统一处理方法
        return R.ok(oaHandler.handleApprovalCallback(callback, billMap, "Sa_Business"));
    }


    //@ApiOperation(value = "审批回调修改状态", notes = "审批回调修改状态", produces = "application/json")
    //@RequestMapping(value = "/justapprovelOld", method = RequestMethod.GET)
    //public R<SaBusinessPojo> justapprovelOld(String key, String type, String approved) {
    //    try {
    //        PrintColor.red("/justapprovel 审批回调修改状态 approved:" + approved);
    //        //1.读取审批记录
    //        String verifyKey = CacheConstants.APPR_CODES_KEY + key;
    //        ApprrecPojo apprrecPojo = saRedisService.getCacheObject(verifyKey, ApprrecPojo.class);
    //        PrintColor.red("商机回调apprrecPojo" + JSON.toJSONString(apprrecPojo));
    //        //2.1 获得单据数据
    //        SaBusinessPojo billPojo = this.saBusinessService.getEntity(apprrecPojo.getBillid());
    //        //3. 写入审核批
    //        //获得第三方账号
    //        if (type == null) type = "wxe";
    //        // oms审批即将完成,需设置OaFlowMark=0
    //        billPojo.setOaflowmark(0);
    //        saBusinessService.updateOaflowmark(billPojo);//只更新oaflowmark
    //        // 若oms审批点击拒绝(或由发起人取消发起),则设置完OaFlowMark=0直接结束方法
    //        if ("false".equals(approved)) {
    //            return R.ok();
    //        }
    //        if ("oms".equals(type)) {
    //            // 点击同意审批：审批人字段赋值, if包裹外面的approval方法会进行审核
    //            billPojo.setAssessorid(apprrecPojo.getUserid());
    //            billPojo.setAssessor(apprrecPojo.getRealname()); //审核员
    //        } else {
    //            SaJustauthPojo justauthByUuid = saJustauthService.getJustauthByUuid(apprrecPojo.getCallbackuuid(), type, null);
    //            if (justauthByUuid == null) {
    //                PrintColor.red("D03M01B1/justapprovel审批回调失败,未找到第三方账号");
    //                return R.fail("审批回调失败,未找到第三方账号");
    //            }
    //            JustauthPojo justauthPojo = new JustauthPojo();
    //            org.springframework.beans.BeanUtils.copyProperties(justauthByUuid, justauthPojo);
    //            billPojo.setAssessorid(justauthPojo.getUserid());
    //            billPojo.setAssessor(justauthPojo.getRealname()); //审核员
    //        }
    //        billPojo.setAssessdate(new Date()); //审核时间
    //        return R.ok(this.saBusinessService.approval(billPojo));
    //    } catch (Exception e) {
    //        System.out.println("写入审核失败：" + e.getMessage());
    //        return R.fail(e.getMessage());
    //    }
    //}


//    @ApiOperation(value = "发出第三方审批", notes = "发出第三方审批wex/ding", produces = "application/json")
//    @RequestMapping(value = "/sendapprovelOld", method = RequestMethod.POST)
//    public R<ApprrecPojo> sendapprovelOld(@RequestBody String json) {
//        try {
//            // 解析JSON参数
//            JSONObject jsonObject = JSONObject.parseObject(json);
//            String key = jsonObject.getString("key");
//            String apprid = jsonObject.getString("apprid");
//            String type = jsonObject.getString("type");
//            String remark = jsonObject.getString("remark");
//            String process = jsonObject.getString("process"); // 新增process参数
//
//            if (type == null) type = "wxe";  // 默认走企业微信
//            String verifyKey = CacheConstants.APPR_CODES_KEY + apprid;
//            //获取token
//            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
//            //从redis中获取模板对象
//            // Object obj = saRedisService.getCacheObject(verifyKey);
//            ApprrecPojo apprrecPojo = saRedisService.getCacheObject(verifyKey, ApprrecPojo.class);
//            ApprovePojo approvePojo = new ApprovePojo();
//            SaBusinessPojo billEntity = this.saBusinessService.getBillEntity(key);
//            Map<String, Object> billMap = OAUtil.billToOAMap(billEntity);
//            approvePojo.setObject(billMap);
//
//            // 发起oms审批,先判断是否正在审批 (最下面发起oms审批成功后需设置OaFlowMark=1)
//            if (billEntity.getOaflowmark() != null && billEntity.getOaflowmark() == 1) {
//                return R.fail("该单据已发起OA审批");
//            }
//            if ("oms".equals(type)) {
//                //创建VM数据对象
//                VelocityContext context = new VelocityContext();
//                context.put("approvePojo", approvePojo);
//                String str = apprrecPojo.getDatatemp();
//                // 初始化并取得Velocity引擎
//                VelocityEngine ve = new VelocityEngine();
//                ve.init();
//                // 转换输出
//                StringWriter writer = new StringWriter();
//                ve.evaluate(context, writer, "", str); // 关键方法
//                //写回String
//                str = writer.toString();
//                // 如果传入了process参数，替换模板中的process部分
//                if (StringUtils.isNotBlank(process)) {
//                    try {
//                        JSONObject jsonTemplate = JSONObject.parseObject(str);
//                        JSONObject processObj = JSONObject.parseObject(process);
//                        jsonTemplate.put("process", processObj);
//                        str = jsonTemplate.toJSONString();
//                    } catch (Exception e) {
//                        PrintColor.red("替换process参数失败: " + e.getMessage());
//                    }
//                }
//                apprrecPojo.setDatatemp(str);
////                String data = JSONObject.toJSONString(approvePojo.getObject());
////                apprrecPojo.setDatatemp(data);
//            } else {
//                //创建VM数据对象
//                VelocityContext context = new VelocityContext();
//                //获得第三方账号
//                SaJustauthPojo justAuth = saJustauthService.getJustauthByUserid(loginUser.getUserid(), type, null);
//                JustauthPojo justauthPojo = new JustauthPojo();
//                if (justAuth == null) {
//                    PrintColor.red("审批发起人 获得第三方账号出错");
//                    return R.fail("审批发起人 获得第三方账号出错");
//                }
//                org.springframework.beans.BeanUtils.copyProperties(justAuth, justauthPojo);
//                approvePojo.setCreatoruserid(justauthPojo.getAuthuuid());
//                approvePojo.setUserid(justauthPojo.getAuthuuid());
//                approvePojo.setModelcode(apprrecPojo.getTemplateid());
//                context.put("approvePojo", approvePojo);
//                String str = apprrecPojo.getDatatemp();
//                // 初始化并取得Velocity引擎
//                VelocityEngine ve = new VelocityEngine();
//                ve.init();
//                // 转换输出
//                StringWriter writer = new StringWriter();
//                ve.evaluate(context, writer, "", str); // 关键方法
//                //写回String
//                str = writer.toString();
//                // 如果传入了process参数，替换模板中的process部分
//                if (StringUtils.isNotBlank(process)) {
//                    try {
//                        JSONObject jsonTemplate = JSONObject.parseObject(str);
//                        JSONObject processObj = JSONObject.parseObject(process);
//                        jsonTemplate.put("process", processObj);
//                        str = jsonTemplate.toJSONString();
//                    } catch (Exception e) {
//                        PrintColor.red("替换process参数失败: " + e.getMessage());
//                    }
//                }
//                apprrecPojo.setDatatemp(str);
//            }
//            //新建审批记录
//            apprrecPojo.setId(inksSnowflake.getSnowflake().nextIdStr());
//            apprrecPojo.setApprname("订单审批");
//            apprrecPojo.setResultcode("");
//            apprrecPojo.setBillid(key);    // 单据ID
//            apprrecPojo.setUserid("");
//            apprrecPojo.setApprtype("");
//            apprrecPojo.setCreateby(loginUser.getRealname());
//            apprrecPojo.setCreatebyid(loginUser.getUserid());
//            apprrecPojo.setCreatedate(new Date());
//            apprrecPojo.setLister(loginUser.getRealname());
//            apprrecPojo.setListerid(loginUser.getUserid());
//            apprrecPojo.setModifydate(new Date());
//            apprrecPojo.setTenantid(loginUser.getTenantid());
//            //发起Flowable审批时加入的评论备注
//            apprrecPojo.setRemark(remark == null ? "" : remark);
//            //将企业微信审批信息存入redis
//            String CachKey = CacheConstants.APPR_CODES_KEY + apprrecPojo.getId();
//            saRedisService.setKeyValue(CachKey, apprrecPojo, 60 * 12, TimeUnit.MINUTES);
//            if ("wxe".equals(type) || "ding".equals(type)) {
//                R r = this.OAHandler.sendApproval(apprrecPojo.getId(), type);
//                if (r.getCode() != 200) {
//                    return R.fail("发起审批失败" + r.getMsg());
//                }
//            }
//            // 发起oms审批成功,需设置OaFlowMark=1 并更新单据
//            billEntity.setOaflowmark(1);
//            saBusinessService.updateOaflowmark(billEntity);
//            return R.ok(apprrecPojo);
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }


}
