package inks.service.sa.crm.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.domain.TenantInfo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.crm.domain.pojo.SaLeadsPojo;
import inks.service.sa.crm.service.SaLeadsService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 线索管理(Sa_Leads)表控制层
 *
 * <AUTHOR>
 * @since 2023-03-22 16:13:29
 */
//@RestController
//@RequestMapping("saLeads")
public class SaLeadsController {

    private final static Logger logger = LoggerFactory.getLogger(SaLeadsController.class);
    @Resource
    private SaLeadsService saLeadsService;
    @Resource
    private SaRedisService saRedisService;

    @ApiOperation(value = " 获取线索管理详细信息", notes = "获取线索管理详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    public R<SaLeadsPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saLeadsService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增线索管理", notes = "新增线索管理", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    public R<SaLeadsPojo> create(@RequestBody String json) {
        try {
            SaLeadsPojo saLeadsPojo = JSONArray.parseObject(json, SaLeadsPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saLeadsPojo.setCreateby(loginUser.getRealName());   // 创建者
            saLeadsPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saLeadsPojo.setCreatedate(new Date());   // 创建时间
            saLeadsPojo.setLister(loginUser.getRealname());   // 制表
            saLeadsPojo.setListerid(loginUser.getUserid());    // 制表id  
            saLeadsPojo.setModifydate(new Date());   //修改时间
            // 加入部门id
            Optional.ofNullable(loginUser.getTenantinfo())
                    .map(TenantInfo::getDeptid)
                    .filter(StringUtils::isNotBlank)
                    .ifPresent(saLeadsPojo::setDeptid);
            return R.ok(this.saLeadsService.insert(saLeadsPojo, loginUser));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改线索管理", notes = "修改线索管理", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public R<SaLeadsPojo> update(@RequestBody String json) {
        try {
            SaLeadsPojo saLeadsPojo = JSONArray.parseObject(json, SaLeadsPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saLeadsPojo.setLister(loginUser.getRealname());   // 制表
            saLeadsPojo.setListerid(loginUser.getUserid());    // 制表id  
            saLeadsPojo.setModifydate(new Date());   //修改时间
//            saLeadsPojo.setAssessor(""); // 审核员
//            saLeadsPojo.setAssessorid(""); // 审核员id
//            saLeadsPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saLeadsService.update(saLeadsPojo, loginUser));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除线索管理", notes = "删除线索管理", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saLeadsService.delete(key, loginUser));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaLeadsPojo saLeadsPojo = this.saLeadsService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saLeadsPojo);
        // 加入公司信息
        if (loginUser.getTenantinfo() != null) inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }

    @ApiOperation(value = "云打印报表", notes = "打印报表 key=billid,ptid打印模版,sn远程打印SN(可选)", produces = "application/json")
    @RequestMapping(value = "/printWebBill", method = RequestMethod.GET)
    public R<String> printWebBill(String key, String ptid, String sn, Integer cmd, Integer redis) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //=========获取单据表头信息========
            SaLeadsPojo saLeadsPojo = this.saLeadsService.getEntity(key);
            // 获取单据表头.表头转MAP
            Map<String, Object> map = BeanUtils.beanToMap(saLeadsPojo);
            // 获取单据表头.加入公司信息
            if (loginUser.getTenantinfo() != null)
                inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
            //=========获取单据Item信息========
            // 单据Item. 带属性List转为Map  EricRen 20220427
            List<Map<String, Object>> lst = null;

            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            maprow.put("row", lst);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            mapreport.put("_grparam", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");
            logger.info(ptJson);
            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();

            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "线索");    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = inksSnowflake.getSnowflake().nextIdStr();
                saRedisService.setCacheObject("report_data:" + rediskey, ptJson, 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", ptJson);   //  打印数据
            }
            // 云打印模板，加载  兼容 URL模式
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());  // 编辑权限

            // 本地打印
            return R.ok(JSONObject.toJSONString(mapPrint));

        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}

