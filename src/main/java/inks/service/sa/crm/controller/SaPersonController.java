package inks.service.sa.crm.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.crm.domain.pojo.SaPersonPojo;
import inks.service.sa.crm.service.SaPersonService;
import inks.service.sa.crm.service.SaPersongroupService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 联系人(Sa_Person)表控制层
 *
 * <AUTHOR>
 * @since 2023-03-06 12:57:00
 */
//@RestController
//@RequestMapping("saPerson")
public class SaPersonController {

    private final static Logger logger = LoggerFactory.getLogger(SaPersonController.class);
    @Resource
    private SaPersonService saPersonService;
    @Resource
    private SaRedisService saRedisService;
    @Resource
    private SaPersongroupService saPersongroupService;

    @ApiOperation(value = " 获取联系人详细信息", notes = "获取联系人详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    public R<SaPersonPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saPersonService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    public R<PageInfo<SaPersonPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_Person.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saPersonService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增联系人", notes = "新增联系人", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    ////////@PreAuthorize(hasPermi = "Sa_Person.Add")
    public R<SaPersonPojo> create(@RequestBody String json) {
        try {
            SaPersonPojo saPersonPojo = JSONArray.parseObject(json, SaPersonPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saPersonPojo.setCreateby(loginUser.getRealName());   // 创建者
            saPersonPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saPersonPojo.setCreatedate(new Date());   // 创建时间
            saPersonPojo.setLister(loginUser.getRealname());   // 制表
            saPersonPojo.setListerid(loginUser.getUserid());    // 制表id  
            saPersonPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saPersonService.insert(saPersonPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改联系人", notes = "修改联系人", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public R<SaPersonPojo> update(@RequestBody String json) {
        try {
            SaPersonPojo saPersonPojo = JSONArray.parseObject(json, SaPersonPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saPersonPojo.setLister(loginUser.getRealname());   // 制表
            saPersonPojo.setListerid(loginUser.getUserid());    // 制表id  
            saPersonPojo.setModifydate(new Date());   //修改时间
//            saPersonPojo.setAssessor(""); // 审核员
//            saPersonPojo.setAssessorid(""); // 审核员id
//            saPersonPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saPersonService.update(saPersonPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除联系人", notes = "删除联系人", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saPersonService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaPersonPojo saPersonPojo = this.saPersonService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saPersonPojo);
        // 加入公司信息
        if (loginUser.getTenantinfo() != null) inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

