//package inks.service.sa.crm.controller;
//
//
//import inks.common.core.domain.LoginUser;
//import inks.common.core.domain.R;
//import inks.common.core.text.inksSnowflake;
//import inks.common.core.utils.ServletUtils;
//import inks.common.core.utils.StringUtils;
//import inks.common.redis.service.RedisService;
//import inks.sa.common.core.service.SaRedisService;
//import inks.service.sa.crm.domain.pojo.LoginBody;
//import inks.service.sa.crm.service.SysDmsLogingService;
//import io.swagger.annotations.ApiOperation;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.web.bind.annotation.*;
//
//import javax.annotation.Resource;
//import javax.servlet.http.HttpServletRequest;
//import java.text.ParseException;
//import java.util.HashMap;
//import java.util.Map;
//
//@RestController
//@RequestMapping("/dms")
//public class DmsTokenController {
//    @Autowired
//    private SaRedisService saRedisService;
//    @Resource
//    private SysDmsLogingService service;
//
////    @Resource
////    private SysLoginService sysLoginService;
//
////    /**
////     * 引用FeignService服务
////     */
////    @Resource
////    private SystemFeignService systemFeignService;
////    @Resource
////    private SysDmsLogingMapper sysDmsLogingMapper;
//
//
//    @ApiOperation(value = "dms用户登陆", notes = "用户登陆", produces = "application/json")
//    @RequestMapping(value = "/login", method = RequestMethod.POST)
//    public R<Object> login(@RequestBody LoginBody loginBody, HttpServletRequest request) {
//        try {
//            LoginUser l = service.login(loginBody.getUserName(), loginBody.getPassword(), request);
//            Map<String, Object> map = saRedisService.createToken(l);
//            return R.ok(map.get("loginuser"));
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }
//
//    @DeleteMapping("logout")
//    public R logout(HttpServletRequest request) throws ParseException {
//        LoginUser loginUser = saRedisService.getLoginUser(request);
//        if (StringUtils.isNotNull(loginUser)) {
//            String username = loginUser.getUsername();
//            // 删除用户缓存记录
//            saRedisService.delLoginUser(loginUser.getToken());
//            service.record(loginUser, request);
//        }
//        return R.ok("");
//    }
//
////    @ApiOperation(value = "根据当前用户获得租户关系表", notes = "根据当前用户获得租户关系表", produces = "application/json")
////    @RequestMapping(value = "/getListByUser", method = RequestMethod.GET)
////    public R<List<PiTenant>> getListByUser() {
////        try {
////            // 获得用户数据
////            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
////            List<SaDmsuserPojo> pidmsuserPojoList = sysDmsLogingMapper.getListByUserid(loginUser.getUserid());
////            List<String> tidList = pidmsuserPojoList.stream()
////                    .map(SaDmsuserPojo::getTenantid)
////                    .collect(Collectors.toList());
////            List<PiTenant> piTenantList = sysDmsLogingMapper.getListInTids(tidList);
////            return R.ok(piTenantList);
////        } catch (Exception e) {
////            return R.fail(e.getMessage());
////        }
////    }
//
////    @ApiOperation(value = "选择租户后生成新token", notes = "选择租户后生成新token", produces = "application/json")
////    @RequestMapping(value = "/token", method = RequestMethod.POST)
////    public R<Map<String, Object>> token(@RequestBody String key) {
////        try {
////            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
////            loginUser.setTenantid(key);
//////            //加入租户信息
////            TenantInfo tenantInfo = this.sysLoginService.getTenantInfo(key);
////            tenantInfo.setIsdeptadmin(0);
////            // 加入架构信息
////            List<DeptinfoPojo> lstdept = null;
////            SaDmsuserPojo pidmsuserPojo = sysDmsLogingMapper.getDmsUserByUserid(loginUser.getUserid(), loginUser.getTenantid());
//////            R<Map<String, Object>> rinfo = this.systemFeignService.getTenantDmsUserByUser(loginUser.getUserid(), loginUser.getTenantid());
////            if (pidmsuserPojo != null) {
//////                String groupids = rinfo.getData().get("groupids") == null ? "" : rinfo.getData().get("groupids").toString();
////                String groupids = pidmsuserPojo.getGroupids();
////                //给groupids加上单引号  由 aa,ss,dd 改为'aa','ss','dd'
////                groupids = Arrays.stream(groupids.split(","))
////                        .map(groupId -> "'" + groupId + "'")
////                        .collect(Collectors.joining(","));
////                loginUser.setGroupids(groupids);
////                //int isadmin = rinfo.getData().get("isadmin") == null ? 0 : Integer.parseInt(rinfo.getData().get("isadmin").toString());
////                //loginUser.setIsadmin(isadmin);
////            }
////            loginUser.setTenantinfo(tenantInfo);
////            // 更新登录日志的tid
////            CiLoginLogEntity ciLoginLogEntity = new CiLoginLogEntity();
////            ciLoginLogEntity.setTenantid(key);
////            ciLoginLogEntity.setTenantName(piTenantMapper.getTenantName(key));
////            ciLoginLogEntity.setId(loginUser.getLogid());
////            ciLoginLogMapper.update(ciLoginLogEntity);
////            // 建立新的loginUser
////            Map<String, Object> map = saRedisService.createToken(loginUser);
////            //加入权限编码
////            R<Map<String, Object>> r = systemFeignService.updateToken(loginUser.getToken());
////            if (r.getCode() == 200) map = r.getData();
////            return R.ok(map);
////        } catch (Exception e) {
////            return R.fail(e.getMessage());
////        }
////    }
//
//    //------------------------------------扫码登录(WX小程序)---------------------------------
//    private final String SCANLOGIN_CODE = "scanlogin_code:";//扫码登录
//
//    @Value("${inks.justauth.api}")
//    private String api;
//
//    @ApiOperation(value = "获取绑定openid的二维码String", notes = "", produces = "application/json")
//    @RequestMapping(value = "/getScanBindCode", method = RequestMethod.GET)
//    public R<Map<String, Object>> getScanBindCode() {
//        try {
//            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
//            //生成key 即UUID
//            String uuid = inksSnowflake.getSnowflake().nextIdStr();
//            //code100存入redis
//            Map<String, Object> missionMsg = new HashMap<>();
//            missionMsg.put("code", "100");
//            missionMsg.put("msg", "任务开始处理");
//            this.saRedisService.setCacheMapValue(SCANLOGIN_CODE, uuid, missionMsg);
//            //设置过期时间6分钟
//            saRedisService.expire(SCANLOGIN_CODE, 60 * 6);
//            //返回前端type,date(key)
//            Map<String, Object> map = new HashMap<>();
//            map.put("type", "sadmsbind");
//            Map<String, Object> dataMap = new HashMap<>();
////            dataMap.put("key", uuid);
//            dataMap.put("api", api);
//            dataMap.put("userid", loginUser.getUserid());
//            dataMap.put("tenantid", loginUser.getTenantid());
//            map.put("data", dataMap);
//            return R.ok(map);
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }
//
//    @ApiOperation(value = "获取解绑openid的二维码String", notes = "", produces = "application/json")
//    @RequestMapping(value = "/getScanUnBindCode", method = RequestMethod.GET)
//    public R<Map<String, Object>> getScanUnBindCode() {
//        try {
//            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
//            //生成key 即UUID
//            String uuid = inksSnowflake.getSnowflake().nextIdStr();
//            //code100存入redis
//            Map<String, Object> missionMsg = new HashMap<>();
//            missionMsg.put("code", "100");
//            missionMsg.put("msg", "任务开始处理");
//            this.saRedisService.setCacheMapValue(SCANLOGIN_CODE, uuid, missionMsg);
//            //设置过期时间6分钟
//            saRedisService.expire(SCANLOGIN_CODE, 60 * 6);
//            //返回前端type,date(key)
//            Map<String, Object> map = new HashMap<>();
//            map.put("type", "sacrmunbind");
//            Map<String, Object> dataMap = new HashMap<>();
////            dataMap.put("key", uuid);
//            dataMap.put("api", api);
//            dataMap.put("userid", loginUser.getUserid());
//            dataMap.put("tenantid", loginUser.getTenantid());
//            map.put("data", dataMap);
//            return R.ok(map);
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }
//
//    @ApiOperation(value = "获取登录二维码String", notes = "获取登录二维码String", produces = "application/json")
//    @RequestMapping(value = "/getScanLoginCode", method = RequestMethod.GET)
//    public R<Map<String, Object>> getScanLoginCode() {
//        try {
//            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
//            //生成key 即UUID
//            String uuid = inksSnowflake.getSnowflake().nextIdStr();
//            //code100存入redis
//            Map<String, Object> missionMsg = new HashMap<>();
//            missionMsg.put("code", "100");
//            missionMsg.put("msg", "任务开始处理");
//            this.saRedisService.setCacheMapValue(SCANLOGIN_CODE, uuid, missionMsg);
//            //设置过期时间6分钟
//            saRedisService.expire(SCANLOGIN_CODE, 60 * 6);
//            //返回前端type,date(key)
//            Map<String, Object> map = new HashMap<>();
//            map.put("type", "webdmslogin");
//            Map<String, Object> dataMap = new HashMap<>();
//            dataMap.put("key", uuid);
//            dataMap.put("api", api);
//            map.put("data", dataMap);
//            return R.ok(map);
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }
//
//
//    /**
//     * 接受dms服务传来的openid,key为redis中的key
//     */
//    @ApiOperation(value = "扫码登录", notes = "扫码登录", produces = "application/json")
//    @RequestMapping(value = "/scanLoginCode", method = RequestMethod.GET)
//    public R<String> scanLoginCode(String openid, String key, HttpServletRequest request) throws ParseException {
//        Map<String, Object> tokenMap = null;
//        LoginUser l = service.scanLogin(openid, key, request);
//        tokenMap = saRedisService.createToken(l);
//        //设置当前计算任务进度
//        Map<String, Object> missionMsg = new HashMap<>();
//        missionMsg.put("code", "200");
//        missionMsg.put("msg", "登录成功");
//        missionMsg.put("token", tokenMap);
//        this.saRedisService.setCacheMapValue(SCANLOGIN_CODE, key, missionMsg);
//        saRedisService.expire(SCANLOGIN_CODE, 60 * 6);
//        return R.ok("扫码登录成功");
//    }
////    @ApiOperation(value = "dms用户登陆", notes = "用户登陆", produces = "application/json")
////    @RequestMapping(value = "/login222", method = RequestMethod.POST)
////    public R<Object> login222(@RequestBody LoginBody loginBody, HttpServletRequest request) {
////        try {
////            LoginUser l = service.login(loginBody.getUserName(), loginBody.getPassword(), request);
////            Map<String, Object> map = saRedisService.createToken(l);
////            return R.ok(map.get("loginuser"));
////        } catch (Exception e) {
////            return R.fail(e.getMessage());
////        }
////    }
//    @ApiOperation(value = "获取扫码登录状态", notes = "获取扫码登录状态", produces = "application/json")
//    @RequestMapping(value = "/getScanLoginState", method = RequestMethod.GET)
//    public R<Map<String, Object>> getScanLoginState(@RequestParam String key) {
//        Map<String, Object> scanLoginState = this.saRedisService.getCacheMapValue(SCANLOGIN_CODE, key);
//        return R.ok(scanLoginState);
//    }
//
//
//}
