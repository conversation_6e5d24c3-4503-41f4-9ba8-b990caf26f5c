package inks.service.sa.crm.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.POIUtil;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.StringUtils;

import inks.sa.common.core.annotation.PreAuthorize;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.crm.domain.pojo.SaLeadsPojo;
import inks.service.sa.crm.service.SaLeadsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import static inks.sa.common.core.utils.SqlUtil.filterDeptid;

/**
 * 线索管理(Sa_Leads)表控制层
 *
 * <AUTHOR>
 * @since 2023-03-22 16:13:29
 */
@RestController
@RequestMapping("S07M02B1")
@Api(tags = "S07M02B1:线索管理")
public class S07M02B1Controller extends SaLeadsController {

    @Resource
    private SaLeadsService saLeadsService;


    @Resource
    private SaRedisService saRedisService;

    @ApiOperation(value = "获取公海线索", notes = "获取公海线索", produces = "application/json")
    @RequestMapping(value = "/getPublicPageList", method = RequestMethod.POST)
    
    public R<PageInfo<SaLeadsPojo>> getPublicPageList(@RequestBody String json) {
        try {
            LoginUser loginUser = this.saRedisService.getLoginUser(ServletUtils.getRequest());
            QueryParam queryParam = JSON.parseObject(json, QueryParam.class);
            if (StringUtils.isEmpty(queryParam.getOrderBy())) queryParam.setOrderBy("Sa_Leads.CreateDate");
            String qpfilter = " and Sa_Leads.Principalid = ''";
            qpfilter += " and Sa_Leads.groupid =''";
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            qpfilter = filterDeptid(qpfilter, loginUser, "Sa_Leads.Deptid", "module.customer.deptpartition");// 部门权限过滤
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saLeadsService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "获取公海线索", notes = "获取公海线索", produces = "application/json")
    @RequestMapping(value = "/getFreePageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Sa_Leads.FreeList")
    public R<PageInfo<SaLeadsPojo>> getFreePageList(@RequestBody String json) {
        try {
            LoginUser loginUser = this.saRedisService.getLoginUser(ServletUtils.getRequest());
            QueryParam queryParam = JSON.parseObject(json, QueryParam.class);
            if (StringUtils.isEmpty(queryParam.getOrderBy())) queryParam.setOrderBy("Sa_Leads.CreateDate");
            String qpfilter = " and Sa_Leads.Principalid = ''";
            qpfilter += " and Sa_Leads.groupid =''";
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saLeadsService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "获取私海线索", notes = "获取私海线索", produces = "application/json")
    @RequestMapping(value = "/getPrivatePageList", method = RequestMethod.POST)
    
    public R<PageInfo<SaLeadsPojo>> getPrivatePageList(@RequestBody String json) {
        try {
            LoginUser loginUser = this.saRedisService.getLoginUser(ServletUtils.getRequest());
            QueryParam queryParam = JSON.parseObject(json, QueryParam.class);
            if (StringUtils.isEmpty(queryParam.getOrderBy())) queryParam.setOrderBy("Sa_Leads.CreateDate");
            String qpfilter = "";
            qpfilter += " and Sa_Leads.groupid =''";
            qpfilter += " and Sa_Leads.Principalid !=''";
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            qpfilter = filterDeptid(qpfilter, loginUser, "Sa_Leads.Deptid", "module.customer.deptpartition");// 部门权限过滤
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saLeadsService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "按条件分页查询 入own =1 我的，0所有，-1 公海； null时赋值为1 查负责人Principalid=自己", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    
    public R<PageInfo<SaLeadsPojo>> getPageList(@RequestBody String json,
                                                @RequestParam(required = false, defaultValue = "1") Integer own) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_Leads.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";

            switch (own) {
                case 1:// 我的
                    qpfilter += " and Sa_Leads.Principalid = '" + loginUser.getUserid() + "'";
                    break;
                case 0:// 所有
                    break;
                case -1:// 公海
                    qpfilter += " and Sa_Leads.Principalid = ''";
                    break;
                default:
                    throw new BaseBusinessException("own参数错误");
            }
            qpfilter += queryParam.getAllFilter();
            qpfilter = filterDeptid(qpfilter, loginUser, "Sa_Leads.Deptid", "module.customer.deptpartition");// 部门权限过滤
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saLeadsService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询 入own =1 我的，0查询所有有负责人的线索，-1 公海； null时赋值为1 查负责人Principalid=自己", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageList", method = RequestMethod.POST)
    
    public R<PageInfo<SaLeadsPojo>> getOnlinePageList(@RequestBody String json,
                                                      @RequestParam(required = false, defaultValue = "1") Integer own) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_Leads.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = " and Sa_Leads.Closed = 0";
            switch (own) {
                case 1:// 我的
                    qpfilter += " and Sa_Leads.Principalid = '" + loginUser.getUserid() + "'";
                    break;
                case 0:// 查询所有有负责人的线索
                    qpfilter += " and Sa_Leads.Principalid !=''";
                    break;
                case -1:// 公海
                    qpfilter += " and Sa_Leads.Principalid = ''";
                    break;
                default:
                    throw new BaseBusinessException("own参数错误");
            }
            qpfilter += queryParam.getAllFilter();
            qpfilter = filterDeptid(qpfilter, loginUser, "Sa_Leads.Deptid", "module.customer.deptpartition");// 部门权限过滤
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saLeadsService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


//    @ApiOperation(value = "获取结余线索 key=1 查负责人Principalid=自己,key=0查负责人为空''", notes = "获取结余线索", produces = "application/json")
//    @RequestMapping(value = "/getOnlinePageList", method = RequestMethod.POST)
//    ////@PreAuthorize(hasPermi = "Sa_Leads.List")
//    public R<PageInfo<SaLeadsPojo>> getOnlinePageList(@RequestBody String json, String key) {
//        try {
//            LoginUser loginUser = this.saRedisService.getLoginUser(ServletUtils.getRequest());
//            QueryParam queryParam = JSON.parseObject(json, QueryParam.class);
//            if (StringUtils.isEmpty(queryParam.getOrderBy())) queryParam.setOrderBy("Sa_Leads.CreateDate");
//            //
//            String qpfilter = " and Sa_Leads.Groupid = ''";
//            qpfilter += " and Sa_Leads.Closed = 0";
//            if ("1".equals(key)) {
//                qpfilter += " and Sa_Leads.Principalid = '" + loginUser.getUserid() + "'";
//            } else if ("0".equals(key)) {
//                qpfilter += " and Sa_Leads.Principalid = ''";
//            }
//            // 加入场景   Eric 20221124
//            qpfilter += queryParam.getAllFilter();
//            queryParam.setFilterstr(qpfilter);
//            return R.ok(this.saLeadsService.getPageList(queryParam));
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }

    //================导入导出=======================
    @ApiOperation(value = "导出数据", notes = "导出数据", produces = "application/json")
    @RequestMapping(value = "/exportList", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Mat_Goods.List")
    public void exportList(@RequestBody String json, String groupid, HttpServletRequest request, HttpServletResponse response) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || Objects.equals(queryParam.getOrderBy(), "")) {
                queryParam.setOrderBy("CreateDate");
            }
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Mat_Goods.deletemark=0";
            if (groupid != null) {
                qpfilter += " and Mat_Goods.Groupid='" + groupid + "'";
            }
            // 加入场景   Nanno 20230203
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams("线索", ""),
                    SaLeadsPojo.class, this.saLeadsService.getPageList(queryParam).getList());
            POIUtil.downloadWorkbook(workbook, request, response, "线索");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /*
     * esay poi导入导出 时间2021-12-13 song
     * */
    @ApiOperation(value = "模板导出", notes = "模板导出", produces = "application/json")
    @RequestMapping(value = "/exportModel", method = RequestMethod.GET)
    public void exportModel(HttpServletRequest request, HttpServletResponse response) {
        List<SaLeadsPojo> list = new ArrayList<>();
        //创建表格
        Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams("线索", ""),
                SaLeadsPojo.class, list);
        try {
            //下载模板
            POIUtil.downloadWorkbook(workbook, request, response, "线索模板");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    @ApiOperation(value = "导入数据", notes = "导入数据", produces = "application/json")
    @RequestMapping(value = "/importExecl", method = RequestMethod.POST)
    public R<List<SaLeadsPojo>> importExecl(String groupid, MultipartFile file, HttpServletRequest request, HttpServletResponse response) throws Exception {
        try {
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //去掉标题和表头
            ImportParams importParams = POIUtil.createImportParams(0, 1);
            //将表格数据写入List
            List<SaLeadsPojo> list = POIUtil.importExcel(file.getInputStream(), SaLeadsPojo.class, importParams);
            return R.ok(list);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "识别编码 By Custname", notes = "识别编码", produces = "application/json")
    @RequestMapping(value = "/getEntityBynsp", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Mat_Goods.List")
    public R<SaLeadsPojo> getEntityBynsp(@RequestBody String json) {
        try {
            SaLeadsPojo saLeadsPojo = JSONArray.parseObject(json, SaLeadsPojo.class);
            if (saLeadsPojo.getCustname() == null) {
                saLeadsPojo.setCustname("");
            }
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            SaLeadsPojo leadsPojo = saLeadsService.getEntityByCustname(saLeadsPojo.getCustname());
            return R.ok(leadsPojo);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "导入信息：线索", notes = "", produces = "application/json")
    @RequestMapping(value = "/importEntity", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Mat_Goods.Add")
    public R<SaLeadsPojo> importEntity(@RequestBody String json) {
        try {
            SaLeadsPojo saLeadsPojo = JSONArray.parseObject(json, SaLeadsPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saLeadsPojo.setCreateby(loginUser.getRealname());   //创建者
            saLeadsPojo.setCreatedate(new Date());   //创建时间
            saLeadsPojo.setLister(loginUser.getRealname());   //用户名
            saLeadsPojo.setModifydate(new Date());   //修改时间
            saLeadsPojo.setTenantid(loginUser.getTenantid());   //租户id
            //查询货品信息是否存在
            SaLeadsPojo pojo = saLeadsService.getEntityByCustname(saLeadsPojo.getCustname());
            //如果存在不做操作
            if (pojo != null) {
                return R.ok(pojo);
            } else {
                saLeadsPojo = this.saLeadsService.insert(saLeadsPojo, loginUser);
            }
            return R.ok(saLeadsPojo);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
