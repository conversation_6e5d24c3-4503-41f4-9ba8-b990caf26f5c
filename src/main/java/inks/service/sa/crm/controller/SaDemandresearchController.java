package inks.service.sa.crm.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.service.SaBillcodeService;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.crm.domain.pojo.SaDemandresearchPojo;
import inks.service.sa.crm.domain.pojo.SaDemandresearchitemPojo;
import inks.service.sa.crm.domain.pojo.SaDemandresearchitemdetailPojo;
import inks.service.sa.crm.service.SaDemandresearchService;
import inks.service.sa.crm.service.SaDemandresearchitemService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 需求调研(Sa_DemandResearch)表控制层
 *
 * <AUTHOR>
 * @since 2024-10-11 10:15:05
 */
//@RestController
//@RequestMapping("saDemandresearch")
public class SaDemandresearchController {

    private final static Logger logger = LoggerFactory.getLogger(SaDemandresearchController.class);
    @Resource
    private SaDemandresearchService saDemandresearchService;
    @Resource
    private SaDemandresearchitemService saDemandresearchitemService;
    @Resource
    private SaRedisService saRedisService;
    @Resource
    private SaBillcodeService saBillcodeService;

    @ApiOperation(value = " 获取需求调研详细信息", notes = "获取需求调研详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_DemandResearch.List")
    public R<SaDemandresearchPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saDemandresearchService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_DemandResearch.List")
    public R<PageInfo<SaDemandresearchitemdetailPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_DemandResearch.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saDemandresearchService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 获取需求调研详细信息", notes = "获取需求调研详细信息", produces = "application/json")
    @RequestMapping(value = "/getBillEntity", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_DemandResearch.List")
    public R<SaDemandresearchPojo> getBillEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saDemandresearchService.getBillEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getBillList", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_DemandResearch.List")
    public R<PageInfo<SaDemandresearchPojo>> getBillList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_DemandResearch.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saDemandresearchService.getBillList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageTh", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_DemandResearch.List")
    public R<PageInfo<SaDemandresearchPojo>> getPageTh(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_DemandResearch.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saDemandresearchService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增需求调研", notes = "新增需求调研", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_DemandResearch.Add")
    public R<SaDemandresearchPojo> create(@RequestBody String json) {
        try {
            SaDemandresearchPojo saDemandresearchPojo = JSONArray.parseObject(json, SaDemandresearchPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //生成单据编码
            String refNo = saBillcodeService.getSerialNo("DxxMxxB1", loginUser.getTenantid(), "Sa_DemandResearch");
            saDemandresearchPojo.setRefno(refNo);
            saDemandresearchPojo.setCreateby(loginUser.getRealName());   // 创建者
            saDemandresearchPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saDemandresearchPojo.setCreatedate(new Date());   // 创建时间
            saDemandresearchPojo.setLister(loginUser.getRealname());   // 制表
            saDemandresearchPojo.setListerid(loginUser.getUserid());    // 制表id            
            saDemandresearchPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saDemandresearchService.insert(saDemandresearchPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改需求调研", notes = "修改需求调研", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_DemandResearch.Edit")
    public R<SaDemandresearchPojo> update(@RequestBody String json) {
        try {
            SaDemandresearchPojo saDemandresearchPojo = JSONArray.parseObject(json, SaDemandresearchPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saDemandresearchPojo.setAssessor(""); //审核员
            saDemandresearchPojo.setAssessorid(""); //审核员
            saDemandresearchPojo.setAssessdate(new Date()); //审核时间
            saDemandresearchPojo.setLister(loginUser.getRealname());   // 制表
            saDemandresearchPojo.setListerid(loginUser.getUserid());    // 制表id   
            saDemandresearchPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saDemandresearchService.update(saDemandresearchPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除需求调研", notes = "删除需求调研", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_DemandResearch.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saDemandresearchService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /*子表操作 */

    @ApiOperation(value = " 新增需求调研Item", notes = "新增需求调研Item", produces = "application/json")
    @RequestMapping(value = "/createItem", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_DemandResearch.Add")
    public R<SaDemandresearchitemPojo> createItem(@RequestBody String json) {
        try {
            SaDemandresearchitemPojo saDemandresearchitemPojo = JSONArray.parseObject(json, SaDemandresearchitemPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saDemandresearchitemService.insert(saDemandresearchitemPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = " 修改需求调研Item", notes = "修改需求调研Item", produces = "application/json")
    @RequestMapping(value = "/updateItem", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_DemandResearch.Edit")
    public R<SaDemandresearchitemPojo> updateItem(@RequestBody String json) {
        try {
            SaDemandresearchitemPojo saDemandresearchitemPojo = JSONArray.parseObject(json, SaDemandresearchitemPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saDemandresearchitemService.update(saDemandresearchitemPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "删除需求调研Item", notes = "删除需求调研Item", produces = "application/json")
    @RequestMapping(value = "/deleteItem", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_DemandResearch.Delete")
    public R<Integer> deleteItem(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saDemandresearchitemService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "审核需求调研", notes = "审核需求调研", produces = "application/json")
    @RequestMapping(value = "/approval", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_DemandResearch.Approval")
    public R<SaDemandresearchPojo> approval(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            SaDemandresearchPojo saDemandresearchPojo = this.saDemandresearchService.getEntity(key);
            if (saDemandresearchPojo.getAssessor().equals("")) {
                saDemandresearchPojo.setAssessor(loginUser.getRealname()); //审核员
                saDemandresearchPojo.setAssessorid(loginUser.getUserid()); //审核员id
            } else {
                saDemandresearchPojo.setAssessor(""); //审核员
                saDemandresearchPojo.setAssessorid(""); //审核员
            }
            saDemandresearchPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saDemandresearchService.approval(saDemandresearchPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_DemandResearch.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaDemandresearchPojo saDemandresearchPojo = this.saDemandresearchService.getBillEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saDemandresearchPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = saDemandresearchPojo.getItem().size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    SaDemandresearchitemPojo saDemandresearchitemPojo = new SaDemandresearchitemPojo();
                    saDemandresearchPojo.getItem().add(saDemandresearchitemPojo);
                }
            }
        }

        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(saDemandresearchPojo.getItem());
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

