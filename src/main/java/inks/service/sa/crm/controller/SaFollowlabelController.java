package inks.service.sa.crm.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.domain.R;
import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.pojo.SaFollowlabelPojo;
import inks.service.sa.crm.service.SaFollowlabelService;
import inks.sa.common.core.service.SaRedisService;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import net.sf.jasperreports.engine.*;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;
/**
 * 跟进标签(Sa_FollowLabel)表控制层
 *
 * <AUTHOR>
 * @since 2025-04-24 09:39:51
 */
//@RestController
//@RequestMapping("saFollowlabel")
public class SaFollowlabelController {
    @Resource
    private SaFollowlabelService saFollowlabelService;
    
    @Resource
    private SaRedisService saRedisService;
    
    private final static Logger logger = LoggerFactory.getLogger(SaFollowlabelController.class);


    @ApiOperation(value=" 获取跟进标签详细信息", notes="获取跟进标签详细信息", produces="application/json")
    @RequestMapping(value="/getEntity",method= RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_FollowLabel.List")
    public R<SaFollowlabelPojo> getEntity(String key) {
        LoginUser loginUser = saRedisService.getLoginUser();
        try {
            return R.ok(this.saFollowlabelService.getEntity(key));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    
    
    @ApiOperation(value="按条件分页查询", notes="按条件分页查询", produces="application/json")
    @RequestMapping(value="/getPageList",method= RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_FollowLabel.List")
    public R<PageInfo<SaFollowlabelPojo>> getPageList(@RequestBody String json) {
        LoginUser loginUser = saRedisService.getLoginUser();
        try {
            QueryParam queryParam = JSONArray.parseObject(json,QueryParam.class);
            if (queryParam.getOrderBy() ==null || "".equals(queryParam.getOrderBy())) queryParam.setOrderBy("Sa_FollowLabel.CreateDate");
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saFollowlabelService.getPageList(queryParam));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    
    
    @ApiOperation(value=" 新增跟进标签", notes="新增跟进标签", produces="application/json")
    @RequestMapping(value="/create",method= RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_FollowLabel.Add")
    public R<SaFollowlabelPojo> create(@RequestBody String json) {
        LoginUser loginUser = saRedisService.getLoginUser();
        try {
            SaFollowlabelPojo saFollowlabelPojo = JSONArray.parseObject(json,SaFollowlabelPojo.class);
            saFollowlabelPojo.setCreateby(loginUser.getRealName());   // 创建者
            saFollowlabelPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saFollowlabelPojo.setCreatedate(new Date());   // 创建时间
            saFollowlabelPojo.setLister(loginUser.getRealname());   // 制表
            saFollowlabelPojo.setListerid(loginUser.getUserid());    // 制表id
            saFollowlabelPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saFollowlabelService.insert(saFollowlabelPojo));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    
    @ApiOperation(value="修改跟进标签", notes="修改跟进标签", produces="application/json")
    @RequestMapping(value="/update",method= RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_FollowLabel.Edit")
    public R<SaFollowlabelPojo> update(@RequestBody String json) {
        LoginUser loginUser = saRedisService.getLoginUser();
        try {
            SaFollowlabelPojo saFollowlabelPojo = JSONArray.parseObject(json,SaFollowlabelPojo.class);
            saFollowlabelPojo.setLister(loginUser.getRealname());   // 制表
            saFollowlabelPojo.setListerid(loginUser.getUserid());    // 制表id
            saFollowlabelPojo.setModifydate(new Date());   //修改时间
    //            saFollowlabelPojo.setAssessor(""); // 审核员
    //            saFollowlabelPojo.setAssessorid(""); // 审核员id
    //            saFollowlabelPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saFollowlabelService.update(saFollowlabelPojo));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    
    @ApiOperation(value="删除跟进标签", notes="删除跟进标签", produces="application/json")
    @RequestMapping(value="/delete",method= RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_FollowLabel.Delete")
    public R<Integer> delete(String key) {
        LoginUser loginUser = saRedisService.getLoginUser();
        try {
            return R.ok(this.saFollowlabelService.delete(key));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_FollowLabel.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser();
        //获取单据信息
        SaFollowlabelPojo saFollowlabelPojo = this.saFollowlabelService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saFollowlabelPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content ;
        if (reportsPojo != null ) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response= ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
    }

