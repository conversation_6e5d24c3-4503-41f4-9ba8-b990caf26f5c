package inks.service.sa.crm.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.POIUtil;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.annotation.PreAuthorize;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.crm.domain.pojo.SaCustomerPojo;
import inks.service.sa.crm.service.SaCustomerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import static inks.sa.common.core.utils.SqlUtil.filterDeptid;

/**
 * 客户(Sa_Customer)表控制层
 *
 * <AUTHOR>
 * @since 2023-03-06 14:17:20
 */
@RestController
@RequestMapping("S07M03B1")
@Api(tags = "S07M03B1:客户管理")
public class S07M03B1Controller extends SaCustomerController {
    @Resource
    private SaCustomerService saCustomerService;

    @Resource
    private SaRedisService saRedisService;

    @ApiOperation(value = "get新编码", notes = "get新编码 ", produces = "application/json")
    @RequestMapping(value = "/getNextCode", method = RequestMethod.GET)
    public R<String> getNextCode(String type) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
//            // 读取最大数
//            SaCustomerPojo saCustomerPojo = new SaCustomerPojo();
//            if (type.equals("1")) {
//                saCustomerPojo.setGrouptype("客户");
//            }
            //生成最大
            String nextCode = this.saCustomerService.getNextCode();
            return R.ok(nextCode);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "按条件分页查询 own =1 我的，0所有，-1 公海； null时赋值为1 查负责人Principalid=自己", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)

    public R<PageInfo<SaCustomerPojo>> getPageList(@RequestBody String json,
                                                   @RequestParam(required = false, defaultValue = "1") Integer own) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy())) {
                queryParam.setOrderBy("Sa_Customer.CreateDate");
            }
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            Integer isdeptadmin = loginUser.getTenantinfo().getIsdeptadmin();
            Integer isadmin = loginUser.getIsadmin();
            String qpfilter = "";
            // 如果是查公海客户，其他管理员、部门权限全部忽略，只查无负责人的客户返回
            if (own == -1) {
                qpfilter += " and Sa_Customer.Principalid = ''";
            } else if (isdeptadmin == 1) {
                qpfilter = filterDeptid(qpfilter, loginUser, "Sa_Customer.Deptid", "module.customer.deptpartition");
            } else {
                if (own == 1) { // 我的
                    qpfilter += " and Sa_Customer.Principalid = '" + loginUser.getUserid() + "'";
                } else if (own == 0) { // 所有
                    // 无需处理
                } else {
                    throw new BaseBusinessException("own参数错误");
                }
            }
            // isadmin 如果是系统管理员且当own=1，前面逻辑不要，重新赋值：只能看到自己的
            if ((isadmin == 1 || isadmin == 2) && own == 1) {
                qpfilter = " and Sa_Customer.Principalid = '" + loginUser.getUserid() + "'";
            }
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saCustomerService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询 own =1 我的，0查询所有有负责人的线索，-1 公海； null时赋值为1 查负责人Principalid=自己", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageList", method = RequestMethod.POST)

    public R<PageInfo<SaCustomerPojo>> getOnlinePageList(@RequestBody String json,
                                                         @RequestParam(required = false, defaultValue = "1") Integer own) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_Customer.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            Integer isdeptadmin = loginUser.getTenantinfo().getIsdeptadmin();
            Integer isadmin = loginUser.getIsadmin();

            String qpfilter = " and Sa_Customer.EnabledMark = 1";
            // 如果是查公海客户，其他管理员、部门权限全部忽略，只查无负责人的客户返回
            if (own == -1) {
                qpfilter += " and Sa_Customer.Principalid = ''";
            } else if (isdeptadmin == 1) {
                qpfilter = filterDeptid(qpfilter, loginUser, "Sa_Customer.Deptid", "module.customer.deptpartition");
            } else {
                if (own == 1) { // 我的
                    qpfilter += " and Sa_Customer.Principalid = '" + loginUser.getUserid() + "'";
                } else if (own == 0) { // 所有
                    // 无需处理
                } else {
                    throw new BaseBusinessException("own参数错误");
                }
            }
            // isadmin 如果是系统管理员且当own=1，前面逻辑不要，重新赋值：只能看到自己的
            if ((isadmin == 1 || isadmin == 2) && own == 1) {
                qpfilter = " and Sa_Customer.Principalid = '" + loginUser.getUserid() + "'";
            }
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saCustomerService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "按条件分页查询 公海", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getFreePageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Sa_Customer.FreeList")
    public R<PageInfo<SaCustomerPojo>> getFreePageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_Customer.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = " and Sa_Customer.EnabledMark = 1";
            // 如果是查公海客户，其他管理员、部门权限全部忽略，只查无负责人的客户返回
            qpfilter += " and Sa_Customer.Principalid = ''";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saCustomerService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    //================导入导出=======================
    @ApiOperation(value = "导出数据", notes = "导出数据", produces = "application/json")
    @RequestMapping(value = "/exportList", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Mat_Goods.List")
    public void exportList(@RequestBody String json, String groupid, HttpServletRequest request, HttpServletResponse response) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || Objects.equals(queryParam.getOrderBy(), "")) {
                queryParam.setOrderBy("CreateDate");
            }
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Mat_Goods.deletemark=0";
            if (groupid != null) {
                qpfilter += " and Mat_Goods.Groupid='" + groupid + "'";
            }
            // 加入场景   Nanno 20230203
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams("客户", ""),
                    SaCustomerPojo.class, this.saCustomerService.getPageList(queryParam).getList());
            POIUtil.downloadWorkbook(workbook, request, response, "客户");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /*
     * esay poi导入导出 时间2021-12-13 song
     * */
    @ApiOperation(value = "模板导出", notes = "模板导出", produces = "application/json")
    @RequestMapping(value = "/exportModel", method = RequestMethod.GET)
    public void exportModel(HttpServletRequest request, HttpServletResponse response) {
        List<SaCustomerPojo> list = new ArrayList<>();
        //创建表格
        Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams("客户", ""),
                SaCustomerPojo.class, list);
        try {
            //下载模板
            POIUtil.downloadWorkbook(workbook, request, response, "客户模板");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    @ApiOperation(value = "导入数据", notes = "导入数据", produces = "application/json")
    @RequestMapping(value = "/importExecl", method = RequestMethod.POST)
    public R<List<SaCustomerPojo>> importExecl(String groupid, MultipartFile file, HttpServletRequest request, HttpServletResponse response) throws Exception {
        try {
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //去掉标题和表头
            ImportParams importParams = POIUtil.createImportParams(0, 1);
            //将表格数据写入List
            List<SaCustomerPojo> list = POIUtil.importExcel(file.getInputStream(), SaCustomerPojo.class, importParams);
            return R.ok(list);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "识别编码 By Custname", notes = "识别编码", produces = "application/json")
    @RequestMapping(value = "/getEntityBynsp", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Mat_Goods.List")
    public R<SaCustomerPojo> getEntityBynsp(@RequestBody String json) {
        try {
            SaCustomerPojo saCustomerPojo = JSONArray.parseObject(json, SaCustomerPojo.class);
            if (saCustomerPojo.getCustname() == null) {
                saCustomerPojo.setCustname("");
            }
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            SaCustomerPojo leadsPojo = saCustomerService.getEntityByCustname(saCustomerPojo.getCustname());
            return R.ok(leadsPojo);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "导入信息：客户", notes = "", produces = "application/json")
    @RequestMapping(value = "/importEntity", method = RequestMethod.POST)
    public R<SaCustomerPojo> importEntity(@RequestBody String json) {
        try {
            SaCustomerPojo saCustomerPojo = JSONArray.parseObject(json, SaCustomerPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saCustomerPojo.setCreateby(loginUser.getRealname());   //创建者
            saCustomerPojo.setCreatedate(new Date());   //创建时间
            saCustomerPojo.setLister(loginUser.getRealname());   //用户名
            saCustomerPojo.setModifydate(new Date());   //修改时间
            saCustomerPojo.setTenantid(loginUser.getTenantid());   //租户id
            //查询客户信息是否存在
            SaCustomerPojo pojo = saCustomerService.getEntityByCustname(saCustomerPojo.getCustname());
            //如果存在不做操作
            if (pojo != null) {
                return R.ok(pojo);
            } else {
                saCustomerPojo = this.saCustomerService.insert(saCustomerPojo, loginUser);
            }
            return R.ok(saCustomerPojo);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
