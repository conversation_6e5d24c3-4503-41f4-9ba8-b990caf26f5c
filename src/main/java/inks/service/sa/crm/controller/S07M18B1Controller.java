package inks.service.sa.crm.controller;

import com.alibaba.fastjson.JSONArray;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.crm.domain.pojo.SaSmpdeliPojo;
import inks.service.sa.crm.domain.pojo.SaSmpdeliitemPojo;
import inks.service.sa.crm.service.SaSmpdeliService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 样品发货(<PERSON>_<PERSON>mpDeli)表控制层
 *
 * <AUTHOR>
 * @since 2024-09-03 16:20:28
 */
@RestController
@RequestMapping("S07M18B1")
@Api(tags = "S07M18B1:样品发货")
public class S07M18B1Controller extends SaSmpdeliController {

    @Resource
    private SaSmpdeliService saSmpdeliService;
    @Resource
    private SaRedisService saRedisService;

    @ApiOperation(value = "中止批量List<SaSmpdeliitemPojo>", notes = "?type=1中止，0为反作废", produces = "application/json")
    @RequestMapping(value = "/closed", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Bus_Invoice.Edit")
    public R<SaSmpdeliPojo> closed(@RequestBody String json, Integer type) {
        try {
            if (type == null) type = 1;
            List<SaSmpdeliitemPojo> lst = JSONArray.parseArray(json, SaSmpdeliitemPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saSmpdeliService.closed(lst, type, loginUser));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "作废批量List<SaSmpdeliitemPojo>", notes = "?type=1中止，0为反作废", produces = "application/json")
    @RequestMapping(value = "/disannul", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Bus_Invoice.Edit")
    public R<SaSmpdeliPojo> disannul(@RequestBody String json, Integer type) {
        try {
            if (type == null) type = 1;
            List<SaSmpdeliitemPojo> lst = JSONArray.parseArray(json, SaSmpdeliitemPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saSmpdeliService.disannul(lst, type, loginUser));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}
