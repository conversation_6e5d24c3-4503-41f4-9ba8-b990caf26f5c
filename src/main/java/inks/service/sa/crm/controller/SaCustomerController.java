package inks.service.sa.crm.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import inks.common.core.domain.*;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.DateUtils;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.core.utils.inks.PrintUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.crm.domain.pojo.SaCustomerPojo;
import inks.service.sa.crm.domain.pojo.SaLeadsPojo;
import inks.service.sa.crm.service.SaCustomerService;
import inks.service.sa.crm.service.SaLeadsService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static inks.common.core.utils.bean.BeanUtils.attrListToMaps;

/**
 * 客户(Sa_Customer)表控制层
 *
 * <AUTHOR>
 * @since 2023-03-06 14:17:20
 */
//@RestController
//@RequestMapping("saCustomer")
public class SaCustomerController {

    private final static Logger logger = LoggerFactory.getLogger(SaCustomerController.class);
    @Resource
    private SaCustomerService saCustomerService;
    @Resource
    private SaLeadsService saLeadsService;
    @Resource
    private SaRedisService saRedisService;

    @ApiOperation(value = " 获取客户详细信息", notes = "获取客户详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    public R<SaCustomerPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saCustomerService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增客户", notes = "新增客户", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    public R<SaCustomerPojo> create(@RequestBody String json) {
        try {
            SaCustomerPojo saCustomerPojo = JSONArray.parseObject(json, SaCustomerPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //判断线索是否重复转客户
            //反写线索表的Groupid,CustMark
            if (StringUtils.isNotBlank(saCustomerPojo.getLeadsid())) {
                SaLeadsPojo saLeadsPojo = saLeadsService.getEntity(saCustomerPojo.getLeadsid());
                if (saLeadsPojo.getCustmark() == 1) {
                    throw new BaseBusinessException("线索已转客户，不能重复转换！");
                }
            }
            saCustomerPojo.setCreateby(loginUser.getRealName());   // 创建者
            saCustomerPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saCustomerPojo.setCreatedate(new Date());   // 创建时间
            saCustomerPojo.setLister(loginUser.getRealname());   // 制表
            saCustomerPojo.setListerid(loginUser.getUserid());    // 制表id  
            saCustomerPojo.setModifydate(new Date());   //修改时间
            // 加入部门id
            Optional.ofNullable(loginUser.getTenantinfo())
                    .map(TenantInfo::getDeptid)
                    .filter(StringUtils::isNotBlank)
                    .ifPresent(saCustomerPojo::setDeptid);
            return R.ok(this.saCustomerService.insert(saCustomerPojo, loginUser));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改客户", notes = "修改客户", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public R<SaCustomerPojo> update(@RequestBody String json) {
        try {
            SaCustomerPojo saCustomerPojo = JSONArray.parseObject(json, SaCustomerPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saCustomerPojo.setLister(loginUser.getRealname());   // 制表
            saCustomerPojo.setListerid(loginUser.getUserid());    // 制表id  
            saCustomerPojo.setModifydate(new Date());   //修改时间
//            saCustomerPojo.setAssessor(""); // 审核员
//            saCustomerPojo.setAssessorid(""); // 审核员id
//            saCustomerPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saCustomerService.update(saCustomerPojo, loginUser));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除客户", notes = "删除客户", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saCustomerService.delete(key, loginUser));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaCustomerPojo saCustomerPojo = this.saCustomerService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saCustomerPojo);
        // 加入公司信息
        if (loginUser.getTenantinfo() != null) inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }

    @ApiOperation(value = "云打印报表", notes = "打印报表 key=billid,ptid打印模版,sn远程打印SN(可选)", produces = "application/json")
    @RequestMapping(value = "/printWebBill", method = RequestMethod.GET)
    public R<String> printWebBill(String key, String ptid, String sn, Integer cmd, Integer redis) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //=========获取单据表头信息========
            SaCustomerPojo saCustomerPojo = this.saCustomerService.getEntity(key);
            // 获取单据表头.表头转MAP
            Map<String, Object> map = BeanUtils.beanToMap(saCustomerPojo);
            // 获取单据表头.加入公司信息
            if (loginUser.getTenantinfo() != null)
                inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
            //=========获取单据Item信息========
//            List<SaContractitemPojo> lstitem = this.saContractitemService.getList(key);
            // 单据Item. 带属性List转为Map  EricRen 20220427
            List<Map<String, Object>> lst = null;

            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            maprow.put("row", lst);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            mapreport.put("_grparam", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");
            logger.info(ptJson);
            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "客户" + saCustomerPojo.getCustname());    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = inksSnowflake.getSnowflake().nextIdStr();
                saRedisService.setCacheObject("report_data:" + rediskey, ptJson, 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", ptJson);   //  打印数据
            }
            // 云打印模板，加载  兼容 URL模式
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());  // 编辑权限


            // 本地打印
//            if (sn == null || sn.equals("local") || sn.equals("localsec") || sn.equals("localthi")) {
            return R.ok(JSONObject.toJSONString(mapPrint));
//            } else {
//                // 远程SN打印
//                R<String> rPrint = this.utilsFeignService.webPrinting(sn, JSONObject.toJSONString(mapPrint), loginUser.getToken());
//                if (rPrint.getCode() == 200) {
//                    return R.ok();
//                } else {
//                    return R.fail(rPrint.getMsg());
//                }
//            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "云打印SaCustomer明细报表(分页PageList)", notes = "json=分页参数,ptid打印模版,groupid(可选),sn远程打印SN(可选),redis", produces = "application/json")
    @RequestMapping(value = "/printWebPageList", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_Customer.Print")
    public R<String> printWebPageList(@RequestBody(required = false) String json, String ptid, String groupid, String sn, Integer cmd, Integer redis) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();

            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());

            // 报表数据
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_Customer.BillDate");
            String qpfilter = "";
            if (groupid != null) {
                qpfilter += " and Sa_Customer.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            List<SaCustomerPojo> lstitem = this.saCustomerService.getPageList(queryParam).getList();

            //表头转MAP
            Map<String, Object> map = new HashMap<>();
            if (queryParam.getDateRange() != null) {
                map.put("startdate", queryParam.getDateRange().getStartDate());
                map.put("enddate", queryParam.getDateRange().getEndDate());
            }

            // 获取单据表头.加入公司信息
            PrintUtils.addCompanyInfo(map, loginUser);

            // 单据Item. 带属性List转为Map  EricRen 20220427
            List<Map<String, Object>> lst = attrListToMaps(lstitem);

            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            maprow.put("row", lst);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            mapreport.put("_grparam", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");


            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "SaCustomer明细：" + DateUtils.parseDateToStr("yyyy-MM-dd", queryParam.getDateRange().getStartDate()) + "~" + DateUtils.parseDateToStr("yyyy-MM-dd", queryParam.getDateRange().getEndDate()));    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = UUID.randomUUID().toString();
                saRedisService.setCacheObject("report_data:" + rediskey, ptJson, 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", ptJson);   //  打印数据
            }
            // 云打印模板，加载
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());
            // 本地打印
            return R.ok(JSONObject.toJSONString(mapPrint));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}

