package inks.service.sa.crm.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.crm.domain.pojo.SaFollowviewPojo;
import inks.service.sa.crm.service.SaFollowviewService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 跟踪记录(Sa_Followview)表控制层
 *
 * <AUTHOR>
 * @since 2024-07-26 09:50:30
 */
//@RestController
//@RequestMapping("saFollowview")
public class SaFollowviewController {
    private final static Logger logger = LoggerFactory.getLogger(SaFollowviewController.class);
    @Resource
    private SaFollowviewService saFollowviewService;
    @Resource
    private SaRedisService saRedisService;

    @ApiOperation(value = " 获取跟踪记录详细信息", notes = "获取跟踪记录详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_Followview.List")
    public R<SaFollowviewPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saFollowviewService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询 own =1 我的， 默认0所有", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_Followview.List")
    public R<PageInfo<SaFollowviewPojo>> getPageList(@RequestBody String json,
                                                     @RequestParam(defaultValue = "0") Integer own) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_Followview.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            if (own == 1) {
                qpfilter += " and Sa_Followview.Createbyid = '" + loginUser.getUserid() + "'";
            }
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saFollowviewService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增跟踪记录", notes = "新增跟踪记录", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_Followview.Add")
    public R<SaFollowviewPojo> create(@RequestBody String json) {
        try {
            SaFollowviewPojo saFollowviewPojo = JSONArray.parseObject(json, SaFollowviewPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saFollowviewPojo.setCreateby(loginUser.getRealName());   // 创建者
            saFollowviewPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saFollowviewPojo.setCreatedate(new Date());   // 创建时间
            saFollowviewPojo.setLister(loginUser.getRealname());   // 制表
            saFollowviewPojo.setListerid(loginUser.getUserid());    // 制表id
            saFollowviewPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saFollowviewService.insert(saFollowviewPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改跟踪记录", notes = "修改跟踪记录", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_Followview.Edit")
    public R<SaFollowviewPojo> update(@RequestBody String json) {
        try {
            SaFollowviewPojo saFollowviewPojo = JSONArray.parseObject(json, SaFollowviewPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saFollowviewPojo.setLister(loginUser.getRealname());   // 制表
            saFollowviewPojo.setListerid(loginUser.getUserid());    // 制表id
            saFollowviewPojo.setModifydate(new Date());   //修改时间
            //            saFollowviewPojo.setAssessor(""); // 审核员
            //            saFollowviewPojo.setAssessorid(""); // 审核员id
            //            saFollowviewPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saFollowviewService.update(saFollowviewPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除跟踪记录", notes = "删除跟踪记录", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_Followview.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saFollowviewService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_Followview.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaFollowviewPojo saFollowviewPojo = this.saFollowviewService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saFollowviewPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }

    @ApiOperation(value = "云打印报表", notes = "打印报表 key=billid,ptid打印模版,sn远程打印SN(可选)", produces = "application/json")
    @RequestMapping(value = "/printWebBill", method = RequestMethod.GET)
    public R<String> printWebBill(String key, String ptid, String sn, Integer cmd, Integer redis) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //=========获取单据表头信息========
            SaFollowviewPojo saFollowviewPojo = this.saFollowviewService.getEntity(key);
            // 获取单据表头.表头转MAP
            Map<String, Object> map = BeanUtils.beanToMap(saFollowviewPojo);
            // 获取单据表头.加入公司信息
            if (loginUser.getTenantinfo() != null)
                inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
            //=========获取单据Item信息========
//            List<SaContractitemPojo> lstitem = this.saContractitemService.getList(key);
            // 单据Item. 带属性List转为Map  EricRen 20220427
            List<Map<String, Object>> lst = null;

            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            maprow.put("row", lst);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            mapreport.put("_grparam", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");
            logger.info(ptJson);
            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "跟进记录");    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = inksSnowflake.getSnowflake().nextIdStr();
                saRedisService.setCacheObject("report_data:" + rediskey, ptJson, 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", ptJson);   //  打印数据
            }
            // 云打印模板，加载  兼容 URL模式
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());  // 编辑权限


            // 本地打印
//            if (sn == null || sn.equals("local") || sn.equals("localsec") || sn.equals("localthi")) {
            return R.ok(JSONObject.toJSONString(mapPrint));
//            } else {
//                // 远程SN打印
//                R<String> rPrint = this.utilsFeignService.webPrinting(sn, JSONObject.toJSONString(mapPrint), loginUser.getToken());
//                if (rPrint.getCode() == 200) {
//                    return R.ok();
//                } else {
//                    return R.fail(rPrint.getMsg());
//                }
//            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}

