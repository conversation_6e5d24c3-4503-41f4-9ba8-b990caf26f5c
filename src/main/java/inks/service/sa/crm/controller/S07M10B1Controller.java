package inks.service.sa.crm.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.crm.domain.SaFollowviewEntity;
import inks.service.sa.crm.domain.pojo.SaFollowviewPojo;
import inks.service.sa.crm.mapper.SaFollowviewMapper;
import inks.service.sa.crm.service.SaFollowviewService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 跟进记录(Sa_Followview)表控制层
 *
 * <AUTHOR>
 * @since 2024-07-26 09:50:30
 */
@RestController
@RequestMapping("S07M10B1")
@Api(tags = "S07M10B1:跟进记录")
public class S07M10B1Controller extends SaFollowviewController {

    @Resource
    private SaFollowviewMapper saFollowviewMapper;
    @Resource
    private SaFollowviewService saFollowviewService;
    @Resource
    private SaRedisService saRedisService;

    @ApiOperation(value = "根据关联id获取所有的跟进记录 citetype:Leads/Customer/Business/Quotation/Contract isauto传入-1时查询Sa_Followview.IsAuto IN (0, 2)", notes = "", produces = "application/json")
    @RequestMapping(value = "/getListByCiteIdAndIsAuto", method = RequestMethod.GET)
    public R<List<SaFollowviewPojo>> getListByLeadIdAndIsAuto(String citeid, String citetype, Integer isauto) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //isauto传入-1时查询Sa_Followview.IsAuto IN (0, 2)
            return R.ok(this.saFollowviewMapper.getListByCiteIdAndIsAuto(citeid, citetype, isauto));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "无分页查询跟进记录 By任意条件 isauto传入-1时查询Sa_Followview.IsAuto IN (0, 2)", notes = "", produces = "application/json")
    @RequestMapping(value = "/getListByAll", method = RequestMethod.POST)
    public R<List<SaFollowviewPojo>> getListByAll(@RequestBody(required = false) SaFollowviewPojo safollowview) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saFollowviewMapper.getListByAll(safollowview));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "完成跟踪记录：传入id/finishdesc", notes = "", produces = "application/json")
    @PostMapping("/finish")
    public R finish(@RequestBody String json) {
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        try {
            SaFollowviewEntity saFollowview = JSONArray.parseObject(json, SaFollowviewEntity.class);
            saFollowview.setFinisher(loginUser.getRealname());
            saFollowview.setFinishdate(new Date());
            saFollowview.setFinishmark(1);
            return R.ok(saFollowviewMapper.update(saFollowview));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "取消完成跟踪记录：传入id", notes = "", produces = "application/json")
    @GetMapping("/unFinish")
    public R unFinish(String key) {
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(saFollowviewMapper.unFinish(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageList", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_Followview.List")
    public R<PageInfo<SaFollowviewPojo>> getOnlinePageList(@RequestBody String json, @RequestParam(defaultValue = "0") Integer own) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_Followview.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = " and Sa_Followview.FinishMark = 0 ";
            if (own == 1) {
                qpfilter += " and Sa_Followview.Createbyid = '" + loginUser.getUserid() + "'";
            }
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saFollowviewService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "根据 citeid 和 isAuto 按 citetype 分组统计数量",
            notes = "", produces = "application/json")
    @GetMapping("/countByCiteType")
    public R<List<Map<String, Object>>> countByCiteType(
            String citetype,
            Integer isauto) {
        try {
            // 验证登录
            saRedisService.getLoginUser(ServletUtils.getRequest());
            // 调用 Mapper
            List<Map<String, Object>> data = saFollowviewMapper
                    .countByCiteType(citetype, isauto);
            return R.ok(data);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "根据isAuto分组统计数量 0 跟进记录 / 1 系统记录 / 2 跟进计划", notes = "", produces = "application/json")
    @GetMapping("/countByIsAuto")
    public R countByIsAuto(@RequestParam(required = false) Integer own) {
        try {
            // 验证登录
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());

            String qpfilter = "";
            if (Objects.equals(own, 1)) {
                qpfilter = " and CreateByid = '" + loginUser.getUserid() + "'";
            }
            List<Map<String, Object>> result = this.saFollowviewMapper.countByIsAuto(qpfilter);
            return R.ok(result);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


}
