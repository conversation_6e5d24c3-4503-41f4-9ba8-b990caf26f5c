//package inks.service.sa.crm.controller;
//
//import inks.common.core.domain.LoginUser;
//import inks.common.core.domain.R;
//import inks.common.core.utils.ServletUtils;
//import inks.service.sa.crm.service.SaInterviewService;
//import inks.sa.common.core.service.SaRedisService;
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestMethod;
//import org.springframework.web.bind.annotation.RestController;
//
//import javax.annotation.Resource;
//
/// **
// * 跟踪访客记录(Sa_Interview)表控制层
// *
// * <AUTHOR>
// * @since 2023-03-25 14:11:06
// */
//@RestController
//@RequestMapping("S07M11B1")
//@Api(tags = "S07M11B1:访客记录")
//public class S07M11B1Controller extends SaInterviewController {
//    @Resource
//    private SaInterviewService saInterviewService;
//
//    @Resource
//    private SaRedisService saRedisService;
//
//
//    @ApiOperation(value = " 根据客户id获取所有的跟进客户记录", notes = "根据线索id获取所有的跟进记录", produces = "application/json")
//    @RequestMapping(value = "/getListByGroupIdAndIsAuto", method = RequestMethod.GET)
//    public R getListByGroupIdAndIsAuto(String groupid, Integer isauto) {
//        try {
//            // 获得用户数据
//            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
//            return R.ok(this.saInterviewService.getListByGroupIdAndIsAuto(groupid, isauto));
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }
//}
