package inks.service.sa.crm.controller;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.DateRange;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.DateUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.crm.mapper.S07MBIR1Mapper;
import inks.service.sa.crm.service.S07MBIR1Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;

/**
 * 
 *
 * <AUTHOR> @date 2022-08-16
 */
@RestController
@RequestMapping("S07MBIR1")
@Api(tags = "S07MBIR1:大屏")
public class S07MBIR1Controller {
    @Resource
    private S07MBIR1Mapper s07MBIR1Mapper;

    /**
     * 
     */
    @Resource
    private S07MBIR1Service s07MBIR1Service;
    @Resource
    private SaRedisService saRedisService;
//1.查询本月新增客户数S07M03B1，新增商机数S07M05B1，新增线索数S07M02B1，新增根据记录S07M10B1，新增合同数S07M06B1。
//
//2.查询本月每天新增客户和线索。
//
//3.统计所有客户类型汇总，根据custtype字段汇总。
//  数据格式：[{name:'木业',value:100},{name:'轴承',value:4}]
//
//4.统计所有客户等级汇总，根据custclass字段汇总，数据格式同上

    @ApiOperation(value = "查询本月新增：客户数S07M03B1，新增商机数S07M05B1，新增线索数S07M02B1，新增根据记录S07M10B1，新增合同数S07M06B1", notes = "", produces = "application/json")
    @RequestMapping(value = "/getNewCountThisMonth", method = RequestMethod.POST)
    public R<Map<String, Object>> getNewCountThisMonth(@RequestBody(required = false) String json, @RequestParam(required = false) Integer own) {
        try {
            LoginUser loginUser = saRedisService.getLoginUser();
            if (json == null) json = "{}";
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getDateRange() == null) {
                Date now = new Date();
                // 截断日期到本月的第一天，即获取本月1号0点的时间
                Date firstDayOfMonth = DateUtils.truncate(now, java.util.Calendar.MONTH);
                queryParam.setDateRange(new DateRange(null, firstDayOfMonth, now));
            }
            String qpfilter = "";
            if (Objects.equals(own, 1)) {
                qpfilter += " and CreateByid = '" + loginUser.getUserid() + "'";
            }
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.s07MBIR1Service.getNewCountThisMonth(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "查询所有累计：客户数S07M03B1，商机数S07M05B1，线索数S07M02B1，根据记录S07M10B1，合同数S07M06B1", notes = "", produces = "application/json")
    @RequestMapping(value = "/getAllCount", method = RequestMethod.POST)
    public R<Map<String, Object>> getAllCount(@RequestBody(required = false) String json, @RequestParam(required = false) Integer own) {
        try {
            LoginUser loginUser = saRedisService.getLoginUser();
            if (json == null) json = "{}";
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getDateRange() == null) {
                Date now = new Date();
                // 2022开始 相当于查所有累计
                Date start = DateUtil.parseDate("2022-01-01");
                queryParam.setDateRange(new DateRange(null, start, now));
            }
            String qpfilter = "";
            if (Objects.equals(own, 1)) {
                qpfilter += " and CreateByid = '" + loginUser.getUserid() + "'";
            }
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.s07MBIR1Service.getNewCountThisMonth(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "查询本月新增客户数S07M03B1，新增商机数S07M05B1，新增线索数S07M02B1，新增根据记录S07M10B1，新增合同数S07M06B1", notes = "", produces = "application/json")
    @RequestMapping(value = "/getNewCountAndAmtThisMonth", method = RequestMethod.POST)
    public R<Map<String, Object>> getNewCountAndAmtThisMonth(@RequestBody(required = false) String json, @RequestParam(required = false) Integer own) {
        try {
            LoginUser loginUser = saRedisService.getLoginUser();
            if (json == null) json = "{}";
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getDateRange() == null) {
                Date now = new Date();
                // 截断日期到本月的第一天，即获取本月1号0点的时间
                Date firstDayOfMonth = DateUtils.truncate(now, java.util.Calendar.MONTH);
                queryParam.setDateRange(new DateRange(null, firstDayOfMonth, now));
            }
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.s07MBIR1Service.getNewCountAndAmtThisMonth(queryParam, loginUser.getUserid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "公海线索数量", notes = "", produces = "application/json")
    @RequestMapping(value = "/countPublicLeads", method = RequestMethod.GET)
    public R<Integer> countPublicLeads() {
        try {
            LoginUser loginUser = saRedisService.getLoginUser();
            return R.ok(this.s07MBIR1Mapper.countPublicLeads());
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }



    @ApiOperation(value = "查询本月[每天]新增客户数S07M03B1，新增商机数S07M05B1，新增线索数S07M02B1，新增根据记录S07M10B1，新增合同数S07M06B1", notes = "", produces = "application/json")
    @RequestMapping(value = "/getNewCountEveryDay", method = RequestMethod.POST)
    public R<List<Map<String, Object>>> getNewCountEveryDay(@RequestBody(required = false) String json, @RequestParam(required = false) Integer trend,
                                                            @RequestParam(required = false) Integer own) {
        try {
            LoginUser loginUser = saRedisService.getLoginUser();
            if (json == null) json = "{}";
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getDateRange() == null) {
                Date now = new Date();
                // 截断日期到本月的第一天，即获取本月1号0点的时间
                Date firstDayOfMonth = DateUtils.truncate(now, java.util.Calendar.MONTH);
                queryParam.setDateRange(new DateRange(null, firstDayOfMonth, now));
            }
            String qpfilter = "";
            if (Objects.equals(own, 1)) {
                qpfilter += " and CreateByid = '" + loginUser.getUserid() + "'";
            }
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.s07MBIR1Service.getNewCountEveryDay(queryParam, trend));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "统计所有客户类型汇总，根据custtype字段汇总。", notes = "", produces = "application/json")
    @RequestMapping(value = "/getCustomerType", method = RequestMethod.GET)
    public R<List<Map<String, Object>>> getCustomerType(@RequestParam(required = false) Integer own) {
        try {
            LoginUser loginUser = saRedisService.getLoginUser();
            String qpfilter = "";
            if (Objects.equals(own, 1)) {
                qpfilter += " and CreateByid = '" + loginUser.getUserid() + "'";
            }
            return R.ok(this.s07MBIR1Service.getCustomerType(qpfilter));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "统计所有客户等级汇总，根据CustClass字段分组", notes = "", produces = "application/json")
    @RequestMapping(value = "/getCustomerClass", method = RequestMethod.GET)
    public R<List<Map<String, Object>>> getCustomerClass() {
        try {
            LoginUser loginUser = saRedisService.getLoginUser();
            return R.ok(this.s07MBIR1Service.getCustomerClass());
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    //1、第一列为状态;状态，就前端根据你返回的数据是否有回款id、发票id、报价id以及合同id判断
    //2、商机的内容:商机refno，客户编码，客户名称，商机时间，负责人;
    //3、相关信息:回访计划时间，样品需求单号，报价单号(最新)，报价金额(sum)，合同单号(最新)，合同金额(sum)，开票金额(sum)，开票结余(计算)，回款计划(sum)，回款金额(sum)，回款结余(计算)
    @ApiOperation(value = "商机执行表", notes = "", produces = "application/json")
    @RequestMapping(value = "/getBusinessInfo", method = RequestMethod.POST)
    public R<PageInfo<Map<String, Object>>> getBusinessInfo(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json,QueryParam.class);
            if (queryParam.getOrderBy() ==null || "".equals(queryParam.getOrderBy())) queryParam.setOrderBy("Sa_Business.CreateDate");
            LoginUser loginUser = saRedisService.getLoginUser();
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.s07MBIR1Service.getBusinessInfo(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "客户执行表", notes = "获取累计合同数、累计合同额、累计回款额、已开票额", produces = "application/json")
    @RequestMapping(value = "/getCustomerInfo", method = RequestMethod.GET)
    public R<Map<String, Object>> getContractStatistics(String groupid) {
        try {
            LoginUser loginUser = saRedisService.getLoginUser();
            return R.ok(this.s07MBIR1Service.getContractStatistics(groupid));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
