package inks.service.sa.crm.controller;

import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 发出商品(SaDeliery)表控制层
 *
 * <AUTHOR>
 * @since 2024-04-05 16:51:44
 */
@RestController
@RequestMapping("S07M16B1")
@Api(tags = "S07M16B1:送货单")//仅用于打印 货品信息都不用，只要送货单，明细中我们用itemName和itemSpec来打印。
public class S07M16B1Controller extends SaDelieryController {


}
