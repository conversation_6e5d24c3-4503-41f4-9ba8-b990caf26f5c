package inks.service.sa.crm.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.crm.domain.pojo.SaAttributePojo;
import inks.service.sa.crm.service.SaAttributeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * SPU属性表(Sa_Attribute)表控制层
 *
 * <AUTHOR>
 * @since 2024-10-25 17:09:04
 */
@RestController
@RequestMapping("S07M01S3")
@Api(tags = "S07M01S3:SPU属性表")
public class S07M01S3Controller extends SaAttributeController {

    @Resource
    private SaAttributeService saAttributeService;


    @Resource
    private SaRedisService saRedisService;


    /**
     * 分页查询
     *
     * @return 查询结果
     */
    @ApiOperation(value = "获得显示的属性表", notes = "获得显示的属性表", produces = "application/json")
    @RequestMapping(value = "/getListByShow", method = RequestMethod.GET)
    public R<List<SaAttributePojo>> getListByShow() {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saAttributeService.getListByShow(loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}

