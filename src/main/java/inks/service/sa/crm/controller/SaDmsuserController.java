package inks.service.sa.crm.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.AESUtil;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.crm.domain.pojo.SaDmsuserPojo;
import inks.service.sa.crm.service.SaDmsuserService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * DMS用户(Sa_DmsUser)表控制层
 *
 * <AUTHOR>
 * @since 2023-12-08 16:42:42
 */
//@RestController
//@RequestMapping("saDmsuser")
public class SaDmsuserController {

    private final static Logger logger = LoggerFactory.getLogger(SaDmsuserController.class);
    @Resource
    private SaDmsuserService saDmsuserService;
    @Resource
    private SaRedisService saRedisService;

    @ApiOperation(value = " 获取DMS用户详细信息", notes = "获取DMS用户详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_DmsUser.List")
    public R<SaDmsuserPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saDmsuserService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_DmsUser.List")
    public R<PageInfo<SaDmsuserPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_DmsUser.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saDmsuserService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增DMS用户", notes = "新增DMS用户", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_DmsUser.Add")
    public R<SaDmsuserPojo> create(@RequestBody String json) {
        try {
            SaDmsuserPojo saDmsuserPojo = JSONArray.parseObject(json, SaDmsuserPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saDmsuserPojo.setCreateby(loginUser.getRealName());   // 创建者
            saDmsuserPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saDmsuserPojo.setCreatedate(new Date());   // 创建时间
            saDmsuserPojo.setLister(loginUser.getRealname());   // 制表
            saDmsuserPojo.setListerid(loginUser.getUserid());    // 制表id  
            saDmsuserPojo.setModifydate(new Date());   //修改时间

            //加密密码
            saDmsuserPojo.setUserpassword(AESUtil.Encrypt(saDmsuserPojo.getUserpassword()));
            return R.ok(this.saDmsuserService.insert(saDmsuserPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改DMS用户", notes = "修改DMS用户", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_DmsUser.Edit")
    public R<SaDmsuserPojo> update(@RequestBody String json) {
        try {
            SaDmsuserPojo saDmsuserPojo = JSONArray.parseObject(json, SaDmsuserPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saDmsuserPojo.setLister(loginUser.getRealname());   // 制表
            saDmsuserPojo.setListerid(loginUser.getUserid());    // 制表id  
            saDmsuserPojo.setModifydate(new Date());   //修改时间
            //加密密码
            saDmsuserPojo.setUserpassword(AESUtil.Encrypt(saDmsuserPojo.getUserpassword()));
            return R.ok(this.saDmsuserService.update(saDmsuserPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除DMS用户", notes = "删除DMS用户", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_DmsUser.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saDmsuserService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_DmsUser.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaDmsuserPojo saDmsuserPojo = this.saDmsuserService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saDmsuserPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

