package inks.service.sa.crm.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.*;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.DateUtils;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.core.utils.inks.PrintUtils;

import inks.sa.common.core.service.SaBillcodeService;
import inks.sa.common.core.service.SaRedisService;
import inks.sa.common.core.utils.PrintColor;
import inks.service.sa.crm.domain.pojo.SaSmpplanPojo;
import inks.service.sa.crm.domain.pojo.SaSmpplanitemPojo;
import inks.service.sa.crm.domain.pojo.SaSmpplanitemdetailPojo;
import inks.service.sa.crm.service.SaSmpplanService;
import inks.service.sa.crm.service.SaSmpplanitemService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static inks.common.core.utils.bean.BeanUtils.attrListToMaps;
import static inks.sa.common.core.utils.SqlUtil.filterDeptid;

/**
 * 样品需求(Sa_SmpPlan)表控制层
 *
 * <AUTHOR>
 * @since 2024-09-03 15:17:53
 */
//@RestController
//@RequestMapping("saSmpplan")
public class SaSmpplanController {

    private final static Logger logger = LoggerFactory.getLogger(SaSmpplanController.class);
    @Resource
    private SaSmpplanService saSmpplanService;
    @Resource
    private SaSmpplanitemService saSmpplanitemService;
    @Resource
    private SaRedisService saRedisService;
    @Resource
    private SaBillcodeService saBillcodeService;

    @ApiOperation(value = " 获取样品需求详细信息", notes = "获取样品需求详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_SmpPlan.List")
    public R<SaSmpplanPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saSmpplanService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_SmpPlan.List")
    
    public R<PageInfo<SaSmpplanitemdetailPojo>> getPageList(@RequestBody String json,
                                                            @RequestParam(required = false, defaultValue = "1") Integer own) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_SmpPlan.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            Integer isdeptadmin = loginUser.getTenantinfo().getIsdeptadmin();
            String qpfilter = "";

            if (isdeptadmin == 1) {
                qpfilter = filterDeptid(qpfilter, loginUser, "Sa_SmpPlan.Deptid", "module.customer.deptpartition");// 部门权限过滤
            } else {
                switch (own) {
                    case 1: // 我的
                        qpfilter += " and Sa_SmpPlan.Operatorid = '" + loginUser.getUserid() + "'";
                        break;
                    case 0: // 所有
                        break;
                    default:
                        throw new BaseBusinessException("own参数错误");
                }
            }
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saSmpplanService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 获取样品需求详细信息", notes = "获取样品需求详细信息", produces = "application/json")
    @RequestMapping(value = "/getBillEntity", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_SmpPlan.List")
    public R<SaSmpplanPojo> getBillEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saSmpplanService.getBillEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getBillList", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_SmpPlan.List")
    public R<PageInfo<SaSmpplanPojo>> getBillList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_SmpPlan.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saSmpplanService.getBillList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageTh", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_SmpPlan.List")
    
    public R<PageInfo<SaSmpplanPojo>> getPageTh(@RequestBody String json,
                                                @RequestParam(required = false, defaultValue = "1") Integer own) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_SmpPlan.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            Integer isdeptadmin = loginUser.getTenantinfo().getIsdeptadmin();
            String qpfilter = "";
            if (isdeptadmin == 1) {
                qpfilter = filterDeptid(qpfilter, loginUser, "Sa_SmpPlan.Deptid", "module.customer.deptpartition");// 部门权限过滤
            } else {
                switch (own) {
                    case 1: // 我的
                        qpfilter += " and Sa_SmpPlan.Operatorid = '" + loginUser.getUserid() + "'";
                        break;
                    case 0: // 所有
                        break;
                    default:
                        throw new BaseBusinessException("own参数错误");
                }
            }
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saSmpplanService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增样品需求", notes = "新增样品需求", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_SmpPlan.Add")
    public R<SaSmpplanPojo> create(@RequestBody String json) {
        try {
            SaSmpplanPojo saSmpplanPojo = JSONArray.parseObject(json, SaSmpplanPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //生成单据编码
            String refNo = saBillcodeService.getSerialNo("DxxMxxB1", loginUser.getTenantid(), "Sa_SmpPlan");
            saSmpplanPojo.setRefno(refNo);
            saSmpplanPojo.setCreateby(loginUser.getRealName());   // 创建者
            saSmpplanPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saSmpplanPojo.setCreatedate(new Date());   // 创建时间
            saSmpplanPojo.setLister(loginUser.getRealname());   // 制表
            saSmpplanPojo.setListerid(loginUser.getUserid());    // 制表id            
            saSmpplanPojo.setModifydate(new Date());   //修改时间
            // 加入部门id
            Optional.ofNullable(loginUser.getTenantinfo())
                    .map(TenantInfo::getDeptid)
                    .filter(StringUtils::isNotBlank)
                    .ifPresent(saSmpplanPojo::setDeptid);
            return R.ok(this.saSmpplanService.insert(saSmpplanPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改样品需求", notes = "修改样品需求", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_SmpPlan.Edit")
    public R<SaSmpplanPojo> update(@RequestBody String json) {
        try {
            SaSmpplanPojo saSmpplanPojo = JSONArray.parseObject(json, SaSmpplanPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saSmpplanPojo.setLister(loginUser.getRealname());   // 制表
            saSmpplanPojo.setListerid(loginUser.getUserid());    // 制表id   
            saSmpplanPojo.setModifydate(new Date());   //修改时间
            saSmpplanPojo.setAssessor(""); //审核员
            saSmpplanPojo.setAssessorid(""); //审核员
            saSmpplanPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saSmpplanService.update(saSmpplanPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除样品需求", notes = "删除样品需求", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_SmpPlan.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saSmpplanService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /*子表操作 */

    @ApiOperation(value = " 新增样品需求Item", notes = "新增样品需求Item", produces = "application/json")
    @RequestMapping(value = "/createItem", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_SmpPlan.Add")
    public R<SaSmpplanitemPojo> createItem(@RequestBody String json) {
        try {
            SaSmpplanitemPojo saSmpplanitemPojo = JSONArray.parseObject(json, SaSmpplanitemPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saSmpplanitemService.insert(saSmpplanitemPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = " 修改样品需求Item", notes = "修改样品需求Item", produces = "application/json")
    @RequestMapping(value = "/updateItem", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_SmpPlan.Edit")
    public R<SaSmpplanitemPojo> updateItem(@RequestBody String json) {
        try {
            SaSmpplanitemPojo saSmpplanitemPojo = JSONArray.parseObject(json, SaSmpplanitemPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saSmpplanitemService.update(saSmpplanitemPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "删除样品需求Item", notes = "删除样品需求Item", produces = "application/json")
    @RequestMapping(value = "/deleteItem", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_SmpPlan.Delete")
    public R<Integer> deleteItem(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saSmpplanitemService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "审核样品需求", notes = "审核样品需求", produces = "application/json")
    @RequestMapping(value = "/approval", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_SmpPlan.Approval")
    public R<SaSmpplanPojo> approval(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            SaSmpplanPojo saSmpplanPojo = this.saSmpplanService.getEntity(key);
            if (saSmpplanPojo.getAssessor().equals("")) {
                saSmpplanPojo.setAssessor(loginUser.getRealname()); //审核员
                saSmpplanPojo.setAssessorid(loginUser.getUserid()); //审核员id
            } else {
                saSmpplanPojo.setAssessor(""); //审核员
                saSmpplanPojo.setAssessorid(""); //审核员
            }
            saSmpplanPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saSmpplanService.approval(saSmpplanPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_SmpPlan.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaSmpplanPojo saSmpplanPojo = this.saSmpplanService.getBillEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saSmpplanPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = saSmpplanPojo.getItem().size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    SaSmpplanitemPojo saSmpplanitemPojo = new SaSmpplanitemPojo();
                    saSmpplanPojo.getItem().add(saSmpplanitemPojo);
                }
            }
        }

        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(saSmpplanPojo.getItem());
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }

    @ApiOperation(value = "云打印报表", notes = "打印报表 key=billid,ptid打印模版,sn远程打印SN(可选)", produces = "application/json")
    @RequestMapping(value = "/printWebBill", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_SmpPlan.Print")
    public R<String> printWebBill(String key, String ptid, String sn, Integer cmd, Integer redis) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //=========获取单据表头信息========
            SaSmpplanPojo saSmpplanPojo = this.saSmpplanService.getEntity(key);
            // 检查是否审核后方可打印单据,改为 feign Eric 20230203


            // 获取单据表头.表头转MAP
            Map<String, Object> map = BeanUtils.beanToMap(saSmpplanPojo);
            // 获取单据表头.加入公司信息
            PrintUtils.addCompanyInfo(map, loginUser);
            //=========获取单据Item信息========
            List<SaSmpplanitemPojo> lstitem = this.saSmpplanitemService.getList(key);
            // 单据Item. 带属性List转为Map  EricRen 20220427
            List<Map<String, Object>> lst = BeanUtils.attrcostListToMaps(lstitem);

            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            maprow.put("row", lst);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            mapreport.put("_grparam", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");
            logger.info(ptJson);
            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();

            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "SaSmpplan" + saSmpplanPojo.getRefno());    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = inksSnowflake.getSnowflake().nextIdStr();
                saRedisService.setCacheObject("report_data:" + rediskey, ptJson, 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", ptJson);   //  打印数据
            }
            // 云打印模板，加载  兼容 URL模式
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());  // 编辑权限

            // 刷入打印Num++
            //SaSmpplanPojo billPrintPojo = new SaSmpplanPojo();
            //billPrintPojo.setId(saSmpplanPojo.getId());
            //billPrintPojo.setPrintcount(saSmpplanPojo.getPrintcount() + 1);
            //billPrintPojo.setTenantid(saSmpplanPojo.getTenantid());
            //this.saSmpplanService.updatePrintcount(billPrintPojo);

            // 本地打印
            return R.ok(JSONObject.toJSONString(mapPrint));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "批量云打印报表(List<SaSmpplanPojo>不带item)", notes = "打印报表 key=billid,ptid打印模版,sn远程打印SN(可选),groupid不为null则只获取第一条客户信息", produces = "application/json")
    @RequestMapping(value = "/printWebPageTh", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_SmpPlan.Print")
    public R<String> printWebPageTh(@RequestBody String json, String ptid, String groupid, String sn, Integer cmd, Integer redis) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();

            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());

            // 报表数据
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_SmpPlan.BillDate");
            String qpfilter = "";
            if (groupid != null) {
                qpfilter += " and Sa_SmpPlan.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            List<SaSmpplanPojo> lstTh = this.saSmpplanService.getPageTh(queryParam).getList();
            // 获取单据表头.表头转MAP
            Map<String, Object> map = new HashMap<>();
            //if (groupid != null && lstTh.size() > 0) {
            //    map.put("groupname", lstTh.get(0).getGroupname());
            //    map.put("abbreviate", lstTh.get(0).getAbbreviate());
            //    map.put("groupuid", lstTh.get(0).getGroupuid());
            //} else {
            //    map.put("groupname", "");
            //    map.put("abbreviate", "");
            //    map.put("groupuid", "");
            //}
            // 获取单据表头.加入公司信息
            PrintUtils.addCompanyInfo(map, loginUser);
            // 单据Item. 带属性List转为Map  EricRen 20220427
            List<Map<String, Object>> lst = attrListToMaps(lstTh);

            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            maprow.put("row", lst);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            mapreport.put("_grparam", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");
            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "SaSmpplan批量打印");    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = UUID.randomUUID().toString();
                saRedisService.setCacheObject("report_data:" + rediskey, ptJson, 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", ptJson);   //  打印数据
            }
            // 云打印模板，加载
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());
            // 本地打印
            return R.ok(JSONObject.toJSONString(mapPrint));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "云打印SaSmpplan明细报表(分页PageList)", notes = "json=分页参数,ptid打印模版,groupid(可选),sn远程打印SN(可选),redis", produces = "application/json")
    @RequestMapping(value = "/printWebPageList", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_SmpPlan.Print")
    public R<String> printWebPageList(@RequestBody(required = false) String json, String ptid, String groupid, String sn, Integer cmd, Integer redis) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();

            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());

            // 报表数据
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_SmpPlan.BillDate");
            String qpfilter = "";
            if (groupid != null) {
                qpfilter += " and Sa_SmpPlan.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            List<SaSmpplanitemdetailPojo> lstitem = this.saSmpplanService.getPageList(queryParam).getList();

            //表头转MAP
            Map<String, Object> map = new HashMap<>();
            if (queryParam.getDateRange() != null) {
                map.put("startdate", queryParam.getDateRange().getStartDate());
                map.put("enddate", queryParam.getDateRange().getEndDate());
            }
            if (groupid != null && lstitem.size() > 0) {
                map.put("groupname", lstitem.get(0).getGroupname());
                map.put("abbreviate", lstitem.get(0).getAbbreviate());
                map.put("groupuid", lstitem.get(0).getGroupuid());
            } else {
                map.put("groupname", "");
                map.put("abbreviate", "");
                map.put("groupuid", "");
            }
            // 获取单据表头.加入公司信息
            PrintUtils.addCompanyInfo(map, loginUser);

            // 单据Item. 带属性List转为Map  EricRen 20220427
            List<Map<String, Object>> lst = attrListToMaps(lstitem);

            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            maprow.put("row", lst);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            mapreport.put("_grparam", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");


            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "SaSmpplan明细：" + DateUtils.parseDateToStr("yyyy-MM-dd", queryParam.getDateRange().getStartDate()) + "~" + DateUtils.parseDateToStr("yyyy-MM-dd", queryParam.getDateRange().getEndDate()));    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = UUID.randomUUID().toString();
                saRedisService.setCacheObject("report_data:" + rediskey, ptJson, 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", ptJson);   //  打印数据
            }
            // 云打印模板，加载
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());
            // 本地打印
            return R.ok(JSONObject.toJSONString(mapPrint));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "一式两份打印", notes = "打印报表 key=billid,ptid打印模版,sn远程打印SN(可选)", produces = "application/json")
    @RequestMapping(value = "/printWebBillMulti", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_SmpPlan.Print")
    public R<String> printWebBillMulti(String key, String ptid, String sn, Integer cmd) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //=========获取单据表头信息========
            SaSmpplanPojo saSmpplanPojo = this.saSmpplanService.getEntity(key);


            // 获取单据表头.表头转MAP
            Map<String, Object> map = BeanUtils.beanToMap(saSmpplanPojo);
            // 获取单据表头.加入公司信息
            PrintUtils.addCompanyInfo(map, loginUser);
            //=========获取单据Item信息========
            QueryParam queryParam = new QueryParam();
            queryParam.setTenantid(loginUser.getTenantid());
            queryParam.setFilterstr(" and Sa_SmpPlanItem.Pid='" + key + "'");
            queryParam.setOrderBy("Sa_SmpPlanItem.RowNum");
            queryParam.setPageNum(1);
            queryParam.setPageSize(1000);
            List<SaSmpplanitemdetailPojo> lstitem = this.saSmpplanService.getPageList(queryParam).getList();
            List<SaSmpplanitemdetailPojo> lstCopy = new ArrayList<>();
            for (SaSmpplanitemdetailPojo map2 : lstitem) {
                SaSmpplanitemdetailPojo newPojo = new SaSmpplanitemdetailPojo();
                BeanUtils.copyProperties(map2, newPojo);
                lstCopy.add(newPojo);
            }
            // 单据Item. 带属性List转为Map  EricRen 20220427
            List<Map<String, Object>> lst = attrListToMaps(lstitem);
            List<Map<String, Object>> lstCopyMap = attrListToMaps(lstCopy);
            // 给第一个 lst 中的每个 map 添加字段 ToWho=1，PageNo=1
            for (Map<String, Object> m : lst) {
                m.put("ToWho", 1);
                m.put("PageNo", 1);
            }

            // 给第二个 lstCopy 中的每个 map 添加字段 ToWho=2，PageNo=1
            for (Map<String, Object> m : lstCopyMap) {
                m.put("ToWho", 2);
                m.put("PageNo", 1);
            }
            // 合并两个 list
            lst.addAll(lstCopyMap);

            PrintColor.red("lst.size() = " + lst.size());
            PrintColor.red("lst: " + lst);


            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            maprow.put("row", lst);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            mapreport.put("_grparam", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");
            logger.info(ptJson);
            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "采购合同" + saSmpplanPojo.getRefno());
            mapPrint.put("data", ptJson);
            // 云打印模板，加载
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());
            // 本地打印
            return R.ok(JSONObject.toJSONString(mapPrint));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}

