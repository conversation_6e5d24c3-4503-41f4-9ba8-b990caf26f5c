package inks.service.sa.crm.controller;

import com.alibaba.fastjson.JSONArray;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.crm.domain.pojo.SaPersonPojo;
import inks.service.sa.crm.domain.pojo.SaPersongroupPojo;
import inks.service.sa.crm.domain.pojo.SaPersonleadsPojo;
import inks.service.sa.crm.service.SaPersonService;
import inks.service.sa.crm.service.SaPersongroupService;
import inks.service.sa.crm.service.SaPersonleadsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 联系人(Sa_Person)表控制层
 *
 * <AUTHOR>
 * @since 2023-03-06 12:57:00
 */
@RestController
@RequestMapping("S07M04B1")
@Api(tags = "S07M04B1:联系人")
public class S07M04B1Controller extends SaPersonController {
    @Resource
    private SaPersonService saPersonService;
    @Resource
    private SaRedisService saRedisService;
    @Resource
    private SaPersongroupService saPersongroupService;
    @Resource
    private SaPersonleadsService saPersonleadsService;

    /**
     * @return R<SaPersonPojo>
     * @Description 新增联系人-线索关联
     * <AUTHOR>
     * @param[1] json 联系人信息
     * @param[2] id 线索id
     * @time 2023/4/13 13:31
     */
    @ApiOperation(value = "新增联系人-线索关联", notes = "新增联系人-线索关联", produces = "application/json")
    @RequestMapping(value = "/createByLeads", method = RequestMethod.POST)
    public R<SaPersonPojo> createByLeads(@RequestBody String json, String id) {
        try {
            SaPersonPojo saPersonPojo = JSONArray.parseObject(json, SaPersonPojo.class);
            // 新建联系人
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saPersonPojo.setCreateby(loginUser.getRealName());   // 创建者
            saPersonPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saPersonPojo.setCreatedate(new Date());   // 创建时间
            saPersonPojo.setLister(loginUser.getRealname());   // 制表
            saPersonPojo.setListerid(loginUser.getUserid());    // 制表id
            saPersonPojo.setModifydate(new Date());   //修改时间
            SaPersonPojo insertPerson = this.saPersonService.insert(saPersonPojo);
            // 新增联系人-线索关联
            SaPersonleadsPojo saPersonleadsPojo = new SaPersonleadsPojo();
            saPersonleadsPojo.setPersonid(insertPerson.getId());
            saPersonleadsPojo.setLeadsid(id);
            saPersonleadsPojo.setCreateby(loginUser.getRealName());   // 创建者
            saPersonleadsPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saPersonleadsPojo.setCreatedate(new Date());   // 创建时间
            saPersonleadsPojo.setLister(loginUser.getRealname());   // 制表
            saPersonleadsPojo.setListerid(loginUser.getUserid());    // 制表id
            saPersonleadsPojo.setModifydate(new Date());   //修改时间
            this.saPersonleadsService.insert(saPersonleadsPojo);
            return R.ok(insertPerson);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * @return R<SaPersonPojo>
     * @Description 新增联系人-客户关联
     * <AUTHOR>
     * @param[1] json 联系人信息
     * @param[2] id 客户id
     * @time 2023/4/13 13:31
     */
    @ApiOperation(value = "新增联系人-客户关联", notes = "新增联系人-线索关联", produces = "application/json")
    @RequestMapping(value = "/createByGroup", method = RequestMethod.POST)
    public R<SaPersonPojo> createByGroup(@RequestBody String json, String id) {
        try {
            SaPersonPojo saPersonPojo = JSONArray.parseObject(json, SaPersonPojo.class);
            // 新建联系人
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saPersonPojo.setCreateby(loginUser.getRealName());   // 创建者
            saPersonPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saPersonPojo.setCreatedate(new Date());   // 创建时间
            saPersonPojo.setLister(loginUser.getRealname());   // 制表
            saPersonPojo.setListerid(loginUser.getUserid());    // 制表id
            saPersonPojo.setModifydate(new Date());   //修改时间
            SaPersonPojo insertPerson = this.saPersonService.insert(saPersonPojo);
            // 新增联系人-客户关联
            SaPersongroupPojo saPersongroupPojo = new SaPersongroupPojo();
            saPersongroupPojo.setPersonid(insertPerson.getId());
            saPersongroupPojo.setGroupid(id);
            saPersongroupPojo.setCreateby(loginUser.getRealName());   // 创建者
            saPersongroupPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saPersongroupPojo.setCreatedate(new Date());   // 创建时间
            saPersongroupPojo.setLister(loginUser.getRealname());   // 制表
            saPersongroupPojo.setListerid(loginUser.getUserid());    // 制表id
            saPersongroupPojo.setModifydate(new Date());   //修改时间
            this.saPersongroupService.insert(saPersongroupPojo);
            return R.ok(insertPerson);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * @return R<PageInfo < SaPersonPojo>>
     * @Description 按线索id查询联系人列表
     * <AUTHOR>
     * @param[1] json
     * @time 2023/4/13 13:57
     */
    @ApiOperation(value = "按线索id查询联系人列表", notes = "按线索id查询联系人列表", produces = "application/json")
    @RequestMapping(value = "/getListByLeads", method = RequestMethod.GET)
    public R<List<SaPersonPojo>> getListByLeads(String id) {
        try {
            return R.ok(this.saPersonService.getListByLeads(id));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * @return R<PageInfo < SaPersonPojo>>
     * @Description 按客户id查询联系人列表
     * <AUTHOR>
     * @param[1] json
     * @time 2023/4/13 13:57
     */
    @ApiOperation(value = "按客户id查询联系人列表", notes = "按客户id查询联系人列表", produces = "application/json")
    @RequestMapping(value = "/getListByGroup", method = RequestMethod.GET)
    public R<List<SaPersonPojo>> getListByGroup(String id) {
        try {
            return R.ok(this.saPersonService.getListByGroup(id));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}
