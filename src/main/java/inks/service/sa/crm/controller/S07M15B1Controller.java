package inks.service.sa.crm.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.AESUtil;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.crm.domain.pojo.SaDmsuserPojo;
import inks.service.sa.crm.mapper.SaDmsuserMapper;
import inks.service.sa.crm.service.SaDmsuserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;

/**
 * DMS用户(Sa_DmsUser)表控制层
 *
 * <AUTHOR>
 * @since 2023-12-08 16:42:42
 */
@RestController
@RequestMapping("S07M15B1")
@Api(tags = "S07M15B1:DMS用户")
public class S07M15B1Controller extends SaDmsuserController {
    @Resource
    private SaDmsuserService saDmsuserService;
    @Resource
    private SaDmsuserMapper saDmsuserMapper;
    @Resource
    private SaRedisService saRedisService;

    /**
     * 通过（手机号,邮箱）查询单条数据UserName只能是手机号或者邮箱
     *
     * @param username 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取DMS用户详细信息ByUserName", notes = "获取DMS用户详细信息ByUserName", produces = "application/json")
    @RequestMapping(value = "/getEntityByUserName", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "PiDmsUser.List")
    public R<SaDmsuserPojo> getEntityByUserName(String username) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saDmsuserMapper.getEntityByUserName(username));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * @return R<SaDmsuserPojo>
     * @Description 初始化密码
     * <AUTHOR>
     * @param[1] keys是userid  初始化为123456
     * @time 2023/4/24 12:46
     */
    @ApiOperation(value = "初始化密码", notes = "初始化密码", produces = "application/json")
    @RequestMapping(value = "/initPassword", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "PiDmsUser.Edit")
    public R<SaDmsuserPojo> initPassword(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            SaDmsuserPojo pidmsuserPojo = new SaDmsuserPojo();
            pidmsuserPojo.setUserid(key);
            pidmsuserPojo.setLister(loginUser.getRealname());   // 制表
            pidmsuserPojo.setListerid(loginUser.getUserid());    // 制表id
            pidmsuserPojo.setTenantid(loginUser.getTenantid());   //租户id
            pidmsuserPojo.setModifydate(new Date());   //修改时间
            //加密密码
            pidmsuserPojo.setUserpassword(AESUtil.Encrypt("123456"));
            return R.ok(this.saDmsuserService.update(pidmsuserPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}
