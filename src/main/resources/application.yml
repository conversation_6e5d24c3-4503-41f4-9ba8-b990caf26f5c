server:
  port: 10657 #服务端口
#  ssl:
#    key-store: classpath:ssl/dev/tomcat.jks #证书文件地址
#    key-store-password: 123456        #密钥
#    key-store-type: JKS            #加密算法
#    key-alias: dev.inksyun.com                 #key名
spring:
  application:
    name: sa-crm
  profiles:
    active: dev
  main:
    allow-bean-definition-overriding: true #当遇到同样名字的时候，是否允许覆盖注册
  servlet:
    multipart:
      max-file-size: 20MB
      max-request-size: 20MB

logging:
  level:
    org:
      springframework:
        security: info

pagehelper:
  helper-dialect: mysql
  reasonable: true
  support-methods-arguments: true
debug: true