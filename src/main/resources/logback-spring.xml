<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- 引入Spring Boot默认的logback配置 -->
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>

    <!-- 设置日志上下文的名称 -->
    <contextName>logback</contextName>

    <!-- 设置日志文件路径  docker部署时，logs/ 在容器内的路径：/home/<USER>/logs
                        windows     logs/ 在当前项目目录下：inks-service-sa-crm/logs -->
    <property name="log.path" value="logs/"/>
<!--    <property name="log.path" value="/var/log/sacrm/" />-->

    <!-- 控制台输出配置 -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <!-- 日志级别过滤器，指定日志级别为debug及以上的日志将输出到控制台 -->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>debug</level>
        </filter>
        <encoder>
            <!-- 控制台日志输出的格式 -->
            <Pattern>${CONSOLE_LOG_PATTERN}</Pattern>
            <!-- 设置字符集为UTF-8 -->
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- 日志文件输出配置：INFO级别日志 -->
    <appender name="INFO_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 定义输出日志的文件路径 -->
        <file>${log.path}/oper/oper.log</file>
        <encoder>
            <!-- 定义日志文件的输出格式 -->
            <pattern>%msg%n</pattern>
            <!-- 设置字符集为UTF-8 -->
            <charset>UTF-8</charset>
        </encoder>
        <!-- 定义日志文件的滚动策略，基于文件大小和时间滚动 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 定义日志文件的命名规则，按天和文件大小滚动生成新文件 -->
            <fileNamePattern>${log.path}/oper/oper-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <!-- 单个日志文件的最大大小为100MB -->
            <maxFileSize>100MB</maxFileSize>
            <!-- 保留30天的日志文件 -->
            <maxHistory>30</maxHistory>
            <!-- 日志文件总大小限制为1GB -->
            <totalSizeCap>1GB</totalSizeCap>
        </rollingPolicy>
        <encoder class="net.logstash.logback.encoder.LogstashEncoder">
            <providers>
                <!-- 设置日志中的时间戳时区 -->
                <timestamp>
                    <timeZone>Asia/Shanghai</timeZone>
                </timestamp>
                <pattern>
                    <!-- 自定义日志格式为JSON格式，message字段包含日志内容 -->
                    <pattern>{"message": "%message"}</pattern>
                </pattern>
            </providers>
        </encoder>
        <!-- 接受INFO级别的日志 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>NEUTRAL</onMismatch>
        </filter>
        <!-- 接受DEBUG级别的日志 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>DEBUG</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>NEUTRAL</onMismatch>
        </filter>
    </appender>

    <!-- 日志文件输出配置：ERROR级别日志 -->
    <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 定义输出日志的文件路径 -->
        <file>${log.path}/error/error.log</file>
        <encoder>
            <!-- 定义日志文件的输出格式，包括日期、线程名、日志级别、日志位置和内容 -->
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</pattern>
            <!-- 设置字符集为UTF-8 -->
            <charset>UTF-8</charset>
        </encoder>
        <!-- 定义日志文件的滚动策略，基于文件大小和时间滚动 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 定义日志文件的命名规则，按天和文件大小滚动生成新文件 -->
            <fileNamePattern>${log.path}/error/error-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <!-- 单个日志文件的最大大小为100MB -->
            <maxFileSize>100MB</maxFileSize>
            <!-- 保留30天的日志文件 -->
            <maxHistory>30</maxHistory>
            <!-- 日志文件总大小限制为1GB -->
            <totalSizeCap>1GB</totalSizeCap>
        </rollingPolicy>
        <!-- 只接受ERROR级别的日志 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- 设置根日志级别为INFO，并指定输出到控制台、INFO文件和ERROR文件 -->
    <root level="INFO">
        <appender-ref ref="STDOUT" />
        <appender-ref ref="INFO_FILE" />
        <appender-ref ref="ERROR_FILE" />
    </root>

    <!-- 定义特定包或类的日志级别为ERROR，减少不必要的日志输出 -->
    <logger name="com.netflix" level="ERROR" />
    <logger name="net.sf.json" level="ERROR" />
    <logger name="org.springframework" level="ERROR" />
    <logger name="springfox" level="ERROR" />
</configuration>
