<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.crm.mapper.SaLeadsMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.crm.domain.pojo.SaLeadsPojo">
        <include refid="selectSaLeadsVo"/>
        where Sa_Leads.id = #{key}
    </select>
    <sql id="selectSaLeadsVo">
        select id,
               Userid,
               Deptid,
               LeadsState,
               GenGroupid,
               Groupid,
               CustName,
               CustType,
               CustClass,
               Telephone,
               Linkman,
               Position,
               Mobile,
               Email,
               CustIndustry,
               CustSource,
               CustAdd,
               Longitude,
               Latitude,
               ClueDetail,
               NextDate,
               Operatorid,
               Operator,
               DepartName,
               WorkStage,
               CustMark,
               ReceiveTime,
               EnabledMark,
               Photo1,
               Photo2,
               Photo3,
               StateNum,
               StateDate,
               Closed,
               Closedid,
               ClosedName,
               ClosedDate,
               RowNum,
               Remark,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Principalid,
               Principal,
               LastFollowDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Custom9,
               Custom10,
               Tenantid,
               TenantName,
               Revision
        from Sa_Leads
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.crm.domain.pojo.SaLeadsPojo">
        <include refid="selectSaLeadsVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_Leads.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.userid != null">
            and Sa_Leads.Userid like concat('%', #{SearchPojo.userid}, '%')
        </if>
        <if test="SearchPojo.deptid != null">
            and Sa_Leads.Deptid like concat('%',
                #{SearchPojo.deptid}, '%')
        </if>
        <if test="SearchPojo.leadsstate != null">
            and Sa_Leads.LeadsState like concat('%',
                #{SearchPojo.leadsstate}, '%')
        </if>
        <if test="SearchPojo.gengroupid != null">
            and Sa_Leads.GenGroupid like concat('%',
                #{SearchPojo.gengroupid}, '%')
        </if>
        <if test="SearchPojo.groupid != null">
            and Sa_Leads.Groupid like concat('%',
                #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.custname != null">
            and Sa_Leads.CustName like concat('%',
                #{SearchPojo.custname}, '%')
        </if>
        <if test="SearchPojo.custtype != null">
            and Sa_Leads.CustType like concat('%',
                #{SearchPojo.custtype}, '%')
        </if>
        <if test="SearchPojo.custclass != null">
            and Sa_Leads.CustClass like concat('%',
                #{SearchPojo.custclass}, '%')
        </if>
        <if test="SearchPojo.telephone != null">
            and Sa_Leads.Telephone like concat('%',
                #{SearchPojo.telephone}, '%')
        </if>
        <if test="SearchPojo.linkman != null">
            and Sa_Leads.Linkman like concat('%',
                #{SearchPojo.linkman}, '%')
        </if>
        <if test="SearchPojo.position != null">
            and Sa_Leads.Position like concat('%',
                #{SearchPojo.position}, '%')
        </if>
        <if test="SearchPojo.mobile != null">
            and Sa_Leads.Mobile like concat('%',
                #{SearchPojo.mobile}, '%')
        </if>
        <if test="SearchPojo.email != null">
            and Sa_Leads.Email like concat('%',
                #{SearchPojo.email}, '%')
        </if>
        <if test="SearchPojo.custindustry != null">
            and Sa_Leads.CustIndustry like concat('%',
                #{SearchPojo.custindustry}, '%')
        </if>
        <if test="SearchPojo.custsource != null">
            and Sa_Leads.CustSource like concat('%',
                #{SearchPojo.custsource}, '%')
        </if>
        <if test="SearchPojo.custadd != null">
            and Sa_Leads.CustAdd like concat('%',
                #{SearchPojo.custadd}, '%')
        </if>
        <if test="SearchPojo.cluedetail != null">
            and Sa_Leads.ClueDetail like concat('%',
                #{SearchPojo.cluedetail}, '%')
        </if>
        <if test="SearchPojo.operatorid != null">
            and Sa_Leads.Principalid like concat('%',
                #{SearchPojo.operatorid}, '%')
        </if>
        <if test="SearchPojo.operator != null">
            and Sa_Leads.Operator like concat('%',
                #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.departname != null">
            and Sa_Leads.DepartName like concat('%',
                #{SearchPojo.departname}, '%')
        </if>
        <if test="SearchPojo.workstage != null">
            and Sa_Leads.WorkStage like concat('%',
                #{SearchPojo.workstage}, '%')
        </if>
        <if test="SearchPojo.photo1 != null">
            and Sa_Leads.Photo1 like concat('%',
                #{SearchPojo.photo1}, '%')
        </if>
        <if test="SearchPojo.photo2 != null">
            and Sa_Leads.Photo2 like concat('%',
                #{SearchPojo.photo2}, '%')
        </if>
        <if test="SearchPojo.photo3 != null">
            and Sa_Leads.Photo3 like concat('%',
                #{SearchPojo.photo3}, '%')
        </if>
        <if test="SearchPojo.closedid != null">
            and Sa_Leads.Closedid like concat('%',
                #{SearchPojo.closedid}, '%')
        </if>
        <if test="SearchPojo.closedname != null">
            and Sa_Leads.ClosedName like concat('%',
                #{SearchPojo.closedname}, '%')
        </if>
        <if test="SearchPojo.summary != null">
            and Sa_Leads.Summary like concat('%',
                #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Sa_Leads.CreateBy like concat('%',
                #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Sa_Leads.CreateByid like concat('%',
                #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Sa_Leads.Lister like concat('%',
                #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Sa_Leads.Listerid like concat('%',
                #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.principal != null">
            and Sa_Leads.Principal like concat('%',
                #{SearchPojo.principal}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Sa_Leads.Custom1 like concat('%',
                #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Sa_Leads.Custom2 like concat('%',
                #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Sa_Leads.Custom3 like concat('%',
                #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Sa_Leads.Custom4 like concat('%',
                #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Sa_Leads.Custom5 like concat('%',
                #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null">
            and Sa_Leads.Custom6 like concat('%',
                #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null">
            and Sa_Leads.Custom7 like concat('%',
                #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null">
            and Sa_Leads.Custom8 like concat('%',
                #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null">
            and Sa_Leads.Custom9 like concat('%',
                #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null">
            and Sa_Leads.Custom10 like concat('%',
                #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null">
            and Sa_Leads.TenantName like concat('%',
                #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.userid != null">
                or Sa_Leads.Userid like concat('%', #{SearchPojo.userid}, '%')
            </if>
            <if test="SearchPojo.deptid != null">
                or Sa_Leads.Deptid like concat('%', #{SearchPojo.deptid}, '%')
            </if>
            <if test="SearchPojo.leadsstate != null">
                or Sa_Leads.LeadsState like concat('%', #{SearchPojo.leadsstate}, '%')
            </if>
            <if test="SearchPojo.gengroupid != null">
                or Sa_Leads.GenGroupid like concat('%', #{SearchPojo.gengroupid}, '%')
            </if>
            <if test="SearchPojo.groupid != null">
                or Sa_Leads.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.custname != null">
                or Sa_Leads.CustName like concat('%', #{SearchPojo.custname}, '%')
            </if>
            <if test="SearchPojo.custtype != null">
                or Sa_Leads.CustType like concat('%', #{SearchPojo.custtype}, '%')
            </if>
            <if test="SearchPojo.custclass != null">
                or Sa_Leads.CustClass like concat('%', #{SearchPojo.custclass}, '%')
            </if>
            <if test="SearchPojo.telephone != null">
                or Sa_Leads.Telephone like concat('%', #{SearchPojo.telephone}, '%')
            </if>
            <if test="SearchPojo.linkman != null">
                or Sa_Leads.Linkman like concat('%', #{SearchPojo.linkman}, '%')
            </if>
            <if test="SearchPojo.position != null">
                or Sa_Leads.Position like concat('%', #{SearchPojo.position}, '%')
            </if>
            <if test="SearchPojo.mobile != null">
                or Sa_Leads.Mobile like concat('%', #{SearchPojo.mobile}, '%')
            </if>
            <if test="SearchPojo.email != null">
                or Sa_Leads.Email like concat('%', #{SearchPojo.email}, '%')
            </if>
            <if test="SearchPojo.custindustry != null">
                or Sa_Leads.CustIndustry like concat('%', #{SearchPojo.custindustry}, '%')
            </if>
            <if test="SearchPojo.custsource != null">
                or Sa_Leads.CustSource like concat('%', #{SearchPojo.custsource}, '%')
            </if>
            <if test="SearchPojo.custadd != null">
                or Sa_Leads.CustAdd like concat('%', #{SearchPojo.custadd}, '%')
            </if>
            <if test="SearchPojo.cluedetail != null">
                or Sa_Leads.ClueDetail like concat('%', #{SearchPojo.cluedetail}, '%')
            </if>
            <if test="SearchPojo.operatorid != null">
                or Sa_Leads.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
            </if>
            <if test="SearchPojo.operator != null">
                or Sa_Leads.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.departname != null">
                or Sa_Leads.DepartName like concat('%', #{SearchPojo.departname}, '%')
            </if>
            <if test="SearchPojo.workstage != null">
                or Sa_Leads.WorkStage like concat('%', #{SearchPojo.workstage}, '%')
            </if>
            <if test="SearchPojo.photo1 != null">
                or Sa_Leads.Photo1 like concat('%', #{SearchPojo.photo1}, '%')
            </if>
            <if test="SearchPojo.photo2 != null">
                or Sa_Leads.Photo2 like concat('%', #{SearchPojo.photo2}, '%')
            </if>
            <if test="SearchPojo.photo3 != null">
                or Sa_Leads.Photo3 like concat('%', #{SearchPojo.photo3}, '%')
            </if>
            <if test="SearchPojo.closedid != null">
                or Sa_Leads.Closedid like concat('%', #{SearchPojo.closedid}, '%')
            </if>
            <if test="SearchPojo.closedname != null">
                or Sa_Leads.ClosedName like concat('%', #{SearchPojo.closedname}, '%')
            </if>
            <if test="SearchPojo.summary != null">
                or Sa_Leads.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Sa_Leads.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Sa_Leads.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Sa_Leads.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Sa_Leads.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.principal != null">
                or Sa_Leads.Principal like concat('%', #{SearchPojo.principal}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Sa_Leads.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Sa_Leads.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Sa_Leads.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Sa_Leads.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Sa_Leads.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null">
                or Sa_Leads.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null">
                or Sa_Leads.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null">
                or Sa_Leads.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null">
                or Sa_Leads.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null">
                or Sa_Leads.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null">
                or Sa_Leads.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into Sa_Leads(id, Userid, Deptid, LeadsState, GenGroupid, Groupid, CustName, CustType, CustClass, Telephone, Linkman, Position, Mobile, Email, CustIndustry, CustSource, CustAdd, Longitude, Latitude, ClueDetail, NextDate, Operatorid, Operator, DepartName, WorkStage, CustMark, ReceiveTime, EnabledMark, Photo1, Photo2, Photo3, StateNum, StateDate, Closed, Closedid, ClosedName, ClosedDate, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Principalid, Principal, LastFollowDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{userid}, #{deptid}, #{leadsstate}, #{gengroupid}, #{groupid}, #{custname}, #{custtype}, #{custclass}, #{telephone}, #{linkman}, #{position}, #{mobile}, #{email}, #{custindustry}, #{custsource}, #{custadd}, #{longitude}, #{latitude}, #{cluedetail}, #{nextdate}, #{operatorid}, #{operator}, #{departname}, #{workstage}, #{custmark}, #{receivetime}, #{enabledmark}, #{photo1}, #{photo2}, #{photo3}, #{statenum}, #{statedate}, #{closed}, #{closedid}, #{closedname}, #{closeddate}, #{rownum}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{principalid}, #{principal}, #{lastfollowdate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_Leads
        <set>
            <if test="userid != null">
                Userid =#{userid},
            </if>
            <if test="deptid != null">
                Deptid =#{deptid},
            </if>
            <if test="leadsstate != null">
                LeadsState =#{leadsstate},
            </if>
            <if test="gengroupid != null">
                GenGroupid =#{gengroupid},
            </if>
            <if test="groupid != null">
                Groupid =#{groupid},
            </if>
            <if test="custname != null">
                CustName =#{custname},
            </if>
            <if test="custtype != null">
                CustType =#{custtype},
            </if>
            <if test="custclass != null">
                CustClass =#{custclass},
            </if>
            <if test="telephone != null">
                Telephone =#{telephone},
            </if>
            <if test="linkman != null">
                Linkman =#{linkman},
            </if>
            <if test="position != null">
                Position =#{position},
            </if>
            <if test="mobile != null">
                Mobile =#{mobile},
            </if>
            <if test="email != null">
                Email =#{email},
            </if>
            <if test="custindustry != null">
                CustIndustry =#{custindustry},
            </if>
            <if test="custsource != null">
                CustSource =#{custsource},
            </if>
            <if test="custadd != null">
                CustAdd =#{custadd},
            </if>
            <if test="longitude != null">
                Longitude =#{longitude},
            </if>
            <if test="latitude != null">
                Latitude =#{latitude},
            </if>
            <if test="cluedetail != null">
                ClueDetail =#{cluedetail},
            </if>
            <if test="nextdate != null">
                NextDate =#{nextdate},
            </if>
            <if test="operatorid != null">
                Operatorid =#{operatorid},
            </if>
            <if test="operator != null">
                Operator =#{operator},
            </if>
            <if test="departname != null">
                DepartName =#{departname},
            </if>
            <if test="workstage != null">
                WorkStage =#{workstage},
            </if>
            <if test="custmark != null">
                CustMark =#{custmark},
            </if>
            <if test="receivetime != null">
                ReceiveTime =#{receivetime},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="photo1 != null">
                Photo1 =#{photo1},
            </if>
            <if test="photo2 != null">
                Photo2 =#{photo2},
            </if>
            <if test="photo3 != null">
                Photo3 =#{photo3},
            </if>
            <if test="statenum != null">
                StateNum =#{statenum},
            </if>
            <if test="statedate != null">
                StateDate =#{statedate},
            </if>
            <if test="closed != null">
                Closed =#{closed},
            </if>
            <if test="closedid != null">
                Closedid =#{closedid},
            </if>
            <if test="closedname != null">
                ClosedName =#{closedname},
            </if>
            <if test="closeddate != null">
                ClosedDate =#{closeddate},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="principalid != null">
                Principalid =#{principalid},
            </if>
            <if test="principal != null">
                Principal =#{principal},
            </if>
            <if test="lastfollowdate != null">
                LastFollowDate =#{lastfollowdate},
            </if>
            <if test="custom1 != null">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_Leads where id = #{key}
    </delete>

    <select id="getEntityByChanceid" resultType="inks.service.sa.crm.domain.pojo.SaLeadsPojo">
        select * from Sa_Leads where Sa_Leads.id = (select leadsid from Sa_Chance where id=#{chanceid})
    </select>

    <select id="getEntityByCustname" resultType="inks.service.sa.crm.domain.pojo.SaLeadsPojo">
        <include refid="selectSaLeadsVo"/>
        where Sa_Leads.CustName = #{custname}
    </select>

    <update id="syncNextDateByFollowview">
        update Sa_Leads
        set NextDate =(select MAX(NextDate) from Sa_Followview where Citeid = #{leadsid} and FinishMark = 0)
        where id = #{leadsid}
    </update>
</mapper>

