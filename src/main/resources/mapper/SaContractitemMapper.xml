<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.crm.mapper.SaContractitemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.crm.domain.pojo.SaContractitemPojo">
        <include refid="selectSaContractitemVo"/>
        where Sa_ContractItem.id = #{key} 
    </select>
    <sql id="selectSaContractitemVo">
         select
id, Pid, Goodsid, ItemCode, ItemName, ItemSpec, ItemUnit, Price, Quantity, ItemTaxrate, Amount, TaxTotal, TaxPrice, TaxAmount, Operator, Operatorid, ChanceItemid, RowNum, AttributeJson, AttributeStr, Remark, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision        from Sa_ContractItem
    </sql>
         <!--查询List-->
    <select id="getList" resultType="inks.service.sa.crm.domain.pojo.SaContractitemPojo">
        <include refid="selectSaContractitemVo"/>
        where Sa_ContractItem.Pid = #{Pid}
        order by RowNum
    </select>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.crm.domain.pojo.SaContractitemPojo">
        <include refid="selectSaContractitemVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
              <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_ContractItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   and Sa_ContractItem.pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
   and Sa_ContractItem.goodsid like concat('%', #{SearchPojo.goodsid}, '%')
</if>
<if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
   and Sa_ContractItem.itemcode like concat('%', #{SearchPojo.itemcode}, '%')
</if>
<if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
   and Sa_ContractItem.itemname like concat('%', #{SearchPojo.itemname}, '%')
</if>
<if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
   and Sa_ContractItem.itemspec like concat('%', #{SearchPojo.itemspec}, '%')
</if>
<if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
   and Sa_ContractItem.itemunit like concat('%', #{SearchPojo.itemunit}, '%')
</if>
<if test="SearchPojo.operator != null and SearchPojo.operator != ''">
   and Sa_ContractItem.operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.operatorid != null and SearchPojo.operatorid != ''">
   and Sa_ContractItem.operatorid like concat('%', #{SearchPojo.operatorid}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   and Sa_ContractItem.remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   and Sa_ContractItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   and Sa_ContractItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   and Sa_ContractItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   and Sa_ContractItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   and Sa_ContractItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
   and Sa_ContractItem.custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
   and Sa_ContractItem.custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
   and Sa_ContractItem.custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
   and Sa_ContractItem.custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
   and Sa_ContractItem.custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   or Sa_ContractItem.Pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
   or Sa_ContractItem.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
</if>
<if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
   or Sa_ContractItem.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
</if>
<if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
   or Sa_ContractItem.ItemName like concat('%', #{SearchPojo.itemname}, '%')
</if>
<if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
   or Sa_ContractItem.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
</if>
<if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
   or Sa_ContractItem.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
</if>
<if test="SearchPojo.operator != null and SearchPojo.operator != ''">
   or Sa_ContractItem.Operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.operatorid != null and SearchPojo.operatorid != ''">
   or Sa_ContractItem.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   or Sa_ContractItem.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   or Sa_ContractItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   or Sa_ContractItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   or Sa_ContractItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   or Sa_ContractItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   or Sa_ContractItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
   or Sa_ContractItem.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
   or Sa_ContractItem.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
   or Sa_ContractItem.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
   or Sa_ContractItem.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
   or Sa_ContractItem.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
</trim>
     </sql>

    
    <!--新增所有列-->
    <insert id="insert" >
        insert into Sa_ContractItem(id, Pid, Goodsid, ItemCode, ItemName, ItemSpec, ItemUnit, Price, Quantity, ItemTaxrate, Amount, TaxTotal, TaxPrice, TaxAmount, Operator, Operatorid, ChanceItemid, RowNum, AttributeJson, AttributeStr, Remark, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision)
        values (#{id}, #{pid}, #{goodsid}, #{itemcode}, #{itemname}, #{itemspec}, #{itemunit}, #{price}, #{quantity}, #{itemtaxrate}, #{amount}, #{taxtotal}, #{taxprice}, #{taxamount}, #{operator}, #{operatorid}, #{chanceitemid}, #{rownum}, #{attributejson}, #{attributestr}, #{remark}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_ContractItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="goodsid != null ">
                Goodsid = #{goodsid},
            </if>
            <if test="itemcode != null ">
                ItemCode = #{itemcode},
            </if>
            <if test="itemname != null ">
                ItemName = #{itemname},
            </if>
            <if test="itemspec != null ">
                ItemSpec = #{itemspec},
            </if>
            <if test="itemunit != null ">
                ItemUnit = #{itemunit},
            </if>
            <if test="price != null">
                Price = #{price},
            </if>
            <if test="quantity != null">
                Quantity = #{quantity},
            </if>
            <if test="itemtaxrate != null">
                ItemTaxrate = #{itemtaxrate},
            </if>
            <if test="amount != null">
                Amount = #{amount},
            </if>
            <if test="taxtotal != null">
                TaxTotal = #{taxtotal},
            </if>
            <if test="taxprice != null">
                TaxPrice = #{taxprice},
            </if>
            <if test="taxamount != null">
                TaxAmount = #{taxamount},
            </if>
            <if test="operator != null ">
                Operator = #{operator},
            </if>
            <if test="operatorid != null ">
                Operatorid = #{operatorid},
            </if>
            <if test="chanceitemid != null ">
                ChanceItemid = #{chanceitemid},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="attributejson != null ">
                AttributeJson = #{attributejson},
            </if>
            <if test="attributestr != null ">
                AttributeStr = #{attributestr},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 = #{custom10},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_ContractItem where id = #{key} 
    </delete>

</mapper>

