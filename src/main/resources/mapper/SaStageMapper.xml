<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.crm.mapper.SaStageMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.crm.domain.pojo.SaStagePojo">
        <include refid="selectbillVo"/>
        where Sa_Stage.id = #{key} 
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
         select
id, GroupType, GroupCode, GroupName, StageJson, Description, EnabledMark, Summary, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Deptid, Tenantid, TenantName, Revision        from Sa_Stage
    </sql>
    <sql id="selectdetailVo">
         select
id, GroupType, GroupCode, GroupName, StageJson, Description, EnabledMark, Summary, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Deptid, Tenantid, TenantName, Revision        from Sa_Stage
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.crm.domain.pojo.SaStageitemdetailPojo">
        <include refid="selectdetailVo"/>
         where 1 = 1 
       <if test="filterstr != null ">
            ${filterstr}
        </if> 
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_Stage.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.grouptype != null ">
   and Sa_Stage.grouptype like concat('%', #{SearchPojo.grouptype}, '%')
</if>
<if test="SearchPojo.groupcode != null ">
   and Sa_Stage.groupcode like concat('%', #{SearchPojo.groupcode}, '%')
</if>
<if test="SearchPojo.groupname != null ">
   and Sa_Stage.groupname like concat('%', #{SearchPojo.groupname}, '%')
</if>
<if test="SearchPojo.description != null ">
   and Sa_Stage.description like concat('%', #{SearchPojo.description}, '%')
</if>
<if test="SearchPojo.summary != null ">
   and Sa_Stage.summary like concat('%', #{SearchPojo.summary}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Sa_Stage.createby like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Sa_Stage.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Sa_Stage.lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Sa_Stage.listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Sa_Stage.custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Sa_Stage.custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Sa_Stage.custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Sa_Stage.custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Sa_Stage.custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and Sa_Stage.custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and Sa_Stage.custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and Sa_Stage.custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   and Sa_Stage.custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   and Sa_Stage.custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.deptid != null ">
   and Sa_Stage.deptid like concat('%', #{SearchPojo.deptid}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Sa_Stage.tenantname like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.grouptype != null ">
   or Sa_Stage.GroupType like concat('%', #{SearchPojo.grouptype}, '%')
</if>
<if test="SearchPojo.groupcode != null ">
   or Sa_Stage.GroupCode like concat('%', #{SearchPojo.groupcode}, '%')
</if>
<if test="SearchPojo.groupname != null ">
   or Sa_Stage.GroupName like concat('%', #{SearchPojo.groupname}, '%')
</if>
<if test="SearchPojo.description != null ">
   or Sa_Stage.Description like concat('%', #{SearchPojo.description}, '%')
</if>
<if test="SearchPojo.summary != null ">
   or Sa_Stage.Summary like concat('%', #{SearchPojo.summary}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Sa_Stage.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Sa_Stage.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Sa_Stage.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Sa_Stage.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Sa_Stage.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Sa_Stage.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Sa_Stage.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Sa_Stage.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Sa_Stage.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   or Sa_Stage.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   or Sa_Stage.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   or Sa_Stage.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   or Sa_Stage.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   or Sa_Stage.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.deptid != null ">
   or Sa_Stage.Deptid like concat('%', #{SearchPojo.deptid}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Sa_Stage.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
     
         <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.crm.domain.pojo.SaStagePojo">
        <include refid="selectbillVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_Stage.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="thand"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="thor"></include>        
         </if>
        </if> 
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="thand">
<if test="SearchPojo.grouptype != null ">
   and Sa_Stage.GroupType like concat('%', #{SearchPojo.grouptype}, '%')
</if>
<if test="SearchPojo.groupcode != null ">
   and Sa_Stage.GroupCode like concat('%', #{SearchPojo.groupcode}, '%')
</if>
<if test="SearchPojo.groupname != null ">
   and Sa_Stage.GroupName like concat('%', #{SearchPojo.groupname}, '%')
</if>
<if test="SearchPojo.description != null ">
   and Sa_Stage.Description like concat('%', #{SearchPojo.description}, '%')
</if>
<if test="SearchPojo.summary != null ">
   and Sa_Stage.Summary like concat('%', #{SearchPojo.summary}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Sa_Stage.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Sa_Stage.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Sa_Stage.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Sa_Stage.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Sa_Stage.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Sa_Stage.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Sa_Stage.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Sa_Stage.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Sa_Stage.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and Sa_Stage.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and Sa_Stage.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and Sa_Stage.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   and Sa_Stage.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   and Sa_Stage.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.deptid != null ">
   and Sa_Stage.Deptid like concat('%', #{SearchPojo.deptid}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Sa_Stage.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="thor">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.grouptype != null ">
   or Sa_Stage.GroupType like concat('%', #{SearchPojo.grouptype}, '%')
</if>
<if test="SearchPojo.groupcode != null ">
   or Sa_Stage.GroupCode like concat('%', #{SearchPojo.groupcode}, '%')
</if>
<if test="SearchPojo.groupname != null ">
   or Sa_Stage.GroupName like concat('%', #{SearchPojo.groupname}, '%')
</if>
<if test="SearchPojo.description != null ">
   or Sa_Stage.Description like concat('%', #{SearchPojo.description}, '%')
</if>
<if test="SearchPojo.summary != null ">
   or Sa_Stage.Summary like concat('%', #{SearchPojo.summary}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Sa_Stage.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Sa_Stage.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Sa_Stage.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Sa_Stage.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Sa_Stage.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Sa_Stage.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Sa_Stage.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Sa_Stage.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Sa_Stage.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   or Sa_Stage.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   or Sa_Stage.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   or Sa_Stage.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   or Sa_Stage.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   or Sa_Stage.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.deptid != null ">
   or Sa_Stage.Deptid like concat('%', #{SearchPojo.deptid}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Sa_Stage.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
     
    <!--新增所有列-->
    <insert id="insert" >
        insert into Sa_Stage(id, GroupType, GroupCode, GroupName, StageJson, Description, EnabledMark, Summary, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Deptid, Tenantid, TenantName, Revision)
        values (#{id}, #{grouptype}, #{groupcode}, #{groupname}, #{stagejson}, #{description}, #{enabledmark}, #{summary}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{deptid}, #{tenantid}, #{tenantname}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_Stage
        <set>
            <if test="grouptype != null ">
                GroupType =#{grouptype},
            </if>
            <if test="groupcode != null ">
                GroupCode =#{groupcode},
            </if>
            <if test="groupname != null ">
                GroupName =#{groupname},
            </if>
            <if test="stagejson != null ">
                StageJson =#{stagejson},
            </if>
            <if test="description != null ">
                Description =#{description},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="summary != null ">
                Summary =#{summary},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="deptid != null ">
                Deptid =#{deptid},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_Stage where id = #{key} 
    </delete>
                                                                                                                <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String" parameterType="inks.service.sa.crm.domain.pojo.SaStagePojo">
        select
          id
        from Sa_StageItem
        where Pid = #{id}
        <if test="item !=null and item.size()>0">
         and id not in
        <foreach collection="item" open="(" close=")" separator="," item="item">
            <if test="item.id != null">
                #{item.id}
            </if>
            <if test="item.id == null">
                 ''
            </if>
        </foreach>
         </if>
    </select>

</mapper>

