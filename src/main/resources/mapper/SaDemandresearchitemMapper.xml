<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.crm.mapper.SaDemandresearchitemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.crm.domain.pojo.SaDemandresearchitemPojo">
        <include refid="selectSaDemandresearchitemVo"/>
        where Sa_DemandResearchItem.id = #{key} 
    </select>
    <sql id="selectSaDemandresearchitemVo">
        select id,
               Pid,
               DemandDictid,
               Description,
               MainMark,
               Level,
               RequirementDate,
               Difficulty,
               Solution,
               RowNum,
               Remark,
               Tenantid,
               Revision
        from Sa_DemandResearchItem
    </sql>
         <!--查询List-->
    <select id="getList" resultType="inks.service.sa.crm.domain.pojo.SaDemandresearchitemPojo">
        <include refid="selectSaDemandresearchitemVo"/>
        where Sa_DemandResearchItem.Pid = #{Pid}
        order by RowNum 
    </select>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.crm.domain.pojo.SaDemandresearchitemPojo">
        <include refid="selectSaDemandresearchitemVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
              <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_DemandResearchItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   and Sa_DemandResearchItem.pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.demanddictid != null and SearchPojo.demanddictid != ''">
   and Sa_DemandResearchItem.demanddictid like concat('%', #{SearchPojo.demanddictid}, '%')
</if>
<if test="SearchPojo.description != null and SearchPojo.description != ''">
   and Sa_DemandResearchItem.description like concat('%', #{SearchPojo.description}, '%')
</if>
<if test="SearchPojo.solution != null and SearchPojo.solution != ''">
   and Sa_DemandResearchItem.solution like concat('%', #{SearchPojo.solution}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   and Sa_DemandResearchItem.remark like concat('%', #{SearchPojo.remark}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   or Sa_DemandResearchItem.Pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.demanddictid != null and SearchPojo.demanddictid != ''">
   or Sa_DemandResearchItem.DemandDictid like concat('%', #{SearchPojo.demanddictid}, '%')
</if>
<if test="SearchPojo.description != null and SearchPojo.description != ''">
   or Sa_DemandResearchItem.Description like concat('%', #{SearchPojo.description}, '%')
</if>
<if test="SearchPojo.solution != null and SearchPojo.solution != ''">
   or Sa_DemandResearchItem.Solution like concat('%', #{SearchPojo.solution}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   or Sa_DemandResearchItem.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
</trim>
     </sql>

    
    <!--新增所有列-->
    <insert id="insert" >
        insert into Sa_DemandResearchItem(id, Pid, DemandDictid, Description, MainMark, Level, RequirementDate, Difficulty, Solution, RowNum, Remark, Tenantid, Revision)
        values (#{id}, #{pid}, #{demanddictid}, #{description}, #{mainmark}, #{level}, #{requirementdate}, #{difficulty}, #{solution}, #{rownum}, #{remark}, #{tenantid}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_DemandResearchItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="demanddictid != null ">
                DemandDictid = #{demanddictid},
            </if>
            <if test="description != null ">
                Description = #{description},
            </if>
            <if test="mainmark != null">
                MainMark = #{mainmark},
            </if>
            <if test="level != null">
                Level = #{level},
            </if>
            <if test="requirementdate != null">
                RequirementDate = #{requirementdate},
            </if>
            <if test="difficulty != null">
                Difficulty = #{difficulty},
            </if>
            <if test="solution != null ">
                Solution = #{solution},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_DemandResearchItem where id = #{key} 
    </delete>

</mapper>

