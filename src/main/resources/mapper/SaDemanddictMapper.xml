<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.crm.mapper.SaDemanddictMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.crm.domain.pojo.SaDemanddictPojo">
        <include refid="selectSaDemanddictVo"/>
        where Sa_DemandDict.id = #{key} 
    </select>
    <sql id="selectSaDemanddictVo">
         select
id, DemandCode, DemandName, DemandType, Description, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Tenantid, Revision        from Sa_DemandDict
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.crm.domain.pojo.SaDemanddictPojo">
        <include refid="selectSaDemanddictVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_DemandDict.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.demandcode != null ">
   and Sa_DemandDict.DemandCode like concat('%', #{SearchPojo.demandcode}, '%')
</if>
<if test="SearchPojo.demandname != null ">
   and Sa_DemandDict.DemandName like concat('%', #{SearchPojo.demandname}, '%')
</if>
<if test="SearchPojo.demandtype != null ">
   and Sa_DemandDict.DemandType like concat('%', #{SearchPojo.demandtype}, '%')
</if>
<if test="SearchPojo.description != null ">
   and Sa_DemandDict.Description like concat('%', #{SearchPojo.description}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Sa_DemandDict.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Sa_DemandDict.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Sa_DemandDict.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Sa_DemandDict.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Sa_DemandDict.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.demandcode != null ">
   or Sa_DemandDict.DemandCode like concat('%', #{SearchPojo.demandcode}, '%')
</if>
<if test="SearchPojo.demandname != null ">
   or Sa_DemandDict.DemandName like concat('%', #{SearchPojo.demandname}, '%')
</if>
<if test="SearchPojo.demandtype != null ">
   or Sa_DemandDict.DemandType like concat('%', #{SearchPojo.demandtype}, '%')
</if>
<if test="SearchPojo.description != null ">
   or Sa_DemandDict.Description like concat('%', #{SearchPojo.description}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Sa_DemandDict.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Sa_DemandDict.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Sa_DemandDict.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Sa_DemandDict.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Sa_DemandDict.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Sa_DemandDict(id, DemandCode, DemandName, DemandType, Description, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Tenantid, Revision)
        values (#{id}, #{demandcode}, #{demandname}, #{demandtype}, #{description}, #{rownum}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{tenantid}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_DemandDict
        <set>
            <if test="demandcode != null ">
                DemandCode =#{demandcode},
            </if>
            <if test="demandname != null ">
                DemandName =#{demandname},
            </if>
            <if test="demandtype != null ">
                DemandType =#{demandtype},
            </if>
            <if test="description != null ">
                Description =#{description},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_DemandDict where id = #{key} 
    </delete>
</mapper>

