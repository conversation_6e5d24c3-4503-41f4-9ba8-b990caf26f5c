<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.crm.mapper.SaBusinessstageMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.crm.domain.pojo.SaBusinessstagePojo">
        <include refid="selectSaBusinessstageVo"/>
        where Sa_BusinessStage.id = #{key} 
    </select>
    <sql id="selectSaBusinessstageVo">
         select
id, Pid, Parentid, StageItemid, StageName, FinishMark, PlanDate, Lister, Listerid, ModifyDate, Remark, RowNum, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision        from Sa_BusinessStage
    </sql>
         <!--查询List-->
    <select id="getList" resultType="inks.service.sa.crm.domain.pojo.SaBusinessstagePojo">
        <include refid="selectSaBusinessstageVo"/>
        where Sa_BusinessStage.Pid = #{Pid}
        order by RowNum 
    </select>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.crm.domain.pojo.SaBusinessstagePojo">
        <include refid="selectSaBusinessstageVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
              <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_BusinessStage.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   and Sa_BusinessStage.pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.parentid != null and SearchPojo.parentid != ''">
   and Sa_BusinessStage.parentid like concat('%', #{SearchPojo.parentid}, '%')
</if>
<if test="SearchPojo.stageitemid != null and SearchPojo.stageitemid != ''">
   and Sa_BusinessStage.stageitemid like concat('%', #{SearchPojo.stageitemid}, '%')
</if>
<if test="SearchPojo.stagename != null and SearchPojo.stagename != ''">
   and Sa_BusinessStage.stagename like concat('%', #{SearchPojo.stagename}, '%')
</if>
<if test="SearchPojo.lister != null and SearchPojo.lister != ''">
   and Sa_BusinessStage.lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
   and Sa_BusinessStage.listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   and Sa_BusinessStage.remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   and Sa_BusinessStage.custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   and Sa_BusinessStage.custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   and Sa_BusinessStage.custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   and Sa_BusinessStage.custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   and Sa_BusinessStage.custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
   and Sa_BusinessStage.custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
   and Sa_BusinessStage.custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
   and Sa_BusinessStage.custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
   and Sa_BusinessStage.custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
   and Sa_BusinessStage.custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   or Sa_BusinessStage.Pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.parentid != null and SearchPojo.parentid != ''">
   or Sa_BusinessStage.Parentid like concat('%', #{SearchPojo.parentid}, '%')
</if>
<if test="SearchPojo.stageitemid != null and SearchPojo.stageitemid != ''">
   or Sa_BusinessStage.StageItemid like concat('%', #{SearchPojo.stageitemid}, '%')
</if>
<if test="SearchPojo.stagename != null and SearchPojo.stagename != ''">
   or Sa_BusinessStage.StageName like concat('%', #{SearchPojo.stagename}, '%')
</if>
<if test="SearchPojo.lister != null and SearchPojo.lister != ''">
   or Sa_BusinessStage.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
   or Sa_BusinessStage.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   or Sa_BusinessStage.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   or Sa_BusinessStage.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   or Sa_BusinessStage.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   or Sa_BusinessStage.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   or Sa_BusinessStage.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   or Sa_BusinessStage.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
   or Sa_BusinessStage.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
   or Sa_BusinessStage.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
   or Sa_BusinessStage.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
   or Sa_BusinessStage.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
   or Sa_BusinessStage.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
</trim>
     </sql>

    
    <!--新增所有列-->
    <insert id="insert" >
        insert into Sa_BusinessStage(id, Pid, Parentid, StageItemid, StageName, FinishMark, PlanDate, Lister, Listerid, ModifyDate, Remark, RowNum, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision)
        values (#{id}, #{pid}, #{parentid}, #{stageitemid}, #{stagename}, #{finishmark}, #{plandate}, #{lister}, #{listerid}, #{modifydate}, #{remark}, #{rownum}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_BusinessStage
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="parentid != null ">
                Parentid = #{parentid},
            </if>
            <if test="stageitemid != null ">
                StageItemid = #{stageitemid},
            </if>
            <if test="stagename != null ">
                StageName = #{stagename},
            </if>
            <if test="finishmark != null">
                FinishMark = #{finishmark},
            </if>
            <if test="plandate != null">
                PlanDate = #{plandate},
            </if>
            <if test="lister != null ">
                Lister = #{lister},
            </if>
            <if test="listerid != null ">
                Listerid = #{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate = #{modifydate},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 = #{custom10},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_BusinessStage where id = #{key} 
    </delete>

</mapper>

