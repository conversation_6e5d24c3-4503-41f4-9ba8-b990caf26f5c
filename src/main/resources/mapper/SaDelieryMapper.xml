<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.crm.mapper.SaDelieryMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.crm.domain.pojo.SaDelieryPojo">
        <include refid="selectbillVo"/>
        where Sa_Deliery.id = #{key} 
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        select Sa_Deliery.id,
               Sa_Deliery.RefNo,
               Sa_Deliery.BillType,
               Sa_Deliery.BillTitle,
               Sa_Deliery.BillDate,
               Sa_Deliery.Groupid,
               Sa_Deliery.Telephone,
               Sa_Deliery.Linkman,
               Sa_Deliery.DeliAdd,
               Sa_Deliery.Taxrate,
               Sa_Deliery.TranSport,
               Sa_Deliery.Salesman,
               Sa_Deliery.Salesmanid,
               Sa_Deliery.Operator,
               Sa_Deliery.Operatorid,
               Sa_Deliery.Summary,
               Sa_Deliery.CreateBy,
               Sa_Deliery.CreateByid,
               Sa_Deliery.CreateDate,
               Sa_Deliery.Lister,
               Sa_Deliery.Listerid,
               Sa_Deliery.ModifyDate,
               Sa_Deliery.Assessor,
               Sa_Deliery.Assessorid,
               Sa_Deliery.AssessDate,
               Sa_Deliery.BillStateCode,
               Sa_Deliery.BillStateDate,
               Sa_Deliery.BillTaxAmount,
               Sa_Deliery.BillTaxTotal,
               Sa_Deliery.BillAmount,
               Sa_Deliery.BillReceived,
               Sa_Deliery.ItemCount,
               Sa_Deliery.PickCount,
               Sa_Deliery.FinishCount,
               Sa_Deliery.InvoCount,
               Sa_Deliery.DisannulCount,
               Sa_Deliery.PrintCount,
               Sa_Deliery.OaFlowMark,
               Sa_Deliery.FirstAmt,
               Sa_Deliery.LastAmt,
               Sa_Deliery.Custom1,
               Sa_Deliery.Custom2,
               Sa_Deliery.Custom3,
               Sa_Deliery.Custom4,
               Sa_Deliery.Custom5,
               Sa_Deliery.Custom6,
               Sa_Deliery.Custom7,
               Sa_Deliery.Custom8,
               Sa_Deliery.Custom9,
               Sa_Deliery.Custom10,
               Sa_Deliery.Tenantid,
               Sa_Deliery.TenantName,
               Sa_Deliery.Revision,
               Sa_Customer.CustName as GroupName
        from Sa_Deliery left join Sa_Customer on Sa_Customer.id = Sa_Deliery.Groupid
    </sql>
    <sql id="selectdetailVo">
        select
               Sa_Deliery.RefNo,
               Sa_Deliery.BillType,
               Sa_Deliery.BillTitle,
               Sa_Deliery.BillDate,
               Sa_Deliery.Groupid,
               Sa_Deliery.Operator,
               Sa_Deliery.CreateBy,
               Sa_Deliery.Lister,
               Sa_Deliery.Assessor,
               Sa_Customer.CustName as GroupName,
               Sa_DelieryItem.*
        from Sa_DelieryItem left join Sa_Deliery on Sa_Deliery.id = Sa_DelieryItem.Pid
                            left join Sa_Customer on Sa_Customer.id = Sa_Deliery.Groupid
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.crm.domain.pojo.SaDelieryitemdetailPojo">
        <include refid="selectdetailVo"/>
         where 1 = 1 
       <if test="filterstr != null ">
            ${filterstr}
        </if> 
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_Deliery.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.refno != null ">
   and Sa_Deliery.refno like concat('%', #{SearchPojo.refno}, '%')
</if>
<if test="SearchPojo.billtype != null ">
   and Sa_Deliery.billtype like concat('%', #{SearchPojo.billtype}, '%')
</if>
<if test="SearchPojo.billtitle != null ">
   and Sa_Deliery.billtitle like concat('%', #{SearchPojo.billtitle}, '%')
</if>
<if test="SearchPojo.groupid != null ">
   and Sa_Deliery.groupid like concat('%', #{SearchPojo.groupid}, '%')
</if>
<if test="SearchPojo.telephone != null ">
   and Sa_Deliery.telephone like concat('%', #{SearchPojo.telephone}, '%')
</if>
<if test="SearchPojo.linkman != null ">
   and Sa_Deliery.linkman like concat('%', #{SearchPojo.linkman}, '%')
</if>
<if test="SearchPojo.deliadd != null ">
   and Sa_Deliery.deliadd like concat('%', #{SearchPojo.deliadd}, '%')
</if>
<if test="SearchPojo.transport != null ">
   and Sa_Deliery.transport like concat('%', #{SearchPojo.transport}, '%')
</if>
<if test="SearchPojo.salesman != null ">
   and Sa_Deliery.salesman like concat('%', #{SearchPojo.salesman}, '%')
</if>
<if test="SearchPojo.salesmanid != null ">
   and Sa_Deliery.salesmanid like concat('%', #{SearchPojo.salesmanid}, '%')
</if>
<if test="SearchPojo.operator != null ">
   and Sa_Deliery.operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.operatorid != null ">
   and Sa_Deliery.operatorid like concat('%', #{SearchPojo.operatorid}, '%')
</if>
<if test="SearchPojo.summary != null ">
   and Sa_Deliery.summary like concat('%', #{SearchPojo.summary}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Sa_Deliery.createby like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Sa_Deliery.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Sa_Deliery.lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Sa_Deliery.listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   and Sa_Deliery.assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   and Sa_Deliery.assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.billstatecode != null ">
   and Sa_Deliery.billstatecode like concat('%', #{SearchPojo.billstatecode}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Sa_Deliery.custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Sa_Deliery.custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Sa_Deliery.custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Sa_Deliery.custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Sa_Deliery.custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and Sa_Deliery.custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and Sa_Deliery.custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and Sa_Deliery.custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   and Sa_Deliery.custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   and Sa_Deliery.custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Sa_Deliery.tenantname like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.refno != null ">
   or Sa_Deliery.RefNo like concat('%', #{SearchPojo.refno}, '%')
</if>
<if test="SearchPojo.billtype != null ">
   or Sa_Deliery.BillType like concat('%', #{SearchPojo.billtype}, '%')
</if>
<if test="SearchPojo.billtitle != null ">
   or Sa_Deliery.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
</if>
<if test="SearchPojo.groupid != null ">
   or Sa_Deliery.Groupid like concat('%', #{SearchPojo.groupid}, '%')
</if>
<if test="SearchPojo.telephone != null ">
   or Sa_Deliery.Telephone like concat('%', #{SearchPojo.telephone}, '%')
</if>
<if test="SearchPojo.linkman != null ">
   or Sa_Deliery.Linkman like concat('%', #{SearchPojo.linkman}, '%')
</if>
<if test="SearchPojo.deliadd != null ">
   or Sa_Deliery.DeliAdd like concat('%', #{SearchPojo.deliadd}, '%')
</if>
<if test="SearchPojo.transport != null ">
   or Sa_Deliery.TranSport like concat('%', #{SearchPojo.transport}, '%')
</if>
<if test="SearchPojo.salesman != null ">
   or Sa_Deliery.Salesman like concat('%', #{SearchPojo.salesman}, '%')
</if>
<if test="SearchPojo.salesmanid != null ">
   or Sa_Deliery.Salesmanid like concat('%', #{SearchPojo.salesmanid}, '%')
</if>
<if test="SearchPojo.operator != null ">
   or Sa_Deliery.Operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.operatorid != null ">
   or Sa_Deliery.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
</if>
<if test="SearchPojo.summary != null ">
   or Sa_Deliery.Summary like concat('%', #{SearchPojo.summary}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Sa_Deliery.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Sa_Deliery.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Sa_Deliery.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Sa_Deliery.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   or Sa_Deliery.Assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   or Sa_Deliery.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.billstatecode != null ">
   or Sa_Deliery.BillStateCode like concat('%', #{SearchPojo.billstatecode}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Sa_Deliery.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Sa_Deliery.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Sa_Deliery.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Sa_Deliery.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Sa_Deliery.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   or Sa_Deliery.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   or Sa_Deliery.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   or Sa_Deliery.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   or Sa_Deliery.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   or Sa_Deliery.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Sa_Deliery.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
     
         <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.crm.domain.pojo.SaDelieryPojo">
        <include refid="selectbillVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_Deliery.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="thand"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="thor"></include>        
         </if>
        </if> 
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="thand">
<if test="SearchPojo.refno != null ">
   and Sa_Deliery.RefNo like concat('%', #{SearchPojo.refno}, '%')
</if>
<if test="SearchPojo.billtype != null ">
   and Sa_Deliery.BillType like concat('%', #{SearchPojo.billtype}, '%')
</if>
<if test="SearchPojo.billtitle != null ">
   and Sa_Deliery.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
</if>
<if test="SearchPojo.groupid != null ">
   and Sa_Deliery.Groupid like concat('%', #{SearchPojo.groupid}, '%')
</if>
<if test="SearchPojo.telephone != null ">
   and Sa_Deliery.Telephone like concat('%', #{SearchPojo.telephone}, '%')
</if>
<if test="SearchPojo.linkman != null ">
   and Sa_Deliery.Linkman like concat('%', #{SearchPojo.linkman}, '%')
</if>
<if test="SearchPojo.deliadd != null ">
   and Sa_Deliery.DeliAdd like concat('%', #{SearchPojo.deliadd}, '%')
</if>
<if test="SearchPojo.transport != null ">
   and Sa_Deliery.TranSport like concat('%', #{SearchPojo.transport}, '%')
</if>
<if test="SearchPojo.salesman != null ">
   and Sa_Deliery.Salesman like concat('%', #{SearchPojo.salesman}, '%')
</if>
<if test="SearchPojo.salesmanid != null ">
   and Sa_Deliery.Salesmanid like concat('%', #{SearchPojo.salesmanid}, '%')
</if>
<if test="SearchPojo.operator != null ">
   and Sa_Deliery.Operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.operatorid != null ">
   and Sa_Deliery.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
</if>
<if test="SearchPojo.summary != null ">
   and Sa_Deliery.Summary like concat('%', #{SearchPojo.summary}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Sa_Deliery.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Sa_Deliery.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Sa_Deliery.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Sa_Deliery.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   and Sa_Deliery.Assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   and Sa_Deliery.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.billstatecode != null ">
   and Sa_Deliery.BillStateCode like concat('%', #{SearchPojo.billstatecode}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Sa_Deliery.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Sa_Deliery.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Sa_Deliery.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Sa_Deliery.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Sa_Deliery.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and Sa_Deliery.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and Sa_Deliery.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and Sa_Deliery.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   and Sa_Deliery.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   and Sa_Deliery.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Sa_Deliery.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="thor">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.refno != null ">
   or Sa_Deliery.RefNo like concat('%', #{SearchPojo.refno}, '%')
</if>
<if test="SearchPojo.billtype != null ">
   or Sa_Deliery.BillType like concat('%', #{SearchPojo.billtype}, '%')
</if>
<if test="SearchPojo.billtitle != null ">
   or Sa_Deliery.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
</if>
<if test="SearchPojo.groupid != null ">
   or Sa_Deliery.Groupid like concat('%', #{SearchPojo.groupid}, '%')
</if>
<if test="SearchPojo.telephone != null ">
   or Sa_Deliery.Telephone like concat('%', #{SearchPojo.telephone}, '%')
</if>
<if test="SearchPojo.linkman != null ">
   or Sa_Deliery.Linkman like concat('%', #{SearchPojo.linkman}, '%')
</if>
<if test="SearchPojo.deliadd != null ">
   or Sa_Deliery.DeliAdd like concat('%', #{SearchPojo.deliadd}, '%')
</if>
<if test="SearchPojo.transport != null ">
   or Sa_Deliery.TranSport like concat('%', #{SearchPojo.transport}, '%')
</if>
<if test="SearchPojo.salesman != null ">
   or Sa_Deliery.Salesman like concat('%', #{SearchPojo.salesman}, '%')
</if>
<if test="SearchPojo.salesmanid != null ">
   or Sa_Deliery.Salesmanid like concat('%', #{SearchPojo.salesmanid}, '%')
</if>
<if test="SearchPojo.operator != null ">
   or Sa_Deliery.Operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.operatorid != null ">
   or Sa_Deliery.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
</if>
<if test="SearchPojo.summary != null ">
   or Sa_Deliery.Summary like concat('%', #{SearchPojo.summary}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Sa_Deliery.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Sa_Deliery.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Sa_Deliery.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Sa_Deliery.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   or Sa_Deliery.Assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   or Sa_Deliery.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.billstatecode != null ">
   or Sa_Deliery.BillStateCode like concat('%', #{SearchPojo.billstatecode}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Sa_Deliery.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Sa_Deliery.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Sa_Deliery.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Sa_Deliery.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Sa_Deliery.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   or Sa_Deliery.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   or Sa_Deliery.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   or Sa_Deliery.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   or Sa_Deliery.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   or Sa_Deliery.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Sa_Deliery.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
     
    <!--新增所有列-->
    <insert id="insert" >
        insert into Sa_Deliery(id, RefNo, BillType, BillTitle, BillDate, Groupid, Telephone, Linkman, DeliAdd, Taxrate, TranSport, Salesman, Salesmanid, Operator, Operatorid, Summary, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Assessor, Assessorid, AssessDate, BillStateCode, BillStateDate, BillTaxAmount, BillTaxTotal, BillAmount, BillReceived, ItemCount, PickCount, FinishCount, InvoCount, DisannulCount, PrintCount, OaFlowMark, FirstAmt, LastAmt, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{refno}, #{billtype}, #{billtitle}, #{billdate}, #{groupid}, #{telephone}, #{linkman}, #{deliadd}, #{taxrate}, #{transport}, #{salesman}, #{salesmanid}, #{operator}, #{operatorid}, #{summary}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{assessor}, #{assessorid}, #{assessdate}, #{billstatecode}, #{billstatedate}, #{billtaxamount}, #{billtaxtotal}, #{billamount}, #{billreceived}, #{itemcount}, #{pickcount}, #{finishcount}, #{invocount}, #{disannulcount}, #{printcount}, #{oaflowmark}, #{firstamt}, #{lastamt}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_Deliery
        <set>
            <if test="refno != null ">
                RefNo =#{refno},
            </if>
            <if test="billtype != null ">
                BillType =#{billtype},
            </if>
            <if test="billtitle != null ">
                BillTitle =#{billtitle},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="groupid != null ">
                Groupid =#{groupid},
            </if>
            <if test="telephone != null ">
                Telephone =#{telephone},
            </if>
            <if test="linkman != null ">
                Linkman =#{linkman},
            </if>
            <if test="deliadd != null ">
                DeliAdd =#{deliadd},
            </if>
            <if test="taxrate != null">
                Taxrate =#{taxrate},
            </if>
            <if test="transport != null ">
                TranSport =#{transport},
            </if>
            <if test="salesman != null ">
                Salesman =#{salesman},
            </if>
            <if test="salesmanid != null ">
                Salesmanid =#{salesmanid},
            </if>
            <if test="operator != null ">
                Operator =#{operator},
            </if>
            <if test="operatorid != null ">
                Operatorid =#{operatorid},
            </if>
            <if test="summary != null ">
                Summary =#{summary},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="assessor != null ">
                Assessor =#{assessor},
            </if>
            <if test="assessorid != null ">
                Assessorid =#{assessorid},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="billstatecode != null ">
                BillStateCode =#{billstatecode},
            </if>
            <if test="billstatedate != null">
                BillStateDate =#{billstatedate},
            </if>
            <if test="billtaxamount != null">
                BillTaxAmount =#{billtaxamount},
            </if>
            <if test="billtaxtotal != null">
                BillTaxTotal =#{billtaxtotal},
            </if>
            <if test="billamount != null">
                BillAmount =#{billamount},
            </if>
            <if test="billreceived != null">
                BillReceived =#{billreceived},
            </if>
            <if test="itemcount != null">
                ItemCount =#{itemcount},
            </if>
            <if test="pickcount != null">
                PickCount =#{pickcount},
            </if>
            <if test="finishcount != null">
                FinishCount =#{finishcount},
            </if>
            <if test="invocount != null">
                InvoCount =#{invocount},
            </if>
            <if test="disannulcount != null">
                DisannulCount =#{disannulcount},
            </if>
            <if test="printcount != null">
                PrintCount =#{printcount},
            </if>
            <if test="oaflowmark != null">
                OaFlowMark =#{oaflowmark},
            </if>
            <if test="firstamt != null">
                FirstAmt =#{firstamt},
            </if>
            <if test="lastamt != null">
                LastAmt =#{lastamt},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_Deliery where id = #{key} 
    </delete>
                                                                                                <!--通过主键审核数据-->
    <update id="approval">
        update Sa_Deliery SET
            Assessor = #{assessor},
            Assessorid = #{assessorid},
            AssessDate = #{assessdate},
            Revision=Revision+1
        where id = #{id}
    </update>
                                                                                                                                <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String" parameterType="inks.service.sa.crm.domain.pojo.SaDelieryPojo">
        select
          id
        from Sa_DelieryItem
        where Pid = #{id}
        <if test="item !=null and item.size()>0">
         and id not in
        <foreach collection="item" open="(" close=")" separator="," item="item">
            <if test="item.id != null">
                #{item.id}
            </if>
            <if test="item.id == null">
                 ''
            </if>
        </foreach>
         </if>
    </select>

</mapper>

