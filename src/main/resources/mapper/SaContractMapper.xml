<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.crm.mapper.SaContractMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.crm.domain.pojo.SaContractPojo">
        <include refid="selectbillVo"/>
        where Sa_Contract.id = #{key} 
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        select Sa_Contract.id,
               Sa_Contract.GenGroupid,
               Sa_Contract.RefNo,
               Sa_Contract.BillType,
               Sa_Contract.BillTitle,
               Sa_Contract.BillDate,
               Sa_Contract.BillTaxAmount,
               Sa_Contract.Groupid,
               Sa_Contract.Businessid,
               Sa_Contract.CustContractor,
               Sa_Contract.CompContractor,
               Sa_Contract.Amount,
               Sa_Contract.ContractSignDate,
               Sa_Contract.RenewalMark,
               Sa_Contract.RenewalType,
               Sa_Contract.ContractExpireDate,
               Sa_Contract.CiteType,
               Sa_Contract.CiteUid,
               Sa_Contract.Citeid,
               Sa_Contract.Linkman,
               Sa_Contract.CustAdd,
               Sa_Contract.OrderTime,
               Sa_Contract.DeliveryDate,
               Sa_Contract.Charge,
               Sa_Contract.ChargeDept,
               Sa_Contract.Operatorid,
               Sa_Contract.Operator,
               Sa_Contract.EnabledMark,
               Sa_Contract.BillPaid,
               Sa_Contract.RowNum,
               Sa_Contract.Summary,
               Sa_Contract.CreateBy,
               Sa_Contract.CreateByid,
               Sa_Contract.CreateDate,
               Sa_Contract.Lister,
               Sa_Contract.Listerid,
               Sa_Contract.ModifyDate,
               Sa_Contract.Assessor,
               Sa_Contract.Assessorid,
               Sa_Contract.AssessDate,
               Sa_Contract.Principalid,
               Sa_Contract.Principal,
               Sa_Contract.LastFollowDate,
               Sa_Contract.Custom1,
               Sa_Contract.Custom2,
               Sa_Contract.Custom3,
               Sa_Contract.Custom4,
               Sa_Contract.Custom5,
               Sa_Contract.Custom6,
               Sa_Contract.Custom7,
               Sa_Contract.Custom8,
               Sa_Contract.Custom9,
               Sa_Contract.Custom10,
               Sa_Contract.Deptid,
               Sa_Contract.DeptName,
               Sa_Contract.Tenantid,
               Sa_Contract.TenantName,
               Sa_Contract.Revision,
               Sa_Customer.CustName,
               Sa_Customer.CustName AS GroupName
        from Sa_Contract
                 left join Sa_Customer on Sa_Contract.Groupid = Sa_Customer.id
    </sql>

    <sql id="selectdetailVo">
        select Sa_ContractItem.id,
               Sa_ContractItem.Pid,
               Sa_ContractItem.Goodsid,
               Sa_ContractItem.ItemCode,
               Sa_ContractItem.ItemName,
               Sa_ContractItem.ItemSpec,
               Sa_ContractItem.ItemUnit,
               Sa_ContractItem.Price,
               Sa_ContractItem.Quantity,
               Sa_ContractItem.ItemTaxrate,
               Sa_ContractItem.Amount,
               Sa_ContractItem.TaxTotal,
               Sa_ContractItem.TaxPrice,
               Sa_ContractItem.TaxAmount,
               Sa_ContractItem.Operator,
               Sa_ContractItem.Operatorid,
               Sa_ContractItem.ChanceItemid,
               Sa_ContractItem.RowNum,
               Sa_ContractItem.AttributeJson,
               Sa_ContractItem.AttributeStr,
               Sa_ContractItem.Remark,
               Sa_ContractItem.Custom1,
               Sa_ContractItem.Custom2,
               Sa_ContractItem.Custom3,
               Sa_ContractItem.Custom4,
               Sa_ContractItem.Custom5,
               Sa_ContractItem.Custom6,
               Sa_ContractItem.Custom7,
               Sa_ContractItem.Custom8,
               Sa_ContractItem.Custom9,
               Sa_ContractItem.Custom10,
               Sa_ContractItem.Tenantid,
               Sa_ContractItem.Revision,
               Sa_Contract.GenGroupid,
               Sa_Contract.RefNo,
               Sa_Contract.BillType,
               Sa_Contract.BillTitle,
               Sa_Contract.BillDate,
               Sa_Contract.BillTaxAmount,
               Sa_Contract.Groupid,
               Sa_Contract.Businessid,
               Sa_Contract.Revision,
               Sa_Customer.CustName,
               Sa_Customer.CustName AS GroupName,
               Sa_Business.BillTitle as BusinessTitle
        from Sa_ContractItem
                 left join Sa_Contract on Sa_ContractItem.Pid = Sa_Contract.id
                 left join Sa_Customer on Sa_Contract.Groupid = Sa_Customer.id
                 left join Sa_Business on Sa_Contract.Businessid = Sa_Business.id
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.crm.domain.pojo.SaContractitemdetailPojo">
        <include refid="selectdetailVo"/>
         where 1 = 1 
       <if test="filterstr != null ">
            ${filterstr}
        </if> 
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_Contract.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.gengroupid != null ">
   and Sa_Contract.gengroupid like concat('%', #{SearchPojo.gengroupid}, '%')
</if>
<if test="SearchPojo.refno != null ">
   and Sa_Contract.refno like concat('%', #{SearchPojo.refno}, '%')
</if>
<if test="SearchPojo.billtype != null ">
   and Sa_Contract.billtype like concat('%', #{SearchPojo.billtype}, '%')
</if>
<if test="SearchPojo.billtitle != null ">
   and Sa_Contract.billtitle like concat('%', #{SearchPojo.billtitle}, '%')
</if>
<if test="SearchPojo.groupid != null ">
   and Sa_Contract.groupid like concat('%', #{SearchPojo.groupid}, '%')
</if>
<if test="SearchPojo.businessid != null ">
   and Sa_Contract.businessid like concat('%', #{SearchPojo.businessid}, '%')
</if>
<if test="SearchPojo.custcontractor != null ">
   and Sa_Contract.custcontractor like concat('%', #{SearchPojo.custcontractor}, '%')
</if>
<if test="SearchPojo.compcontractor != null ">
   and Sa_Contract.compcontractor like concat('%', #{SearchPojo.compcontractor}, '%')
</if>
<if test="SearchPojo.citetype != null ">
   and Sa_Contract.citetype like concat('%', #{SearchPojo.citetype}, '%')
</if>
<if test="SearchPojo.citeuid != null ">
   and Sa_Contract.citeuid like concat('%', #{SearchPojo.citeuid}, '%')
</if>
<if test="SearchPojo.citeid != null ">
   and Sa_Contract.citeid like concat('%', #{SearchPojo.citeid}, '%')
</if>
<if test="SearchPojo.linkman != null ">
   and Sa_Contract.linkman like concat('%', #{SearchPojo.linkman}, '%')
</if>
<if test="SearchPojo.custadd != null ">
   and Sa_Contract.custadd like concat('%', #{SearchPojo.custadd}, '%')
</if>
<if test="SearchPojo.charge != null ">
   and Sa_Contract.charge like concat('%', #{SearchPojo.charge}, '%')
</if>
<if test="SearchPojo.chargedept != null ">
   and Sa_Contract.chargedept like concat('%', #{SearchPojo.chargedept}, '%')
</if>
<if test="SearchPojo.operatorid != null ">
   and Sa_Contract.operatorid like concat('%', #{SearchPojo.operatorid}, '%')
</if>
<if test="SearchPojo.operator != null ">
   and Sa_Contract.operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.summary != null ">
   and Sa_Contract.summary like concat('%', #{SearchPojo.summary}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Sa_Contract.createby like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Sa_Contract.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Sa_Contract.lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Sa_Contract.listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   and Sa_Contract.assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   and Sa_Contract.assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.principalid != null ">
   and Sa_Contract.principalid like concat('%', #{SearchPojo.principalid}, '%')
</if>
<if test="SearchPojo.principal != null ">
   and Sa_Contract.principal like concat('%', #{SearchPojo.principal}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Sa_Contract.custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Sa_Contract.custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Sa_Contract.custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Sa_Contract.custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Sa_Contract.custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and Sa_Contract.custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and Sa_Contract.custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and Sa_Contract.custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   and Sa_Contract.custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   and Sa_Contract.custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.deptid != null ">
   and Sa_Contract.deptid like concat('%', #{SearchPojo.deptid}, '%')
</if>
<if test="SearchPojo.deptname != null ">
   and Sa_Contract.deptname like concat('%', #{SearchPojo.deptname}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Sa_Contract.tenantname like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.gengroupid != null ">
   or Sa_Contract.GenGroupid like concat('%', #{SearchPojo.gengroupid}, '%')
</if>
<if test="SearchPojo.refno != null ">
   or Sa_Contract.RefNo like concat('%', #{SearchPojo.refno}, '%')
</if>
<if test="SearchPojo.billtype != null ">
   or Sa_Contract.BillType like concat('%', #{SearchPojo.billtype}, '%')
</if>
<if test="SearchPojo.billtitle != null ">
   or Sa_Contract.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
</if>
<if test="SearchPojo.groupid != null ">
   or Sa_Contract.Groupid like concat('%', #{SearchPojo.groupid}, '%')
</if>
<if test="SearchPojo.businessid != null ">
   or Sa_Contract.Businessid like concat('%', #{SearchPojo.businessid}, '%')
</if>
<if test="SearchPojo.custcontractor != null ">
   or Sa_Contract.CustContractor like concat('%', #{SearchPojo.custcontractor}, '%')
</if>
<if test="SearchPojo.compcontractor != null ">
   or Sa_Contract.CompContractor like concat('%', #{SearchPojo.compcontractor}, '%')
</if>
<if test="SearchPojo.citetype != null ">
   or Sa_Contract.CiteType like concat('%', #{SearchPojo.citetype}, '%')
</if>
<if test="SearchPojo.citeuid != null ">
   or Sa_Contract.CiteUid like concat('%', #{SearchPojo.citeuid}, '%')
</if>
<if test="SearchPojo.citeid != null ">
   or Sa_Contract.Citeid like concat('%', #{SearchPojo.citeid}, '%')
</if>
<if test="SearchPojo.linkman != null ">
   or Sa_Contract.Linkman like concat('%', #{SearchPojo.linkman}, '%')
</if>
<if test="SearchPojo.custadd != null ">
   or Sa_Contract.CustAdd like concat('%', #{SearchPojo.custadd}, '%')
</if>
<if test="SearchPojo.charge != null ">
   or Sa_Contract.Charge like concat('%', #{SearchPojo.charge}, '%')
</if>
<if test="SearchPojo.chargedept != null ">
   or Sa_Contract.ChargeDept like concat('%', #{SearchPojo.chargedept}, '%')
</if>
<if test="SearchPojo.operatorid != null ">
   or Sa_Contract.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
</if>
<if test="SearchPojo.operator != null ">
   or Sa_Contract.Operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.summary != null ">
   or Sa_Contract.Summary like concat('%', #{SearchPojo.summary}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Sa_Contract.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Sa_Contract.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Sa_Contract.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Sa_Contract.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   or Sa_Contract.Assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   or Sa_Contract.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.principalid != null ">
   or Sa_Contract.Principalid like concat('%', #{SearchPojo.principalid}, '%')
</if>
<if test="SearchPojo.principal != null ">
   or Sa_Contract.Principal like concat('%', #{SearchPojo.principal}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Sa_Contract.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Sa_Contract.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Sa_Contract.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Sa_Contract.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Sa_Contract.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   or Sa_Contract.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   or Sa_Contract.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   or Sa_Contract.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   or Sa_Contract.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   or Sa_Contract.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.deptid != null ">
   or Sa_Contract.Deptid like concat('%', #{SearchPojo.deptid}, '%')
</if>
<if test="SearchPojo.deptname != null ">
   or Sa_Contract.DeptName like concat('%', #{SearchPojo.deptname}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Sa_Contract.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
     
         <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.crm.domain.pojo.SaContractPojo">
        <include refid="selectbillVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_Contract.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="thand"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="thor"></include>        
         </if>
        </if> 
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="thand">
<if test="SearchPojo.gengroupid != null ">
   and Sa_Contract.GenGroupid like concat('%', #{SearchPojo.gengroupid}, '%')
</if>
<if test="SearchPojo.refno != null ">
   and Sa_Contract.RefNo like concat('%', #{SearchPojo.refno}, '%')
</if>
<if test="SearchPojo.billtype != null ">
   and Sa_Contract.BillType like concat('%', #{SearchPojo.billtype}, '%')
</if>
<if test="SearchPojo.billtitle != null ">
   and Sa_Contract.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
</if>
<if test="SearchPojo.groupid != null ">
   and Sa_Contract.Groupid like concat('%', #{SearchPojo.groupid}, '%')
</if>
<if test="SearchPojo.businessid != null ">
   and Sa_Contract.Businessid like concat('%', #{SearchPojo.businessid}, '%')
</if>
<if test="SearchPojo.custcontractor != null ">
   and Sa_Contract.CustContractor like concat('%', #{SearchPojo.custcontractor}, '%')
</if>
<if test="SearchPojo.compcontractor != null ">
   and Sa_Contract.CompContractor like concat('%', #{SearchPojo.compcontractor}, '%')
</if>
<if test="SearchPojo.citetype != null ">
   and Sa_Contract.CiteType like concat('%', #{SearchPojo.citetype}, '%')
</if>
<if test="SearchPojo.citeuid != null ">
   and Sa_Contract.CiteUid like concat('%', #{SearchPojo.citeuid}, '%')
</if>
<if test="SearchPojo.citeid != null ">
   and Sa_Contract.Citeid like concat('%', #{SearchPojo.citeid}, '%')
</if>
<if test="SearchPojo.linkman != null ">
   and Sa_Contract.Linkman like concat('%', #{SearchPojo.linkman}, '%')
</if>
<if test="SearchPojo.custadd != null ">
   and Sa_Contract.CustAdd like concat('%', #{SearchPojo.custadd}, '%')
</if>
<if test="SearchPojo.charge != null ">
   and Sa_Contract.Charge like concat('%', #{SearchPojo.charge}, '%')
</if>
<if test="SearchPojo.chargedept != null ">
   and Sa_Contract.ChargeDept like concat('%', #{SearchPojo.chargedept}, '%')
</if>
<if test="SearchPojo.operatorid != null ">
   and Sa_Contract.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
</if>
<if test="SearchPojo.operator != null ">
   and Sa_Contract.Operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.summary != null ">
   and Sa_Contract.Summary like concat('%', #{SearchPojo.summary}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Sa_Contract.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Sa_Contract.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Sa_Contract.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Sa_Contract.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   and Sa_Contract.Assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   and Sa_Contract.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.principalid != null ">
   and Sa_Contract.Principalid like concat('%', #{SearchPojo.principalid}, '%')
</if>
<if test="SearchPojo.principal != null ">
   and Sa_Contract.Principal like concat('%', #{SearchPojo.principal}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Sa_Contract.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Sa_Contract.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Sa_Contract.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Sa_Contract.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Sa_Contract.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and Sa_Contract.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and Sa_Contract.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and Sa_Contract.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   and Sa_Contract.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   and Sa_Contract.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.deptid != null ">
   and Sa_Contract.Deptid like concat('%', #{SearchPojo.deptid}, '%')
</if>
<if test="SearchPojo.deptname != null ">
   and Sa_Contract.DeptName like concat('%', #{SearchPojo.deptname}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Sa_Contract.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="thor">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.gengroupid != null ">
   or Sa_Contract.GenGroupid like concat('%', #{SearchPojo.gengroupid}, '%')
</if>
<if test="SearchPojo.refno != null ">
   or Sa_Contract.RefNo like concat('%', #{SearchPojo.refno}, '%')
</if>
<if test="SearchPojo.billtype != null ">
   or Sa_Contract.BillType like concat('%', #{SearchPojo.billtype}, '%')
</if>
<if test="SearchPojo.billtitle != null ">
   or Sa_Contract.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
</if>
<if test="SearchPojo.groupid != null ">
   or Sa_Contract.Groupid like concat('%', #{SearchPojo.groupid}, '%')
</if>
<if test="SearchPojo.businessid != null ">
   or Sa_Contract.Businessid like concat('%', #{SearchPojo.businessid}, '%')
</if>
<if test="SearchPojo.custcontractor != null ">
   or Sa_Contract.CustContractor like concat('%', #{SearchPojo.custcontractor}, '%')
</if>
<if test="SearchPojo.compcontractor != null ">
   or Sa_Contract.CompContractor like concat('%', #{SearchPojo.compcontractor}, '%')
</if>
<if test="SearchPojo.citetype != null ">
   or Sa_Contract.CiteType like concat('%', #{SearchPojo.citetype}, '%')
</if>
<if test="SearchPojo.citeuid != null ">
   or Sa_Contract.CiteUid like concat('%', #{SearchPojo.citeuid}, '%')
</if>
<if test="SearchPojo.citeid != null ">
   or Sa_Contract.Citeid like concat('%', #{SearchPojo.citeid}, '%')
</if>
<if test="SearchPojo.linkman != null ">
   or Sa_Contract.Linkman like concat('%', #{SearchPojo.linkman}, '%')
</if>
<if test="SearchPojo.custadd != null ">
   or Sa_Contract.CustAdd like concat('%', #{SearchPojo.custadd}, '%')
</if>
<if test="SearchPojo.charge != null ">
   or Sa_Contract.Charge like concat('%', #{SearchPojo.charge}, '%')
</if>
<if test="SearchPojo.chargedept != null ">
   or Sa_Contract.ChargeDept like concat('%', #{SearchPojo.chargedept}, '%')
</if>
<if test="SearchPojo.operatorid != null ">
   or Sa_Contract.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
</if>
<if test="SearchPojo.operator != null ">
   or Sa_Contract.Operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.summary != null ">
   or Sa_Contract.Summary like concat('%', #{SearchPojo.summary}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Sa_Contract.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Sa_Contract.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Sa_Contract.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Sa_Contract.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   or Sa_Contract.Assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   or Sa_Contract.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.principalid != null ">
   or Sa_Contract.Principalid like concat('%', #{SearchPojo.principalid}, '%')
</if>
<if test="SearchPojo.principal != null ">
   or Sa_Contract.Principal like concat('%', #{SearchPojo.principal}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Sa_Contract.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Sa_Contract.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Sa_Contract.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Sa_Contract.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Sa_Contract.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   or Sa_Contract.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   or Sa_Contract.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   or Sa_Contract.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   or Sa_Contract.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   or Sa_Contract.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.deptid != null ">
   or Sa_Contract.Deptid like concat('%', #{SearchPojo.deptid}, '%')
</if>
<if test="SearchPojo.deptname != null ">
   or Sa_Contract.DeptName like concat('%', #{SearchPojo.deptname}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Sa_Contract.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
     
    <!--新增所有列-->
    <insert id="insert" >
        insert into Sa_Contract(id, GenGroupid, RefNo, BillType, BillTitle, BillDate, BillTaxAmount, Groupid, Businessid, CustContractor, CompContractor, Amount, ContractSignDate, RenewalMark, RenewalType, ContractExpireDate, CiteType, CiteUid, Citeid, Linkman, CustAdd, OrderTime, DeliveryDate, Charge, ChargeDept, Operatorid, Operator, EnabledMark, BillPaid, RowNum, Summary, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Assessor, Assessorid, AssessDate, Principalid, Principal, LastFollowDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Deptid, DeptName, Tenantid, TenantName, Revision)
        values (#{id}, #{gengroupid}, #{refno}, #{billtype}, #{billtitle}, #{billdate}, #{billtaxamount}, #{groupid}, #{businessid}, #{custcontractor}, #{compcontractor}, #{amount}, #{contractsigndate}, #{renewalmark}, #{renewaltype}, #{contractexpiredate}, #{citetype}, #{citeuid}, #{citeid}, #{linkman}, #{custadd}, #{ordertime}, #{deliverydate}, #{charge}, #{chargedept}, #{operatorid}, #{operator}, #{enabledmark}, #{billpaid}, #{rownum}, #{summary}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{assessor}, #{assessorid}, #{assessdate}, #{principalid}, #{principal}, #{lastfollowdate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{deptid}, #{deptname}, #{tenantid}, #{tenantname}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_Contract
        <set>
            <if test="gengroupid != null ">
                GenGroupid =#{gengroupid},
            </if>
            <if test="refno != null ">
                RefNo =#{refno},
            </if>
            <if test="billtype != null ">
                BillType =#{billtype},
            </if>
            <if test="billtitle != null ">
                BillTitle =#{billtitle},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="billtaxamount != null">
                BillTaxAmount =#{billtaxamount},
            </if>
            <if test="groupid != null ">
                Groupid =#{groupid},
            </if>
            <if test="businessid != null ">
                Businessid =#{businessid},
            </if>
            <if test="custcontractor != null ">
                CustContractor =#{custcontractor},
            </if>
            <if test="compcontractor != null ">
                CompContractor =#{compcontractor},
            </if>
            <if test="amount != null">
                Amount =#{amount},
            </if>
            <if test="contractsigndate != null">
                ContractSignDate =#{contractsigndate},
            </if>
            <if test="renewalmark != null">
                RenewalMark =#{renewalmark},
            </if>
            <if test="renewaltype != null">
                RenewalType =#{renewaltype},
            </if>
            <if test="contractexpiredate != null">
                ContractExpireDate =#{contractexpiredate},
            </if>
            <if test="citetype != null ">
                CiteType =#{citetype},
            </if>
            <if test="citeuid != null ">
                CiteUid =#{citeuid},
            </if>
            <if test="citeid != null ">
                Citeid =#{citeid},
            </if>
            <if test="linkman != null ">
                Linkman =#{linkman},
            </if>
            <if test="custadd != null ">
                CustAdd =#{custadd},
            </if>
            <if test="ordertime != null">
                OrderTime =#{ordertime},
            </if>
            <if test="deliverydate != null">
                DeliveryDate =#{deliverydate},
            </if>
            <if test="charge != null ">
                Charge =#{charge},
            </if>
            <if test="chargedept != null ">
                ChargeDept =#{chargedept},
            </if>
            <if test="operatorid != null ">
                Operatorid =#{operatorid},
            </if>
            <if test="operator != null ">
                Operator =#{operator},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="billpaid != null">
                BillPaid =#{billpaid},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="summary != null ">
                Summary =#{summary},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="assessor != null ">
                Assessor =#{assessor},
            </if>
            <if test="assessorid != null ">
                Assessorid =#{assessorid},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="principalid != null ">
                Principalid =#{principalid},
            </if>
            <if test="principal != null ">
                Principal =#{principal},
            </if>
            <if test="lastfollowdate != null">
                LastFollowDate =#{lastfollowdate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="deptid != null ">
                Deptid =#{deptid},
            </if>
            <if test="deptname != null ">
                DeptName =#{deptname},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_Contract where id = #{key} 
    </delete>
                                                                                                                                                    <!--通过主键审核数据-->
    <update id="approval">
        update Sa_Contract SET
            Assessor = #{assessor},
            Assessorid = #{assessorid},
            AssessDate = #{assessdate},
            Revision=Revision+1
        where id = #{id}
    </update>
                                                                                        <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String" parameterType="inks.service.sa.crm.domain.pojo.SaContractPojo">
        select
          id
        from Sa_ContractItem
        where Pid = #{id}
        <if test="item !=null and item.size()>0">
         and id not in
        <foreach collection="item" open="(" close=")" separator="," item="item">
            <if test="item.id != null">
                #{item.id}
            </if>
            <if test="item.id == null">
                 ''
            </if>
        </foreach>
         </if>
    </select>

    <update id="syncBussinessContractAmount">
        update Sa_Business
        set ContractAmount = IFNULL((select sum(BillTaxAmount) from Sa_Contract where Businessid = #{id}),0)
        where id = #{id}
    </update>
</mapper>

