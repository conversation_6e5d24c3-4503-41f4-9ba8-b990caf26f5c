<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.crm.mapper.SaDmslicensekeyMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.crm.domain.pojo.SaDmslicensekeyPojo">
        select
          id, DmsUserid, LicenseKey, Software, SoftwarePeriod, MaxOnlineUser, MaxSupportHard, IsLimited, KeyExpiration, KeyQuantity, StateCode, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Tenantid, TenantName, Revision        from Sa_DmsLicenseKey
        where Sa_DmsLicenseKey.id = #{key}
    </select>
    <sql id="selectSaDmslicensekeyVo">
         select
          id, DmsUserid, <PERSON><PERSON><PERSON>, Software, SoftwarePeriod, MaxOnlineUser, MaxSupportHard, IsLimited, KeyExpiration, KeyQuantity, StateCode, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Tenantid, TenantName, Revision        from Sa_DmsLicenseKey
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.crm.domain.pojo.SaDmslicensekeyPojo">
        <include refid="selectSaDmslicensekeyVo"/>
         where 1 = 1
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_DmsLicenseKey.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">
           <include refid="and"></include>
         </if>
         <if test="SearchType==1">
           <include refid="or"></include>
         </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.dmsuserid != null ">
   and Sa_DmsLicenseKey.DmsUserid like concat('%', #{SearchPojo.dmsuserid}, '%')
</if>
<if test="SearchPojo.licensekey != null ">
   and Sa_DmsLicenseKey.LicenseKey like concat('%', #{SearchPojo.licensekey}, '%')
</if>
<if test="SearchPojo.software != null ">
   and Sa_DmsLicenseKey.Software like concat('%', #{SearchPojo.software}, '%')
</if>
<if test="SearchPojo.statecode != null ">
   and Sa_DmsLicenseKey.StateCode like concat('%', #{SearchPojo.statecode}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Sa_DmsLicenseKey.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Sa_DmsLicenseKey.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Sa_DmsLicenseKey.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Sa_DmsLicenseKey.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Sa_DmsLicenseKey.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Sa_DmsLicenseKey.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.dmsuserid != null ">
   or Sa_DmsLicenseKey.DmsUserid like concat('%', #{SearchPojo.dmsuserid}, '%')
</if>
<if test="SearchPojo.licensekey != null ">
   or Sa_DmsLicenseKey.LicenseKey like concat('%', #{SearchPojo.licensekey}, '%')
</if>
<if test="SearchPojo.software != null ">
   or Sa_DmsLicenseKey.Software like concat('%', #{SearchPojo.software}, '%')
</if>
<if test="SearchPojo.statecode != null ">
   or Sa_DmsLicenseKey.StateCode like concat('%', #{SearchPojo.statecode}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Sa_DmsLicenseKey.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Sa_DmsLicenseKey.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Sa_DmsLicenseKey.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Sa_DmsLicenseKey.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Sa_DmsLicenseKey.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Sa_DmsLicenseKey.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Sa_DmsLicenseKey(id, DmsUserid, LicenseKey, Software, SoftwarePeriod, MaxOnlineUser, MaxSupportHard, IsLimited, KeyExpiration, KeyQuantity, StateCode, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Tenantid, TenantName, Revision)
        values (#{id}, #{dmsuserid}, #{licensekey}, #{software}, #{softwareperiod}, #{maxonlineuser}, #{maxsupporthard}, #{islimited}, #{keyexpiration}, #{keyquantity}, #{statecode}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_DmsLicenseKey
        <set>
            <if test="dmsuserid != null ">
                DmsUserid =#{dmsuserid},
            </if>
            <if test="licensekey != null ">
                LicenseKey =#{licensekey},
            </if>
            <if test="software != null ">
                Software =#{software},
            </if>
            <if test="softwareperiod != null">
                SoftwarePeriod =#{softwareperiod},
            </if>
            <if test="maxonlineuser != null">
                MaxOnlineUser =#{maxonlineuser},
            </if>
            <if test="maxsupporthard != null">
                MaxSupportHard =#{maxsupporthard},
            </if>
            <if test="islimited != null">
                IsLimited =#{islimited},
            </if>
            <if test="keyexpiration != null">
                KeyExpiration =#{keyexpiration},
            </if>
            <if test="keyquantity != null">
                KeyQuantity =#{keyquantity},
            </if>
            <if test="statecode != null ">
                StateCode =#{statecode},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_DmsLicenseKey where id = #{key}
    </delete>


</mapper>

