<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.crm.mapper.SaSmpplanMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.crm.domain.pojo.SaSmpplanPojo">
        <include refid="selectbillVo"/>
        where Sa_SmpPlan.id = #{key} 
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        select Sa_SmpPlan.id,
               Sa_SmpPlan.RefNo,
               Sa_SmpPlan.BillType,
               Sa_SmpPlan.BillDate,
               Sa_SmpPlan.BillTitle,
               Sa_SmpPlan.Groupid,
               Sa_SmpPlan.Businessid,
               Sa_SmpPlan.Operator,
               Sa_SmpPlan.Operatorid,
               Sa_SmpPlan.Summary,
               Sa_SmpPlan.CreateBy,
               Sa_SmpPlan.CreateByid,
               Sa_SmpPlan.CreateDate,
               Sa_SmpPlan.Lister,
               Sa_SmpPlan.Listerid,
               Sa_SmpPlan.ModifyDate,
               Sa_SmpPlan.Assessor,
               Sa_SmpPlan.Assessorid,
               Sa_SmpPlan.AssessDate,
               Sa_SmpPlan.BillStateCode,
               Sa_SmpPlan.BillStateDate,
               Sa_SmpPlan.BillStartDate,
               Sa_SmpPlan.BillPlanDate,
               Sa_SmpPlan.ItemCount,
               Sa_SmpPlan.MrpCount,
               Sa_SmpPlan.StartCount,
               Sa_SmpPlan.DisannulCount,
               Sa_SmpPlan.FinishCount,
               Sa_SmpPlan.PrintCount,
               Sa_SmpPlan.OaFlowMark,
               Sa_SmpPlan.BillWkWpid,
               Sa_SmpPlan.BillWkWpCode,
               Sa_SmpPlan.BillWkWpName,
               Sa_SmpPlan.Custom1,
               Sa_SmpPlan.Custom2,
               Sa_SmpPlan.Custom3,
               Sa_SmpPlan.Custom4,
               Sa_SmpPlan.Custom5,
               Sa_SmpPlan.Custom6,
               Sa_SmpPlan.Custom7,
               Sa_SmpPlan.Custom8,
               Sa_SmpPlan.Custom9,
               Sa_SmpPlan.Custom10,
               Sa_SmpPlan.Deptid,
               Sa_SmpPlan.Tenantid,
               Sa_SmpPlan.TenantName,
               Sa_SmpPlan.Revision,
               Sa_Customer.CustName,
               Sa_Customer.CustName AS GroupName,
               Sa_Business.BillTitle as BusinessTitle
        from Sa_SmpPlan              Left join Sa_Customer on Sa_SmpPlan.Groupid = Sa_Customer.id
                                     Left join Sa_Business on Sa_SmpPlan.Businessid = Sa_Business.id
    </sql>
    <sql id="selectdetailVo">
        select
               Sa_SmpPlan.RefNo,
               Sa_SmpPlan.BillType,
               Sa_SmpPlan.BillDate,
               Sa_SmpPlan.BillTitle,
               Sa_SmpPlan.Groupid,
               Sa_SmpPlan.Businessid,
               Sa_Customer.CustName,
               Sa_Customer.CustName AS GroupName,
               Sa_Business.BillTitle as BusinessTitle,
               Sa_SmpPlanItem.*
        from Sa_SmpPlan right join Sa_SmpPlanItem on Sa_SmpPlan.id = Sa_SmpPlanItem.Pid
             Left join Sa_Customer on Sa_SmpPlan.Groupid = Sa_Customer.id
             Left join Sa_Business on Sa_SmpPlan.Businessid = Sa_Business.id
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.crm.domain.pojo.SaSmpplanitemdetailPojo">
        <include refid="selectdetailVo"/>
         where 1 = 1 
       <if test="filterstr != null ">
            ${filterstr}
        </if> 
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_SmpPlan.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.refno != null ">
   and Sa_SmpPlan.refno like concat('%', #{SearchPojo.refno}, '%')
</if>
<if test="SearchPojo.billtype != null ">
   and Sa_SmpPlan.billtype like concat('%', #{SearchPojo.billtype}, '%')
</if>
<if test="SearchPojo.billtitle != null ">
   and Sa_SmpPlan.billtitle like concat('%', #{SearchPojo.billtitle}, '%')
</if>
<if test="SearchPojo.groupid != null ">
   and Sa_SmpPlan.groupid like concat('%', #{SearchPojo.groupid}, '%')
</if>
<if test="SearchPojo.businessid != null ">
   and Sa_SmpPlan.businessid like concat('%', #{SearchPojo.businessid}, '%')
</if>
<if test="SearchPojo.operator != null ">
   and Sa_SmpPlan.operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.summary != null ">
   and Sa_SmpPlan.summary like concat('%', #{SearchPojo.summary}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Sa_SmpPlan.createby like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Sa_SmpPlan.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Sa_SmpPlan.lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Sa_SmpPlan.listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   and Sa_SmpPlan.assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   and Sa_SmpPlan.assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.billstatecode != null ">
   and Sa_SmpPlan.billstatecode like concat('%', #{SearchPojo.billstatecode}, '%')
</if>
<if test="SearchPojo.billwkwpid != null ">
   and Sa_SmpPlan.billwkwpid like concat('%', #{SearchPojo.billwkwpid}, '%')
</if>
<if test="SearchPojo.billwkwpcode != null ">
   and Sa_SmpPlan.billwkwpcode like concat('%', #{SearchPojo.billwkwpcode}, '%')
</if>
<if test="SearchPojo.billwkwpname != null ">
   and Sa_SmpPlan.billwkwpname like concat('%', #{SearchPojo.billwkwpname}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Sa_SmpPlan.custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Sa_SmpPlan.custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Sa_SmpPlan.custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Sa_SmpPlan.custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Sa_SmpPlan.custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and Sa_SmpPlan.custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and Sa_SmpPlan.custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and Sa_SmpPlan.custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   and Sa_SmpPlan.custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   and Sa_SmpPlan.custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Sa_SmpPlan.tenantname like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.refno != null ">
   or Sa_SmpPlan.RefNo like concat('%', #{SearchPojo.refno}, '%')
</if>
      <if test="SearchPojo.custname != null ">
          or Sa_Customer.CustName like concat('%', #{SearchPojo.custname}, '%')
      </if>
<if test="SearchPojo.billtype != null ">
   or Sa_SmpPlan.BillType like concat('%', #{SearchPojo.billtype}, '%')
</if>
<if test="SearchPojo.billtitle != null ">
   or Sa_SmpPlan.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
</if>
<if test="SearchPojo.groupid != null ">
   or Sa_SmpPlan.Groupid like concat('%', #{SearchPojo.groupid}, '%')
</if>
<if test="SearchPojo.businessid != null ">
   or Sa_SmpPlan.Businessid like concat('%', #{SearchPojo.businessid}, '%')
</if>
<if test="SearchPojo.operator != null ">
   or Sa_SmpPlan.Operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.summary != null ">
   or Sa_SmpPlan.Summary like concat('%', #{SearchPojo.summary}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Sa_SmpPlan.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Sa_SmpPlan.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Sa_SmpPlan.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Sa_SmpPlan.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   or Sa_SmpPlan.Assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   or Sa_SmpPlan.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.billstatecode != null ">
   or Sa_SmpPlan.BillStateCode like concat('%', #{SearchPojo.billstatecode}, '%')
</if>
<if test="SearchPojo.billwkwpid != null ">
   or Sa_SmpPlan.BillWkWpid like concat('%', #{SearchPojo.billwkwpid}, '%')
</if>
<if test="SearchPojo.billwkwpcode != null ">
   or Sa_SmpPlan.BillWkWpCode like concat('%', #{SearchPojo.billwkwpcode}, '%')
</if>
<if test="SearchPojo.billwkwpname != null ">
   or Sa_SmpPlan.BillWkWpName like concat('%', #{SearchPojo.billwkwpname}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Sa_SmpPlan.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Sa_SmpPlan.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Sa_SmpPlan.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Sa_SmpPlan.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Sa_SmpPlan.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   or Sa_SmpPlan.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   or Sa_SmpPlan.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   or Sa_SmpPlan.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   or Sa_SmpPlan.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   or Sa_SmpPlan.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Sa_SmpPlan.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
     
         <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.crm.domain.pojo.SaSmpplanPojo">
        <include refid="selectbillVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_SmpPlan.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="thand"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="thor"></include>        
         </if>
        </if> 
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="thand">
<if test="SearchPojo.refno != null ">
   and Sa_SmpPlan.RefNo like concat('%', #{SearchPojo.refno}, '%')
</if>
<if test="SearchPojo.billtype != null ">
   and Sa_SmpPlan.BillType like concat('%', #{SearchPojo.billtype}, '%')
</if>
<if test="SearchPojo.billtitle != null ">
   and Sa_SmpPlan.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
</if>
<if test="SearchPojo.groupid != null ">
   and Sa_SmpPlan.Groupid like concat('%', #{SearchPojo.groupid}, '%')
</if>
<if test="SearchPojo.businessid != null ">
   and Sa_SmpPlan.Businessid like concat('%', #{SearchPojo.businessid}, '%')
</if>
<if test="SearchPojo.operator != null ">
   and Sa_SmpPlan.Operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.summary != null ">
   and Sa_SmpPlan.Summary like concat('%', #{SearchPojo.summary}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Sa_SmpPlan.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Sa_SmpPlan.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Sa_SmpPlan.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Sa_SmpPlan.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   and Sa_SmpPlan.Assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   and Sa_SmpPlan.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.billstatecode != null ">
   and Sa_SmpPlan.BillStateCode like concat('%', #{SearchPojo.billstatecode}, '%')
</if>
<if test="SearchPojo.billwkwpid != null ">
   and Sa_SmpPlan.BillWkWpid like concat('%', #{SearchPojo.billwkwpid}, '%')
</if>
<if test="SearchPojo.billwkwpcode != null ">
   and Sa_SmpPlan.BillWkWpCode like concat('%', #{SearchPojo.billwkwpcode}, '%')
</if>
<if test="SearchPojo.billwkwpname != null ">
   and Sa_SmpPlan.BillWkWpName like concat('%', #{SearchPojo.billwkwpname}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Sa_SmpPlan.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Sa_SmpPlan.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Sa_SmpPlan.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Sa_SmpPlan.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Sa_SmpPlan.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and Sa_SmpPlan.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and Sa_SmpPlan.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and Sa_SmpPlan.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   and Sa_SmpPlan.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   and Sa_SmpPlan.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Sa_SmpPlan.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="thor">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.refno != null ">
   or Sa_SmpPlan.RefNo like concat('%', #{SearchPojo.refno}, '%')
</if>
<if test="SearchPojo.billtype != null ">
   or Sa_SmpPlan.BillType like concat('%', #{SearchPojo.billtype}, '%')
</if>
<if test="SearchPojo.billtitle != null ">
   or Sa_SmpPlan.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
</if>
<if test="SearchPojo.groupid != null ">
   or Sa_SmpPlan.Groupid like concat('%', #{SearchPojo.groupid}, '%')
</if>
<if test="SearchPojo.businessid != null ">
   or Sa_SmpPlan.Businessid like concat('%', #{SearchPojo.businessid}, '%')
</if>
<if test="SearchPojo.operator != null ">
   or Sa_SmpPlan.Operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.summary != null ">
   or Sa_SmpPlan.Summary like concat('%', #{SearchPojo.summary}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Sa_SmpPlan.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Sa_SmpPlan.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Sa_SmpPlan.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Sa_SmpPlan.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   or Sa_SmpPlan.Assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   or Sa_SmpPlan.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.billstatecode != null ">
   or Sa_SmpPlan.BillStateCode like concat('%', #{SearchPojo.billstatecode}, '%')
</if>
<if test="SearchPojo.billwkwpid != null ">
   or Sa_SmpPlan.BillWkWpid like concat('%', #{SearchPojo.billwkwpid}, '%')
</if>
<if test="SearchPojo.billwkwpcode != null ">
   or Sa_SmpPlan.BillWkWpCode like concat('%', #{SearchPojo.billwkwpcode}, '%')
</if>
<if test="SearchPojo.billwkwpname != null ">
   or Sa_SmpPlan.BillWkWpName like concat('%', #{SearchPojo.billwkwpname}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Sa_SmpPlan.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Sa_SmpPlan.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Sa_SmpPlan.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Sa_SmpPlan.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Sa_SmpPlan.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   or Sa_SmpPlan.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   or Sa_SmpPlan.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   or Sa_SmpPlan.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   or Sa_SmpPlan.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   or Sa_SmpPlan.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Sa_SmpPlan.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
     
    <!--新增所有列-->
    <insert id="insert" >
        insert into Sa_SmpPlan(id, RefNo, BillType, BillDate, BillTitle, Groupid, Businessid, Operator, Operatorid, Summary, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Assessor, Assessorid, AssessDate, BillStateCode, BillStateDate, BillStartDate, BillPlanDate, ItemCount, MrpCount, StartCount, DisannulCount, FinishCount, PrintCount, OaFlowMark, BillWkWpid, BillWkWpCode, BillWkWpName, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Deptid, Tenantid, TenantName, Revision)
        values (#{id}, #{refno}, #{billtype}, #{billdate}, #{billtitle}, #{groupid}, #{businessid}, #{operator}, #{operatorid}, #{summary}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{assessor}, #{assessorid}, #{assessdate}, #{billstatecode}, #{billstatedate}, #{billstartdate}, #{billplandate}, #{itemcount}, #{mrpcount}, #{startcount}, #{disannulcount}, #{finishcount}, #{printcount}, #{oaflowmark}, #{billwkwpid}, #{billwkwpcode}, #{billwkwpname}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{deptid}, #{tenantid}, #{tenantname}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_SmpPlan
        <set>
            <if test="refno != null ">
                RefNo =#{refno},
            </if>
            <if test="billtype != null ">
                BillType =#{billtype},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="billtitle != null ">
                BillTitle =#{billtitle},
            </if>
            <if test="groupid != null ">
                Groupid =#{groupid},
            </if>
            <if test="businessid != null ">
                Businessid =#{businessid},
            </if>
            <if test="operator != null ">
                Operator =#{operator},
            </if>
            <if test="operatorid != null ">
                Operatorid =#{operatorid},
            </if>
            <if test="summary != null ">
                Summary =#{summary},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="assessor != null ">
                Assessor =#{assessor},
            </if>
            <if test="assessorid != null ">
                Assessorid =#{assessorid},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="billstatecode != null ">
                BillStateCode =#{billstatecode},
            </if>
            <if test="billstatedate != null">
                BillStateDate =#{billstatedate},
            </if>
            <if test="billstartdate != null">
                BillStartDate =#{billstartdate},
            </if>
            <if test="billplandate != null">
                BillPlanDate =#{billplandate},
            </if>
            <if test="itemcount != null">
                ItemCount =#{itemcount},
            </if>
            <if test="mrpcount != null">
                MrpCount =#{mrpcount},
            </if>
            <if test="startcount != null">
                StartCount =#{startcount},
            </if>
            <if test="disannulcount != null">
                DisannulCount =#{disannulcount},
            </if>
            <if test="finishcount != null">
                FinishCount =#{finishcount},
            </if>
            <if test="printcount != null">
                PrintCount =#{printcount},
            </if>
            <if test="oaflowmark != null">
                OaFlowMark =#{oaflowmark},
            </if>
            <if test="billwkwpid != null ">
                BillWkWpid =#{billwkwpid},
            </if>
            <if test="billwkwpcode != null ">
                BillWkWpCode =#{billwkwpcode},
            </if>
            <if test="billwkwpname != null ">
                BillWkWpName =#{billwkwpname},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="deptid != null ">
                Deptid =#{deptid},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_SmpPlan where id = #{key} 
    </delete>
    <!--通过主键审核数据-->
    <update id="approval">
        update Sa_SmpPlan SET
            Assessor = #{assessor},
            Assessorid = #{assessorid},
            AssessDate = #{assessdate},
            Revision=Revision+1
        where id = #{id}
    </update>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String" parameterType="inks.service.sa.crm.domain.pojo.SaSmpplanPojo">
        select
          id
        from Sa_SmpPlanItem
        where Pid = #{id}
        <if test="item !=null and item.size()>0">
         and id not in
        <foreach collection="item" open="(" close=")" separator="," item="item">
            <if test="item.id != null">
                #{item.id}
            </if>
            <if test="item.id == null">
                 ''
            </if>
        </foreach>
         </if>
    </select>

    <update id="updateFinishCount">
        update Sa_SmpPlan
        SET FinishCount =COALESCE((SELECT COUNT(0)
        FROM Sa_SmpPlanItem
        where Sa_SmpPlanItem.Pid = #{pid}
        and (Sa_SmpPlanItem.Closed = 1
        or Sa_SmpPlanItem.FinishQty >= Sa_SmpPlanItem.Quantity)), 0)
        where id = #{pid}
    </update>

    <update id="updateOaflowmark">
        update Sa_SmpPlan
        SET OaFlowMark = #{oaflowmark}
        where id = #{id}
    </update>

<!--    // 同一个商机这是第几次转到样品需求 查询某个 Businessid（如一个商机 ID）在 当前时间之前 ，已经创建过多少条记录，然后 加 1 ，表示“这是第几次转到样品需求”。-->
    <select id="getBusToSmpTimes" resultType="int">
        select count(*)+1
        from Sa_SmpPlan
        where Businessid = #{businessid}
          and CreateDate <![CDATA[<]]> #{createdate}
    </select>
</mapper>

