<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.crm.mapper.SaReceiptMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.crm.domain.pojo.SaReceiptPojo">
        <include refid="selectSaReceiptVo"/>
        where Sa_Receipt.id = #{key}
    </select>
    <sql id="selectSaReceiptVo">
         select
          id, RefNo, BillType, BillTitle, BillDate, Planid, PlanUid, Orderid, OrderUid, Groupid, Businessid, CustName, Amount, Operatorid, Operator, RowNum, Remark, CreateBy, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>er, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, AssessDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, <PERSON>anti<PERSON>, TenantName, Revision        from Sa_Receipt
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.crm.domain.pojo.SaReceiptPojo">
        <include refid="selectSaReceiptVo"/>
         where 1 = 1
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_Receipt.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">
           <include refid="and"></include>
         </if>
         <if test="SearchType==1">
           <include refid="or"></include>
         </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.refno != null ">
   and Sa_Receipt.RefNo like concat('%', #{SearchPojo.refno}, '%')
</if>
<if test="SearchPojo.billtype != null ">
   and Sa_Receipt.BillType like concat('%', #{SearchPojo.billtype}, '%')
</if>
<if test="SearchPojo.billtitle != null ">
   and Sa_Receipt.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
</if>
<if test="SearchPojo.planid != null ">
   and Sa_Receipt.Planid like concat('%', #{SearchPojo.planid}, '%')
</if>
<if test="SearchPojo.planuid != null ">
   and Sa_Receipt.PlanUid like concat('%', #{SearchPojo.planuid}, '%')
</if>
<if test="SearchPojo.orderid != null ">
   and Sa_Receipt.Orderid like concat('%', #{SearchPojo.orderid}, '%')
</if>
<if test="SearchPojo.orderuid != null ">
   and Sa_Receipt.OrderUid like concat('%', #{SearchPojo.orderuid}, '%')
</if>
<if test="SearchPojo.groupid != null ">
   and Sa_Receipt.Groupid like concat('%', #{SearchPojo.groupid}, '%')
</if>
<if test="SearchPojo.custname != null ">
   and Sa_Receipt.CustName like concat('%', #{SearchPojo.custname}, '%')
</if>
<if test="SearchPojo.operatorid != null ">
   and Sa_Receipt.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
</if>
<if test="SearchPojo.operator != null ">
   and Sa_Receipt.Operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Sa_Receipt.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Sa_Receipt.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Sa_Receipt.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Sa_Receipt.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Sa_Receipt.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   and Sa_Receipt.Assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   and Sa_Receipt.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Sa_Receipt.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Sa_Receipt.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Sa_Receipt.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Sa_Receipt.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Sa_Receipt.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and Sa_Receipt.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and Sa_Receipt.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and Sa_Receipt.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   and Sa_Receipt.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   and Sa_Receipt.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Sa_Receipt.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.refno != null ">
   or Sa_Receipt.RefNo like concat('%', #{SearchPojo.refno}, '%')
</if>
<if test="SearchPojo.billtype != null ">
   or Sa_Receipt.BillType like concat('%', #{SearchPojo.billtype}, '%')
</if>
<if test="SearchPojo.billtitle != null ">
   or Sa_Receipt.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
</if>
<if test="SearchPojo.planid != null ">
   or Sa_Receipt.Planid like concat('%', #{SearchPojo.planid}, '%')
</if>
<if test="SearchPojo.planuid != null ">
   or Sa_Receipt.PlanUid like concat('%', #{SearchPojo.planuid}, '%')
</if>
<if test="SearchPojo.orderid != null ">
   or Sa_Receipt.Orderid like concat('%', #{SearchPojo.orderid}, '%')
</if>
<if test="SearchPojo.orderuid != null ">
   or Sa_Receipt.OrderUid like concat('%', #{SearchPojo.orderuid}, '%')
</if>
<if test="SearchPojo.groupid != null ">
   or Sa_Receipt.Groupid like concat('%', #{SearchPojo.groupid}, '%')
</if>
<if test="SearchPojo.custname != null ">
   or Sa_Receipt.CustName like concat('%', #{SearchPojo.custname}, '%')
</if>
<if test="SearchPojo.operatorid != null ">
   or Sa_Receipt.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
</if>
<if test="SearchPojo.operator != null ">
   or Sa_Receipt.Operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Sa_Receipt.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Sa_Receipt.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Sa_Receipt.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Sa_Receipt.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Sa_Receipt.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   or Sa_Receipt.Assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   or Sa_Receipt.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Sa_Receipt.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Sa_Receipt.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Sa_Receipt.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Sa_Receipt.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Sa_Receipt.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   or Sa_Receipt.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   or Sa_Receipt.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   or Sa_Receipt.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   or Sa_Receipt.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   or Sa_Receipt.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Sa_Receipt.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Sa_Receipt(id, RefNo, BillType, BillTitle, BillDate, Planid, PlanUid, Orderid, OrderUid, Groupid, Businessid, CustName, Amount, Operatorid, Operator, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Assessor, Assessorid, AssessDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{refno}, #{billtype}, #{billtitle}, #{billdate}, #{planid}, #{planuid}, #{orderid}, #{orderuid}, #{groupid}, #{businessid}, #{custname}, #{amount}, #{operatorid}, #{operator}, #{rownum}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{assessor}, #{assessorid}, #{assessdate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_Receipt
        <set>
            <if test="refno != null ">
                RefNo =#{refno},
            </if>
            <if test="billtype != null ">
                BillType =#{billtype},
            </if>
            <if test="billtitle != null ">
                BillTitle =#{billtitle},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="planid != null ">
                Planid =#{planid},
            </if>
            <if test="planuid != null ">
                PlanUid =#{planuid},
            </if>
            <if test="orderid != null ">
                Orderid =#{orderid},
            </if>
            <if test="orderuid != null ">
                OrderUid =#{orderuid},
            </if>
            <if test="groupid != null ">
                Groupid =#{groupid},
            </if>
            <if test="businessid != null ">
                Businessid =#{businessid},
            </if>
            <if test="custname != null ">
                CustName =#{custname},
            </if>
            <if test="amount != null">
                Amount =#{amount},
            </if>
            <if test="operatorid != null ">
                Operatorid =#{operatorid},
            </if>
            <if test="operator != null ">
                Operator =#{operator},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="assessor != null ">
                Assessor =#{assessor},
            </if>
            <if test="assessorid != null ">
                Assessorid =#{assessorid},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_Receipt where id = #{key}
    </delete>
                                                                                                <!--通过主键审核数据-->
    <update id="approval">
        update Sa_Receipt SET
            Assessor = #{assessor},
            Assessorid = #{assessorid},
            AssessDate = #{assessdate},
            Revision=Revision+1
        where id = #{id}
    </update>

    <update id="syncReceipPlanFinishAmt">
        update Sa_ReceiptPlan
        set FinishAmt = (SELECT COALESCE(sum(Sa_Receipt.Amount), 0) as Amt
                         FROM Sa_Receipt
                         where Sa_Receipt.Planid = #{planid})
        where id = #{planid}
    </update>
</mapper>

