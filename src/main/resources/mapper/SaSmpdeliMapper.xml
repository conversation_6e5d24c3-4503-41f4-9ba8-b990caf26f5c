<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.crm.mapper.SaSmpdeliMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.crm.domain.pojo.SaSmpdeliPojo">
        <include refid="selectbillVo"/>
        where Sa_SmpDeli.id = #{key} 
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        select Sa_SmpDeli.id,
               Sa_SmpDeli.RefNo,
               Sa_SmpDeli.BillType,
               Sa_SmpDeli.BillTitle,
               Sa_SmpDeli.BillDate,
               Sa_SmpDeli.Groupid,
               Sa_SmpDeli.Businessid,
               Sa_SmpDeli.Telephone,
               Sa_SmpDeli.<PERSON>man,
               Sa_SmpDeli.DeliAdd,
               Sa_SmpDeli.Taxrate,
               Sa_SmpDeli.TranSport,
               Sa_SmpDeli.Salesman,
               Sa_SmpDeli.Salesmanid,
               Sa_SmpDeli.Operator,
               Sa_SmpDeli.Operatorid,
               Sa_SmpDeli.Summary,
               Sa_SmpDeli.CreateBy,
               Sa_SmpDeli.CreateByid,
               Sa_SmpDeli.CreateDate,
               Sa_SmpDeli.Lister,
               Sa_SmpDeli.Listerid,
               Sa_SmpDeli.ModifyDate,
               Sa_SmpDeli.Assessor,
               Sa_SmpDeli.Assessorid,
               Sa_SmpDeli.AssessDate,
               Sa_SmpDeli.BillStateCode,
               Sa_SmpDeli.BillStateDate,
               Sa_SmpDeli.BillTaxAmount,
               Sa_SmpDeli.BillTaxTotal,
               Sa_SmpDeli.BillAmount,
               Sa_SmpDeli.BillReceived,
               Sa_SmpDeli.ItemCount,
               Sa_SmpDeli.FinishCount,
               Sa_SmpDeli.DisannulCount,
               Sa_SmpDeli.PrintCount,
               Sa_SmpDeli.Custom1,
               Sa_SmpDeli.Custom2,
               Sa_SmpDeli.Custom3,
               Sa_SmpDeli.Custom4,
               Sa_SmpDeli.Custom5,
               Sa_SmpDeli.Custom6,
               Sa_SmpDeli.Custom7,
               Sa_SmpDeli.Custom8,
               Sa_SmpDeli.Custom9,
               Sa_SmpDeli.Custom10,
               Sa_SmpDeli.Deptid,
               Sa_SmpDeli.Tenantid,
               Sa_SmpDeli.TenantName,
               Sa_SmpDeli.Revision,
               Sa_Customer.CustName,
               Sa_Customer.CustName AS GroupName,
               Sa_Business.BillTitle as BusinessTitle
        from Sa_SmpDeli
                 Left join Sa_Customer on Sa_SmpDeli.Groupid = Sa_Customer.id
                 Left join Sa_Business on Sa_SmpDeli.Businessid = Sa_Business.id
    </sql>
    <sql id="selectdetailVo">
        select Sa_SmpDeli.RefNo,
               Sa_SmpDeli.BillType,
               Sa_SmpDeli.BillTitle,
               Sa_SmpDeli.BillDate,
               Sa_SmpDeli.Groupid,
               Sa_SmpDeli.Businessid,
               Sa_Customer.CustName,
               Sa_Customer.CustName AS GroupName,
               Sa_Business.BillTitle as BusinessTitle,
               Sa_SmpDeliItem.*
        from Sa_SmpDeli
                 right join Sa_SmpDeliItem on Sa_SmpDeli.id = Sa_SmpDeliItem.Pid
                 Left join Sa_Customer on Sa_SmpDeli.Groupid = Sa_Customer.id
                 Left join Sa_Business on Sa_SmpDeli.Businessid = Sa_Business.id
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.crm.domain.pojo.SaSmpdeliitemdetailPojo">
        <include refid="selectdetailVo"/>
         where 1 = 1 
       <if test="filterstr != null ">
            ${filterstr}
        </if> 
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_SmpDeli.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.refno != null ">
   and Sa_SmpDeli.refno like concat('%', #{SearchPojo.refno}, '%')
</if>
<if test="SearchPojo.billtype != null ">
   and Sa_SmpDeli.billtype like concat('%', #{SearchPojo.billtype}, '%')
</if>
<if test="SearchPojo.billtitle != null ">
   and Sa_SmpDeli.billtitle like concat('%', #{SearchPojo.billtitle}, '%')
</if>
<if test="SearchPojo.groupid != null ">
   and Sa_SmpDeli.groupid like concat('%', #{SearchPojo.groupid}, '%')
</if>
<if test="SearchPojo.businessid != null ">
   and Sa_SmpDeli.businessid like concat('%', #{SearchPojo.businessid}, '%')
</if>
<if test="SearchPojo.telephone != null ">
   and Sa_SmpDeli.telephone like concat('%', #{SearchPojo.telephone}, '%')
</if>
<if test="SearchPojo.linkman != null ">
   and Sa_SmpDeli.linkman like concat('%', #{SearchPojo.linkman}, '%')
</if>
<if test="SearchPojo.deliadd != null ">
   and Sa_SmpDeli.deliadd like concat('%', #{SearchPojo.deliadd}, '%')
</if>
<if test="SearchPojo.transport != null ">
   and Sa_SmpDeli.transport like concat('%', #{SearchPojo.transport}, '%')
</if>
<if test="SearchPojo.salesman != null ">
   and Sa_SmpDeli.salesman like concat('%', #{SearchPojo.salesman}, '%')
</if>
<if test="SearchPojo.salesmanid != null ">
   and Sa_SmpDeli.salesmanid like concat('%', #{SearchPojo.salesmanid}, '%')
</if>
<if test="SearchPojo.operator != null ">
   and Sa_SmpDeli.operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.operatorid != null ">
   and Sa_SmpDeli.operatorid like concat('%', #{SearchPojo.operatorid}, '%')
</if>
<if test="SearchPojo.summary != null ">
   and Sa_SmpDeli.summary like concat('%', #{SearchPojo.summary}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Sa_SmpDeli.createby like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Sa_SmpDeli.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Sa_SmpDeli.lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Sa_SmpDeli.listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   and Sa_SmpDeli.assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   and Sa_SmpDeli.assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.billstatecode != null ">
   and Sa_SmpDeli.billstatecode like concat('%', #{SearchPojo.billstatecode}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Sa_SmpDeli.custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Sa_SmpDeli.custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Sa_SmpDeli.custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Sa_SmpDeli.custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Sa_SmpDeli.custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and Sa_SmpDeli.custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and Sa_SmpDeli.custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and Sa_SmpDeli.custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   and Sa_SmpDeli.custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   and Sa_SmpDeli.custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Sa_SmpDeli.tenantname like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.refno != null ">
   or Sa_SmpDeli.RefNo like concat('%', #{SearchPojo.refno}, '%')
</if>
      <if test="SearchPojo.custname != null ">
          or Sa_Customer.CustName like concat('%', #{SearchPojo.custname}, '%')
      </if>
<if test="SearchPojo.billtype != null ">
   or Sa_SmpDeli.BillType like concat('%', #{SearchPojo.billtype}, '%')
</if>
<if test="SearchPojo.billtitle != null ">
   or Sa_SmpDeli.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
</if>
<if test="SearchPojo.groupid != null ">
   or Sa_SmpDeli.Groupid like concat('%', #{SearchPojo.groupid}, '%')
</if>
<if test="SearchPojo.businessid != null ">
   or Sa_SmpDeli.Businessid like concat('%', #{SearchPojo.businessid}, '%')
</if>
<if test="SearchPojo.telephone != null ">
   or Sa_SmpDeli.Telephone like concat('%', #{SearchPojo.telephone}, '%')
</if>
<if test="SearchPojo.linkman != null ">
   or Sa_SmpDeli.Linkman like concat('%', #{SearchPojo.linkman}, '%')
</if>
<if test="SearchPojo.deliadd != null ">
   or Sa_SmpDeli.DeliAdd like concat('%', #{SearchPojo.deliadd}, '%')
</if>
<if test="SearchPojo.transport != null ">
   or Sa_SmpDeli.TranSport like concat('%', #{SearchPojo.transport}, '%')
</if>
<if test="SearchPojo.salesman != null ">
   or Sa_SmpDeli.Salesman like concat('%', #{SearchPojo.salesman}, '%')
</if>
<if test="SearchPojo.salesmanid != null ">
   or Sa_SmpDeli.Salesmanid like concat('%', #{SearchPojo.salesmanid}, '%')
</if>
<if test="SearchPojo.operator != null ">
   or Sa_SmpDeli.Operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.operatorid != null ">
   or Sa_SmpDeli.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
</if>
<if test="SearchPojo.summary != null ">
   or Sa_SmpDeli.Summary like concat('%', #{SearchPojo.summary}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Sa_SmpDeli.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Sa_SmpDeli.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Sa_SmpDeli.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Sa_SmpDeli.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   or Sa_SmpDeli.Assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   or Sa_SmpDeli.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.billstatecode != null ">
   or Sa_SmpDeli.BillStateCode like concat('%', #{SearchPojo.billstatecode}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Sa_SmpDeli.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Sa_SmpDeli.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Sa_SmpDeli.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Sa_SmpDeli.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Sa_SmpDeli.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   or Sa_SmpDeli.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   or Sa_SmpDeli.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   or Sa_SmpDeli.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   or Sa_SmpDeli.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   or Sa_SmpDeli.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Sa_SmpDeli.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
     
         <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.crm.domain.pojo.SaSmpdeliPojo">
        <include refid="selectbillVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_SmpDeli.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="thand"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="thor"></include>        
         </if>
        </if> 
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="thand">
<if test="SearchPojo.refno != null ">
   and Sa_SmpDeli.RefNo like concat('%', #{SearchPojo.refno}, '%')
</if>
<if test="SearchPojo.billtype != null ">
   and Sa_SmpDeli.BillType like concat('%', #{SearchPojo.billtype}, '%')
</if>
<if test="SearchPojo.billtitle != null ">
   and Sa_SmpDeli.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
</if>
<if test="SearchPojo.groupid != null ">
   and Sa_SmpDeli.Groupid like concat('%', #{SearchPojo.groupid}, '%')
</if>
<if test="SearchPojo.businessid != null ">
   and Sa_SmpDeli.Businessid like concat('%', #{SearchPojo.businessid}, '%')
</if>
<if test="SearchPojo.telephone != null ">
   and Sa_SmpDeli.Telephone like concat('%', #{SearchPojo.telephone}, '%')
</if>
<if test="SearchPojo.linkman != null ">
   and Sa_SmpDeli.Linkman like concat('%', #{SearchPojo.linkman}, '%')
</if>
<if test="SearchPojo.deliadd != null ">
   and Sa_SmpDeli.DeliAdd like concat('%', #{SearchPojo.deliadd}, '%')
</if>
<if test="SearchPojo.transport != null ">
   and Sa_SmpDeli.TranSport like concat('%', #{SearchPojo.transport}, '%')
</if>
<if test="SearchPojo.salesman != null ">
   and Sa_SmpDeli.Salesman like concat('%', #{SearchPojo.salesman}, '%')
</if>
<if test="SearchPojo.salesmanid != null ">
   and Sa_SmpDeli.Salesmanid like concat('%', #{SearchPojo.salesmanid}, '%')
</if>
<if test="SearchPojo.operator != null ">
   and Sa_SmpDeli.Operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.operatorid != null ">
   and Sa_SmpDeli.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
</if>
<if test="SearchPojo.summary != null ">
   and Sa_SmpDeli.Summary like concat('%', #{SearchPojo.summary}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Sa_SmpDeli.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Sa_SmpDeli.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Sa_SmpDeli.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Sa_SmpDeli.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   and Sa_SmpDeli.Assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   and Sa_SmpDeli.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.billstatecode != null ">
   and Sa_SmpDeli.BillStateCode like concat('%', #{SearchPojo.billstatecode}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Sa_SmpDeli.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Sa_SmpDeli.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Sa_SmpDeli.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Sa_SmpDeli.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Sa_SmpDeli.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and Sa_SmpDeli.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and Sa_SmpDeli.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and Sa_SmpDeli.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   and Sa_SmpDeli.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   and Sa_SmpDeli.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Sa_SmpDeli.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="thor">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.refno != null ">
   or Sa_SmpDeli.RefNo like concat('%', #{SearchPojo.refno}, '%')
</if>
<if test="SearchPojo.billtype != null ">
   or Sa_SmpDeli.BillType like concat('%', #{SearchPojo.billtype}, '%')
</if>
<if test="SearchPojo.billtitle != null ">
   or Sa_SmpDeli.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
</if>
<if test="SearchPojo.groupid != null ">
   or Sa_SmpDeli.Groupid like concat('%', #{SearchPojo.groupid}, '%')
</if>
<if test="SearchPojo.businessid != null ">
   or Sa_SmpDeli.Businessid like concat('%', #{SearchPojo.businessid}, '%')
</if>
<if test="SearchPojo.telephone != null ">
   or Sa_SmpDeli.Telephone like concat('%', #{SearchPojo.telephone}, '%')
</if>
<if test="SearchPojo.linkman != null ">
   or Sa_SmpDeli.Linkman like concat('%', #{SearchPojo.linkman}, '%')
</if>
<if test="SearchPojo.deliadd != null ">
   or Sa_SmpDeli.DeliAdd like concat('%', #{SearchPojo.deliadd}, '%')
</if>
<if test="SearchPojo.transport != null ">
   or Sa_SmpDeli.TranSport like concat('%', #{SearchPojo.transport}, '%')
</if>
<if test="SearchPojo.salesman != null ">
   or Sa_SmpDeli.Salesman like concat('%', #{SearchPojo.salesman}, '%')
</if>
<if test="SearchPojo.salesmanid != null ">
   or Sa_SmpDeli.Salesmanid like concat('%', #{SearchPojo.salesmanid}, '%')
</if>
<if test="SearchPojo.operator != null ">
   or Sa_SmpDeli.Operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.operatorid != null ">
   or Sa_SmpDeli.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
</if>
<if test="SearchPojo.summary != null ">
   or Sa_SmpDeli.Summary like concat('%', #{SearchPojo.summary}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Sa_SmpDeli.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Sa_SmpDeli.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Sa_SmpDeli.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Sa_SmpDeli.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   or Sa_SmpDeli.Assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   or Sa_SmpDeli.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.billstatecode != null ">
   or Sa_SmpDeli.BillStateCode like concat('%', #{SearchPojo.billstatecode}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Sa_SmpDeli.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Sa_SmpDeli.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Sa_SmpDeli.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Sa_SmpDeli.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Sa_SmpDeli.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   or Sa_SmpDeli.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   or Sa_SmpDeli.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   or Sa_SmpDeli.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   or Sa_SmpDeli.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   or Sa_SmpDeli.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Sa_SmpDeli.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
     
    <!--新增所有列-->
    <insert id="insert" >
        insert into Sa_SmpDeli(id, RefNo, BillType, BillTitle, BillDate, Groupid, Businessid, Telephone, Linkman, DeliAdd, Taxrate, TranSport, Salesman, Salesmanid, Operator, Operatorid, Summary, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Assessor, Assessorid, AssessDate, BillStateCode, BillStateDate, BillTaxAmount, BillTaxTotal, BillAmount, BillReceived, ItemCount, FinishCount, DisannulCount, PrintCount, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Deptid, Tenantid, TenantName, Revision)
        values (#{id}, #{refno}, #{billtype}, #{billtitle}, #{billdate}, #{groupid}, #{businessid}, #{telephone}, #{linkman}, #{deliadd}, #{taxrate}, #{transport}, #{salesman}, #{salesmanid}, #{operator}, #{operatorid}, #{summary}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{assessor}, #{assessorid}, #{assessdate}, #{billstatecode}, #{billstatedate}, #{billtaxamount}, #{billtaxtotal}, #{billamount}, #{billreceived}, #{itemcount}, #{finishcount}, #{disannulcount}, #{printcount}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{deptid}, #{tenantid}, #{tenantname}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_SmpDeli
        <set>
            <if test="refno != null ">
                RefNo =#{refno},
            </if>
            <if test="billtype != null ">
                BillType =#{billtype},
            </if>
            <if test="billtitle != null ">
                BillTitle =#{billtitle},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="groupid != null ">
                Groupid =#{groupid},
            </if>
            <if test="businessid != null ">
                Businessid =#{businessid},
            </if>
            <if test="telephone != null ">
                Telephone =#{telephone},
            </if>
            <if test="linkman != null ">
                Linkman =#{linkman},
            </if>
            <if test="deliadd != null ">
                DeliAdd =#{deliadd},
            </if>
            <if test="taxrate != null">
                Taxrate =#{taxrate},
            </if>
            <if test="transport != null ">
                TranSport =#{transport},
            </if>
            <if test="salesman != null ">
                Salesman =#{salesman},
            </if>
            <if test="salesmanid != null ">
                Salesmanid =#{salesmanid},
            </if>
            <if test="operator != null ">
                Operator =#{operator},
            </if>
            <if test="operatorid != null ">
                Operatorid =#{operatorid},
            </if>
            <if test="summary != null ">
                Summary =#{summary},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="assessor != null ">
                Assessor =#{assessor},
            </if>
            <if test="assessorid != null ">
                Assessorid =#{assessorid},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="billstatecode != null ">
                BillStateCode =#{billstatecode},
            </if>
            <if test="billstatedate != null">
                BillStateDate =#{billstatedate},
            </if>
            <if test="billtaxamount != null">
                BillTaxAmount =#{billtaxamount},
            </if>
            <if test="billtaxtotal != null">
                BillTaxTotal =#{billtaxtotal},
            </if>
            <if test="billamount != null">
                BillAmount =#{billamount},
            </if>
            <if test="billreceived != null">
                BillReceived =#{billreceived},
            </if>
            <if test="itemcount != null">
                ItemCount =#{itemcount},
            </if>
            <if test="finishcount != null">
                FinishCount =#{finishcount},
            </if>
            <if test="disannulcount != null">
                DisannulCount =#{disannulcount},
            </if>
            <if test="printcount != null">
                PrintCount =#{printcount},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="deptid != null ">
                Deptid =#{deptid},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_SmpDeli where id = #{key} 
    </delete>
    <!--通过主键审核数据-->
    <update id="approval">
        update Sa_SmpDeli SET
            Assessor = #{assessor},
            Assessorid = #{assessorid},
            AssessDate = #{assessdate},
            Revision=Revision+1
        where id = #{id}
    </update>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String" parameterType="inks.service.sa.crm.domain.pojo.SaSmpdeliPojo">
        select
          id
        from Sa_SmpDeliItem
        where Pid = #{id}
        <if test="item !=null and item.size()>0">
         and id not in
        <foreach collection="item" open="(" close=")" separator="," item="item">
            <if test="item.id != null">
                #{item.id}
            </if>
            <if test="item.id == null">
                 ''
            </if>
        </foreach>
         </if>
    </select>
    
    <update id="updateFinishCount">
        update Sa_SmpDeli
        SET FinishCount =COALESCE((SELECT COUNT(0)
        FROM Sa_SmpDeliItem
        where Sa_SmpDeliItem.Pid = #{pid}
        and (Sa_SmpDeliItem.FinishQty >= Sa_SmpDeliItem.Quantity or
        Sa_SmpDeliItem.Closed = 1 or
        Sa_SmpDeliItem.VirtualItem = 1)), 0)
        where id = #{pid}
    </update>
    
    <update id="updateDisannulCountAndAmount">
        update Sa_SmpDeli
        SET DisannulCount =COALESCE((SELECT COUNT(0)
        FROM Sa_SmpDeliItem
        where Sa_SmpDeliItem.Pid = #{key}
        and Sa_SmpDeliItem.DisannulMark = 1), 0),
        BillTaxAmount = COALESCE((SELECT Sum(TaxAmount)
        FROM Sa_SmpDeliItem
        where Sa_SmpDeliItem.Pid = #{key}
        and Sa_SmpDeliItem.DisannulMark = 0), 0),
        BillAmount    =COALESCE((SELECT Sum(Amount)
        FROM Sa_SmpDeliItem
        where Sa_SmpDeliItem.Pid = #{key}
        and Sa_SmpDeliItem.DisannulMark = 0), 0),
        BillTaxTotal=COALESCE((SELECT Sum(TaxTotal)
        FROM Sa_SmpDeliItem
        where Sa_SmpDeliItem.Pid = #{key}
        and Sa_SmpDeliItem.DisannulMark = 0), 0)
        where id = #{key}
    </update>

    <update id="syncPlanItemFinishQty">
        update Sa_SmpPlanItem
        SET FinishQty =COALESCE((SELECT sum(quantity)
                                 FROM Sa_SmpDeliItem
                                 where Sa_SmpDeliItem.citeitemid = #{citeitemid}
                                   and Sa_SmpDeliItem.DisannulMark = 0), 0)
        where id = #{key}
          and Tenantid = #{tid}
    </update>

<update id="syncPlanFinishCount">
    WITH PidCTE AS (SELECT Pid
                    FROM Sa_SmpDeliItem
                    WHERE id = #{citeitemid})
    UPDATE Sa_SmpPlan
    SET FinishCount = COALESCE(
            (SELECT COUNT(0)
             FROM Sa_SmpPlanItem
             WHERE Sa_SmpPlanItem.Pid = (SELECT Pid FROM PidCTE)
               AND (Sa_SmpPlanItem.Closed = 1
                 OR Sa_SmpPlanItem.FinishQty >= Sa_SmpPlanItem.Quantity)), 0)
    WHERE id = (SELECT Pid FROM PidCTE);
</update>

</mapper>

