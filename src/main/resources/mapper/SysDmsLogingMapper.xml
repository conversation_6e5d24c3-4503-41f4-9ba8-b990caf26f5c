<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.crm.mapper.SysDmsLogingMapper">
    <select id="login" resultType="inks.common.core.domain.LoginUser">
        SELECT Userid AS userid, UserName AS username, UserPassword AS password, RealName AS realName
        FROM Sa_DmsUser
        WHERE username = #{userName}
          and UserPassword = #{passWord} LIMIT 1
    </select>
    <select id="count" resultType="java.lang.Integer">
        SELECT count(1)
        FROM Sa_DmsUser
        WHERE username = #{userName}
    </select>
    <select id="getListByOpenid" resultType="inks.service.sa.crm.domain.pojo.SaDmsuserPojo">
        select *
        from Sa_DmsUser
        where Sa_DmsUser.Openid = #{openid}
    </select>

<!--    <select id="getListInTids" resultType="inks.service.sa.crm.domain.PiTenant">-->
<!--        SELECT * FROM PiTenant-->
<!--        WHERE PiTenant.Tenantid IN-->
<!--        <foreach item="tid" collection="tidList" open="(" separator="," close=")">-->
<!--            #{tid}-->
<!--        </foreach>-->
<!--    </select>-->
    <select id="getListByUserid" resultType="inks.service.sa.crm.domain.pojo.SaDmsuserPojo">
        select * from Sa_DmsUser where Userid = #{userid}
    </select>
    <select id="getDmsUserByUserid" resultType="inks.service.sa.crm.domain.pojo.SaDmsuserPojo">
        select * from Sa_DmsUser where Userid = #{userid} and Tenantid = #{tid}
    </select>


</mapper>
