<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.crm.mapper.SaFollowviewMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.crm.domain.pojo.SaFollowviewPojo">
        <include refid="selectSaFollowviewVo"/>
        where Sa_Followview.id = #{key}
    </select>
    <sql id="selectSaFollowviewVo">
         select
id, Citeid, CiteType, ItemType, ItemContent, IsAuto, CustName, Salesman, NextDate, EndDate, FinishDate, Finisher, FinishDesc, FinishMark, PhotoUrl1, PhotoUrl2, PhotoUrl3, PhotoName1, PhotoName2, PhotoName3, Operatorid, Operator, LabelJson, Collaboratorids, Collaborators, RowNum, Remark, Lister, Listerid, CreateBy, CreateByid, CreateDate, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision        from Sa_Followview
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.crm.domain.pojo.SaFollowviewPojo">
        <include refid="selectSaFollowviewVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_Followview.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.citeid != null">
            and Sa_Followview.Citeid like concat('%', #{SearchPojo.citeid}, '%')
        </if>
        <if test="SearchPojo.citetype != null">
            and Sa_Followview.CiteType like concat('%',
                #{SearchPojo.citetype}, '%')
        </if>
        <if test="SearchPojo.itemtype != null">
            and Sa_Followview.ItemType like concat('%',
                #{SearchPojo.itemtype}, '%')
        </if>
        <if test="SearchPojo.itemcontent != null">
            and Sa_Followview.ItemContent like concat('%',
                #{SearchPojo.itemcontent}, '%')
        </if>
        <if test="SearchPojo.custname != null">
            and Sa_Followview.CustName like concat('%',
                #{SearchPojo.custname}, '%')
        </if>
        <if test="SearchPojo.finishdesc != null">
            and Sa_Followview.FinishDesc like concat('%',
                #{SearchPojo.finishdesc}, '%')
        </if>
        <if test="SearchPojo.photourl1 != null">
            and Sa_Followview.PhotoUrl1 like concat('%',
                #{SearchPojo.photourl1}, '%')
        </if>
        <if test="SearchPojo.photourl2 != null">
            and Sa_Followview.PhotoUrl2 like concat('%',
                #{SearchPojo.photourl2}, '%')
        </if>
        <if test="SearchPojo.photourl3 != null">
            and Sa_Followview.PhotoUrl3 like concat('%',
                #{SearchPojo.photourl3}, '%')
        </if>
        <if test="SearchPojo.photoname1 != null">
            and Sa_Followview.PhotoName1 like concat('%',
                #{SearchPojo.photoname1}, '%')
        </if>
        <if test="SearchPojo.photoname2 != null">
            and Sa_Followview.PhotoName2 like concat('%',
                #{SearchPojo.photoname2}, '%')
        </if>
        <if test="SearchPojo.photoname3 != null">
            and Sa_Followview.PhotoName3 like concat('%',
                #{SearchPojo.photoname3}, '%')
        </if>
        <if test="SearchPojo.operatorid != null">
            and Sa_Followview.Operatorid like concat('%',
                #{SearchPojo.operatorid}, '%')
        </if>
        <if test="SearchPojo.operator != null">
            and Sa_Followview.Operator like concat('%',
                #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.labeljson != null">
            and Sa_Followview.LabelJson like concat('%',
                #{SearchPojo.labeljson}, '%')
        </if>
        <if test="SearchPojo.remark != null">
            and Sa_Followview.Remark like concat('%',
                #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Sa_Followview.Lister like concat('%',
                #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Sa_Followview.Listerid like concat('%',
                #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Sa_Followview.Custom1 like concat('%',
                #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Sa_Followview.Custom2 like concat('%',
                #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Sa_Followview.Custom3 like concat('%',
                #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Sa_Followview.Custom4 like concat('%',
                #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Sa_Followview.Custom5 like concat('%',
                #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null">
            and Sa_Followview.Custom6 like concat('%',
                #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null">
            and Sa_Followview.Custom7 like concat('%',
                #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null">
            and Sa_Followview.Custom8 like concat('%',
                #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null">
            and Sa_Followview.Custom9 like concat('%',
                #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null">
            and Sa_Followview.Custom10 like concat('%',
                #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null">
            and Sa_Followview.TenantName like concat('%',
                #{SearchPojo.tenantname}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Sa_Followview.CreateBy like concat('%',
                #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Sa_Followview.CreateByid like concat('%',
                #{SearchPojo.createbyid}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.citeid != null">
                or Sa_Followview.Citeid like concat('%', #{SearchPojo.citeid}, '%')
            </if>
            <if test="SearchPojo.citetype != null">
                or Sa_Followview.CiteType like concat('%',
                    #{SearchPojo.citetype}, '%')
            </if>
            <if test="SearchPojo.itemtype != null">
                or Sa_Followview.ItemType like concat('%',
                    #{SearchPojo.itemtype}, '%')
            </if>
            <if test="SearchPojo.itemcontent != null">
                or Sa_Followview.ItemContent like concat('%',
                    #{SearchPojo.itemcontent}, '%')
            </if>
            <if test="SearchPojo.custname != null">
                or Sa_Followview.CustName like concat('%',
                    #{SearchPojo.custname}, '%')
            </if>
            <if test="SearchPojo.finishdesc != null">
                or Sa_Followview.FinishDesc like concat('%',
                    #{SearchPojo.finishdesc}, '%')
            </if>
            <if test="SearchPojo.photourl1 != null">
                or Sa_Followview.PhotoUrl1 like concat('%',
                    #{SearchPojo.photourl1}, '%')
            </if>
            <if test="SearchPojo.photourl2 != null">
                or Sa_Followview.PhotoUrl2 like concat('%',
                    #{SearchPojo.photourl2}, '%')
            </if>
            <if test="SearchPojo.photourl3 != null">
                or Sa_Followview.PhotoUrl3 like concat('%',
                    #{SearchPojo.photourl3}, '%')
            </if>
            <if test="SearchPojo.photoname1 != null">
                or Sa_Followview.PhotoName1 like concat('%',
                    #{SearchPojo.photoname1}, '%')
            </if>
            <if test="SearchPojo.photoname2 != null">
                or Sa_Followview.PhotoName2 like concat('%',
                    #{SearchPojo.photoname2}, '%')
            </if>
            <if test="SearchPojo.photoname3 != null">
                or Sa_Followview.PhotoName3 like concat('%',
                    #{SearchPojo.photoname3}, '%')
            </if>
            <if test="SearchPojo.operatorid != null">
                or Sa_Followview.Operatorid like concat('%',
                    #{SearchPojo.operatorid}, '%')
            </if>
            <if test="SearchPojo.operator != null">
                or Sa_Followview.Operator like concat('%',
                    #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.labeljson != null">
                or Sa_Followview.LabelJson like concat('%',
                    #{SearchPojo.labeljson}, '%')
            </if>
            <if test="SearchPojo.remark != null">
                or Sa_Followview.Remark like concat('%',
                    #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Sa_Followview.Lister like concat('%',
                    #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Sa_Followview.Listerid like concat('%',
                    #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Sa_Followview.Custom1 like concat('%',
                    #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Sa_Followview.Custom2 like concat('%',
                    #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Sa_Followview.Custom3 like concat('%',
                    #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Sa_Followview.Custom4 like concat('%',
                    #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Sa_Followview.Custom5 like concat('%',
                    #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null">
                or Sa_Followview.Custom6 like concat('%',
                    #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null">
                or Sa_Followview.Custom7 like concat('%',
                    #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null">
                or Sa_Followview.Custom8 like concat('%',
                    #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null">
                or Sa_Followview.Custom9 like concat('%',
                    #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null">
                or Sa_Followview.Custom10 like concat('%',
                    #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null">
                or Sa_Followview.TenantName like concat('%',
                    #{SearchPojo.tenantname}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Sa_Followview.CreateBy like concat('%',
                    #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Sa_Followview.CreateByid like concat('%',
                    #{SearchPojo.createbyid}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into Sa_Followview(id, Citeid, CiteType, ItemType, ItemContent, IsAuto, CustName, Salesman, NextDate, EndDate, FinishDate, Finisher, FinishDesc, FinishMark, PhotoUrl1, PhotoUrl2, PhotoUrl3, PhotoName1, PhotoName2, PhotoName3, Operatorid, Operator, LabelJson, Collaboratorids, Collaborators, RowNum, Remark, Lister, Listerid, CreateBy, CreateByid, CreateDate, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{citeid}, #{citetype}, #{itemtype}, #{itemcontent}, #{isauto}, #{custname}, #{salesman}, #{nextdate}, #{enddate}, #{finishdate}, #{finisher}, #{finishdesc}, #{finishmark}, #{photourl1}, #{photourl2}, #{photourl3}, #{photoname1}, #{photoname2}, #{photoname3}, #{operatorid}, #{operator}, #{labeljson}, #{collaboratorids}, #{collaborators}, #{rownum}, #{remark}, #{lister}, #{listerid}, #{createby}, #{createbyid}, #{createdate}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_Followview
        <set>
            <if test="citeid != null">
                Citeid =#{citeid},
            </if>
            <if test="citetype != null">
                CiteType =#{citetype},
            </if>
            <if test="itemtype != null">
                ItemType =#{itemtype},
            </if>
            <if test="itemcontent != null">
                ItemContent =#{itemcontent},
            </if>
            <if test="isauto != null">
                IsAuto =#{isauto},
            </if>
            <if test="custname != null">
                CustName =#{custname},
            </if>
            <if test="salesman != null ">
                Salesman =#{salesman},
            </if>
            <if test="nextdate != null">
                NextDate =#{nextdate},
            </if>
            <if test="enddate != null">
                EndDate =#{enddate},
            </if>
            <if test="finishdate != null">
                FinishDate =#{finishdate},
            </if>
            <if test="finisher != null ">
                Finisher =#{finisher},
            </if>
            <if test="finishdesc != null">
                FinishDesc =#{finishdesc},
            </if>
            <if test="finishmark != null">
                FinishMark =#{finishmark},
            </if>
            <if test="photourl1 != null">
                PhotoUrl1 =#{photourl1},
            </if>
            <if test="photourl2 != null">
                PhotoUrl2 =#{photourl2},
            </if>
            <if test="photourl3 != null">
                PhotoUrl3 =#{photourl3},
            </if>
            <if test="photoname1 != null">
                PhotoName1 =#{photoname1},
            </if>
            <if test="photoname2 != null">
                PhotoName2 =#{photoname2},
            </if>
            <if test="photoname3 != null">
                PhotoName3 =#{photoname3},
            </if>
            <if test="operatorid != null">
                Operatorid =#{operatorid},
            </if>
            <if test="operator != null">
                Operator =#{operator},
            </if>
            <if test="labeljson != null ">
                LabelJson =#{labeljson},
            </if>
            <if test="collaboratorids != null ">
                Collaboratorids =#{collaboratorids},
            </if>
            <if test="collaborators != null ">
                Collaborators =#{collaborators},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="remark != null">
                Remark =#{remark},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null">
                TenantName =#{tenantname},
            </if>
            Revision=Revision + 1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Sa_Followview
        where id = #{key}
    </delete>


    <select id="getListByCiteIdAndIsAuto" resultType="inks.service.sa.crm.domain.pojo.SaFollowviewPojo">
        <include refid="selectSaFollowviewVo"/>
        WHERE 1 = 1
        <if test="citeid != null and citeid != ''">
            AND Sa_Followview.Citeid = #{citeid}
        </if>
        <choose>
            <when test="isauto == -1">
                AND Sa_Followview.IsAuto IN (0, 2)
            </when>
            <when test="isauto != null">
                AND Sa_Followview.IsAuto = #{isauto}
            </when>
        </choose>
        <if test="citetype != null and citetype != ''">
            AND Sa_Followview.CiteType = #{citetype}
        </if>
    </select>


    <select id="countByIsAuto" resultType="java.util.Map">
        SELECT IsAuto AS isAuto,
        CASE IsAuto
        WHEN 0 THEN '跟进记录'
        WHEN 1 THEN '系统记录'
        WHEN 2 THEN '跟进计划'
        ELSE '未知类型'
        END AS label,
        COUNT(1) AS count
        FROM Sa_Followview
        WHERE 1 = 1 ${filter}
        GROUP BY IsAuto
    </select>

    <!-- Mapper XML -->
    <select id="countByCiteType" resultType="java.util.Map">
        SELECT CiteType AS citeType,
        COUNT(1) AS count
        FROM Sa_Followview
        <where>
            <if test="citeid != null and citeid != ''">
                AND Citeid = #{citeid}
            </if>
            <if test="isauto != null">
                AND IsAuto = #{isauto}
            </if>
        </where>
        GROUP BY CiteType
    </select>

<!--    isauto传入-1时查询Sa_Followview.IsAuto IN (0, 2)-->
    <select id="getListByAll" resultType="inks.service.sa.crm.domain.pojo.SaFollowviewPojo">
        <include refid="selectSaFollowviewVo"/>
        where 1 = 1
        <if test="citeid != null and citeid != ''">
            and Sa_Followview.Citeid = #{citeid}
        </if>
        <if test="isauto != null">
            <choose>
                <when test="isauto == -1">
                    and Sa_Followview.IsAuto in (0, 2)
                </when>
                <otherwise>
                    and Sa_Followview.IsAuto = #{isauto}
                </otherwise>
            </choose>
        </if>
        <if test="citetype != null and citetype != ''">
            and Sa_Followview.CiteType = #{citetype}
        </if>
        <if test="itemtype != null and itemtype != ''">
            and Sa_Followview.ItemType = #{itemtype}
        </if>
        <if test="itemcontent != null and itemcontent != ''">
            and Sa_Followview.ItemContent = #{itemcontent}
        </if>
        <if test="custname != null and custname != ''">
            and Sa_Followview.CustName = #{custname}
        </if>
        <if test="finishdesc != null and finishdesc != ''">
            and Sa_Followview.FinishDesc = #{finishdesc}
        </if>
        <if test="photourl1 != null and photourl1 != ''">
            and Sa_Followview.PhotoUrl1 = #{photourl1}
        </if>
        <if test="photourl2 != null and photourl2 != ''">
            and Sa_Followview.PhotoUrl2 = #{photourl2}
        </if>
        <if test="photourl3 != null and photourl3 != ''">
            and Sa_Followview.PhotoUrl3 = #{photourl3}
        </if>
        <if test="photoname1 != null and photoname1 != ''">
            and Sa_Followview.PhotoName1 = #{photoname1}
        </if>
        <if test="photoname2 != null and photoname2 != ''">
            and Sa_Followview.PhotoName2 = #{photoname2}
        </if>
        <if test="photoname3 != null and photoname3 != ''">
            and Sa_Followview.PhotoName3 = #{photoname3}
        </if>
        <if test="operatorid != null and operatorid != ''">
            and Sa_Followview.Operatorid = #{operatorid}
        </if>
        <if test="operator != null and operator != ''">
            and Sa_Followview.Operator = #{operator}
        </if>
        <if test="labeljson != null and labeljson != ''">
            and Sa_Followview.LabelJson = #{labeljson}
        </if>
        <if test="rownum != null and rownum != ''">
            and Sa_Followview.RowNum = #{rownum}
        </if>
        <if test="remark != null and remark != ''">
            and Sa_Followview.Remark = #{remark}
        </if>
        order by Sa_Followview.CreateDate desc
    </select>

    <update id="unFinish">
        update Sa_Followview
        SET FinishDate =null,
            Finisher   ='',
            FinishDesc ='',
            FinishMark =0,
            Revision=Revision + 1
        where id = #{key}
    </update>
</mapper>

