<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.crm.mapper.SaDmsuserMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.crm.domain.pojo.SaDmsuserPojo">
        select
          Userid, Parentid, UserName, RealName, NickName, UserPassword, Openid, Mobile, Email, Sex, LangCode, Avatar, UserType, IsAdmin, Deptid, DeptCode, DeptName, IsDeptAdmin, DeptRowNum, RowNum, UserStatus, UserCode, Groupids, GroupNames, DmsFunctids, DmsFunctNames, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Tenantid, TenantName, Revision        from Sa_DmsUser
        where Sa_DmsUser.Userid = #{key}
    </select>
    <sql id="selectSaDmsuserVo">
         select
          Userid, Parentid, UserName, RealName, NickName, UserPassword, Openid, Mobile, Email, Sex, LangCode, Avatar, UserType, IsAdmin, Deptid, DeptCode, DeptName, IsDeptAdmin, DeptRowNum, RowNum, UserStatus, UserCode, Groupids, GroupNames, DmsFunctids, DmsFunctNames, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Tenantid, TenantName, Revision        from Sa_DmsUser
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.crm.domain.pojo.SaDmsuserPojo">
        <include refid="selectSaDmsuserVo"/>
         where 1 = 1
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_DmsUser.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">
           <include refid="and"></include>
         </if>
         <if test="SearchType==1">
           <include refid="or"></include>
         </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.parentid != null ">
   and Sa_DmsUser.Parentid like concat('%', #{SearchPojo.parentid}, '%')
</if>
<if test="SearchPojo.username != null ">
   and Sa_DmsUser.UserName like concat('%', #{SearchPojo.username}, '%')
</if>
<if test="SearchPojo.realname != null ">
   and Sa_DmsUser.RealName like concat('%', #{SearchPojo.realname}, '%')
</if>
<if test="SearchPojo.nickname != null ">
   and Sa_DmsUser.NickName like concat('%', #{SearchPojo.nickname}, '%')
</if>
<if test="SearchPojo.userpassword != null ">
   and Sa_DmsUser.UserPassword like concat('%', #{SearchPojo.userpassword}, '%')
</if>
<if test="SearchPojo.openid != null ">
   and Sa_DmsUser.Openid like concat('%', #{SearchPojo.openid}, '%')
</if>
<if test="SearchPojo.mobile != null ">
   and Sa_DmsUser.Mobile like concat('%', #{SearchPojo.mobile}, '%')
</if>
<if test="SearchPojo.email != null ">
   and Sa_DmsUser.Email like concat('%', #{SearchPojo.email}, '%')
</if>
<if test="SearchPojo.langcode != null ">
   and Sa_DmsUser.LangCode like concat('%', #{SearchPojo.langcode}, '%')
</if>
<if test="SearchPojo.avatar != null ">
   and Sa_DmsUser.Avatar like concat('%', #{SearchPojo.avatar}, '%')
</if>
<if test="SearchPojo.deptid != null ">
   and Sa_DmsUser.Deptid like concat('%', #{SearchPojo.deptid}, '%')
</if>
<if test="SearchPojo.deptcode != null ">
   and Sa_DmsUser.DeptCode like concat('%', #{SearchPojo.deptcode}, '%')
</if>
<if test="SearchPojo.deptname != null ">
   and Sa_DmsUser.DeptName like concat('%', #{SearchPojo.deptname}, '%')
</if>
<if test="SearchPojo.usercode != null ">
   and Sa_DmsUser.UserCode like concat('%', #{SearchPojo.usercode}, '%')
</if>
<if test="SearchPojo.groupids != null ">
   and Sa_DmsUser.Groupids like concat('%', #{SearchPojo.groupids}, '%')
</if>
<if test="SearchPojo.groupnames != null ">
   and Sa_DmsUser.GroupNames like concat('%', #{SearchPojo.groupnames}, '%')
</if>
<if test="SearchPojo.dmsfunctids != null ">
   and Sa_DmsUser.DmsFunctids like concat('%', #{SearchPojo.dmsfunctids}, '%')
</if>
<if test="SearchPojo.dmsfunctnames != null ">
   and Sa_DmsUser.DmsFunctNames like concat('%', #{SearchPojo.dmsfunctnames}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Sa_DmsUser.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Sa_DmsUser.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Sa_DmsUser.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Sa_DmsUser.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Sa_DmsUser.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Sa_DmsUser.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.parentid != null ">
   or Sa_DmsUser.Parentid like concat('%', #{SearchPojo.parentid}, '%')
</if>
<if test="SearchPojo.username != null ">
   or Sa_DmsUser.UserName like concat('%', #{SearchPojo.username}, '%')
</if>
<if test="SearchPojo.realname != null ">
   or Sa_DmsUser.RealName like concat('%', #{SearchPojo.realname}, '%')
</if>
<if test="SearchPojo.nickname != null ">
   or Sa_DmsUser.NickName like concat('%', #{SearchPojo.nickname}, '%')
</if>
<if test="SearchPojo.userpassword != null ">
   or Sa_DmsUser.UserPassword like concat('%', #{SearchPojo.userpassword}, '%')
</if>
<if test="SearchPojo.openid != null ">
   or Sa_DmsUser.Openid like concat('%', #{SearchPojo.openid}, '%')
</if>
<if test="SearchPojo.mobile != null ">
   or Sa_DmsUser.Mobile like concat('%', #{SearchPojo.mobile}, '%')
</if>
<if test="SearchPojo.email != null ">
   or Sa_DmsUser.Email like concat('%', #{SearchPojo.email}, '%')
</if>
<if test="SearchPojo.langcode != null ">
   or Sa_DmsUser.LangCode like concat('%', #{SearchPojo.langcode}, '%')
</if>
<if test="SearchPojo.avatar != null ">
   or Sa_DmsUser.Avatar like concat('%', #{SearchPojo.avatar}, '%')
</if>
<if test="SearchPojo.deptid != null ">
   or Sa_DmsUser.Deptid like concat('%', #{SearchPojo.deptid}, '%')
</if>
<if test="SearchPojo.deptcode != null ">
   or Sa_DmsUser.DeptCode like concat('%', #{SearchPojo.deptcode}, '%')
</if>
<if test="SearchPojo.deptname != null ">
   or Sa_DmsUser.DeptName like concat('%', #{SearchPojo.deptname}, '%')
</if>
<if test="SearchPojo.usercode != null ">
   or Sa_DmsUser.UserCode like concat('%', #{SearchPojo.usercode}, '%')
</if>
<if test="SearchPojo.groupids != null ">
   or Sa_DmsUser.Groupids like concat('%', #{SearchPojo.groupids}, '%')
</if>
<if test="SearchPojo.groupnames != null ">
   or Sa_DmsUser.GroupNames like concat('%', #{SearchPojo.groupnames}, '%')
</if>
<if test="SearchPojo.dmsfunctids != null ">
   or Sa_DmsUser.DmsFunctids like concat('%', #{SearchPojo.dmsfunctids}, '%')
</if>
<if test="SearchPojo.dmsfunctnames != null ">
   or Sa_DmsUser.DmsFunctNames like concat('%', #{SearchPojo.dmsfunctnames}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Sa_DmsUser.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Sa_DmsUser.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Sa_DmsUser.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Sa_DmsUser.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Sa_DmsUser.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Sa_DmsUser.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Sa_DmsUser(Userid, Parentid, UserName, RealName, NickName, UserPassword, Openid, Mobile, Email, Sex, LangCode, Avatar, UserType, IsAdmin, Deptid, DeptCode, DeptName, IsDeptAdmin, DeptRowNum, RowNum, UserStatus, UserCode, Groupids, GroupNames, DmsFunctids, DmsFunctNames, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Tenantid, TenantName, Revision)
        values (#{userid}, #{parentid}, #{username}, #{realname}, #{nickname}, #{userpassword}, #{openid}, #{mobile}, #{email}, #{sex}, #{langcode}, #{avatar}, #{usertype}, #{isadmin}, #{deptid}, #{deptcode}, #{deptname}, #{isdeptadmin}, #{deptrownum}, #{rownum}, #{userstatus}, #{usercode}, #{groupids}, #{groupnames}, #{dmsfunctids}, #{dmsfunctnames}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_DmsUser
        <set>
            <if test="parentid != null ">
                Parentid =#{parentid},
            </if>
            <if test="username != null ">
                UserName =#{username},
            </if>
            <if test="realname != null ">
                RealName =#{realname},
            </if>
            <if test="nickname != null ">
                NickName =#{nickname},
            </if>
            <if test="userpassword != null ">
                UserPassword =#{userpassword},
            </if>
            <if test="openid != null ">
                Openid =#{openid},
            </if>
            <if test="mobile != null ">
                Mobile =#{mobile},
            </if>
            <if test="email != null ">
                Email =#{email},
            </if>
            <if test="sex != null">
                Sex =#{sex},
            </if>
            <if test="langcode != null ">
                LangCode =#{langcode},
            </if>
            <if test="avatar != null ">
                Avatar =#{avatar},
            </if>
            <if test="usertype != null">
                UserType =#{usertype},
            </if>
            <if test="isadmin != null">
                IsAdmin =#{isadmin},
            </if>
            <if test="deptid != null ">
                Deptid =#{deptid},
            </if>
            <if test="deptcode != null ">
                DeptCode =#{deptcode},
            </if>
            <if test="deptname != null ">
                DeptName =#{deptname},
            </if>
            <if test="isdeptadmin != null">
                IsDeptAdmin =#{isdeptadmin},
            </if>
            <if test="deptrownum != null">
                DeptRowNum =#{deptrownum},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="userstatus != null">
                UserStatus =#{userstatus},
            </if>
            <if test="usercode != null ">
                UserCode =#{usercode},
            </if>
            <if test="groupids != null ">
                Groupids =#{groupids},
            </if>
            <if test="groupnames != null ">
                GroupNames =#{groupnames},
            </if>
            <if test="dmsfunctids != null ">
                DmsFunctids =#{dmsfunctids},
            </if>
            <if test="dmsfunctnames != null ">
                DmsFunctNames =#{dmsfunctnames},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where Userid = #{userid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_DmsUser where Userid = #{key}
    </delete>

    <select id="getEntityByUserName" resultType="inks.service.sa.crm.domain.pojo.SaDmsuserPojo">
        select * from Sa_DmsUser where Mobile = #{username} or Email = #{username}
    </select>
</mapper>

