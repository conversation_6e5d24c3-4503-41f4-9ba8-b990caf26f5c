<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.crm.mapper.SaCustomerMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.crm.domain.pojo.SaCustomerPojo">
        <include refid="selectSaCustomerVo"/>
        where Sa_Customer.id = #{key}
    </select>
    <select id="getEntityByCustuid" resultType="inks.service.sa.crm.domain.pojo.SaCustomerPojo">
        <include refid="selectSaCustomerVo"/>
        where Sa_Customer.CustUid = #{custuid}
    </select>
    <sql id="selectSaCustomerVo">
         select
id, CustUid, Userid, Deptid, Leadsid, GenGroupid, CustName, CustType, CustClass, Telephone, Linkman, Position, Mobile, Email, CustIndustry, CustSource, Longitude, Latitude, CustAdd, NextDate, Operatorid, Operator, DepartName, EnabledMark, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Principalid, Principal, LastFollowDate, ExtendCode, City, County, Street, LocalAdd, Region, ActionType, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision        from Sa_Customer
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.crm.domain.pojo.SaCustomerPojo">
        <include refid="selectSaCustomerVo"/>
         where 1 = 1
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_Customer.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">
           <include refid="and"></include>
         </if>
         <if test="SearchType==1">
           <include refid="or"></include>
         </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.custuid != null ">
   and Sa_Customer.CustUid like concat('%', #{SearchPojo.custuid}, '%')
</if>
<if test="SearchPojo.userid != null ">
   and Sa_Customer.Userid like concat('%', #{SearchPojo.userid}, '%')
</if>
<if test="SearchPojo.deptid != null ">
   and Sa_Customer.Deptid like concat('%', #{SearchPojo.deptid}, '%')
</if>
<if test="SearchPojo.leadsid != null ">
   and Sa_Customer.Leadsid like concat('%', #{SearchPojo.leadsid}, '%')
</if>
<if test="SearchPojo.gengroupid != null ">
   and Sa_Customer.GenGroupid like concat('%', #{SearchPojo.gengroupid}, '%')
</if>
<if test="SearchPojo.custname != null ">
   and Sa_Customer.CustName like concat('%', #{SearchPojo.custname}, '%')
</if>
<if test="SearchPojo.custtype != null ">
   and Sa_Customer.CustType like concat('%', #{SearchPojo.custtype}, '%')
</if>
<if test="SearchPojo.custclass != null ">
   and Sa_Customer.CustClass like concat('%', #{SearchPojo.custclass}, '%')
</if>
<if test="SearchPojo.telephone != null ">
   and Sa_Customer.Telephone like concat('%', #{SearchPojo.telephone}, '%')
</if>
<if test="SearchPojo.linkman != null ">
   and Sa_Customer.Linkman like concat('%', #{SearchPojo.linkman}, '%')
</if>
<if test="SearchPojo.position != null ">
   and Sa_Customer.Position like concat('%', #{SearchPojo.position}, '%')
</if>
<if test="SearchPojo.mobile != null ">
   and Sa_Customer.Mobile like concat('%', #{SearchPojo.mobile}, '%')
</if>
<if test="SearchPojo.email != null ">
   and Sa_Customer.Email like concat('%', #{SearchPojo.email}, '%')
</if>
<if test="SearchPojo.custindustry != null ">
   and Sa_Customer.CustIndustry like concat('%', #{SearchPojo.custindustry}, '%')
</if>
<if test="SearchPojo.custsource != null ">
   and Sa_Customer.CustSource like concat('%', #{SearchPojo.custsource}, '%')
</if>
<if test="SearchPojo.custadd != null ">
   and Sa_Customer.CustAdd like concat('%', #{SearchPojo.custadd}, '%')
</if>
<if test="SearchPojo.operatorid != null ">
   and Sa_Customer.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
</if>
<if test="SearchPojo.operator != null ">
   and Sa_Customer.Operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.departname != null ">
   and Sa_Customer.DepartName like concat('%', #{SearchPojo.departname}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Sa_Customer.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Sa_Customer.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Sa_Customer.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Sa_Customer.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Sa_Customer.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.principalid != null ">
   and Sa_Customer.Principalid like concat('%', #{SearchPojo.principalid}, '%')
</if>
<if test="SearchPojo.principal != null ">
   and Sa_Customer.Principal like concat('%', #{SearchPojo.principal}, '%')
</if>
<if test="SearchPojo.extendcode != null ">
   and Sa_Customer.ExtendCode like concat('%', #{SearchPojo.extendcode}, '%')
</if>
<if test="SearchPojo.city != null ">
   and Sa_Customer.City like concat('%', #{SearchPojo.city}, '%')
</if>
<if test="SearchPojo.county != null ">
   and Sa_Customer.County like concat('%', #{SearchPojo.county}, '%')
</if>
<if test="SearchPojo.street != null ">
   and Sa_Customer.Street like concat('%', #{SearchPojo.street}, '%')
</if>
<if test="SearchPojo.localadd != null ">
   and Sa_Customer.LocalAdd like concat('%', #{SearchPojo.localadd}, '%')
</if>
<if test="SearchPojo.region != null ">
   and Sa_Customer.Region like concat('%', #{SearchPojo.region}, '%')
</if>
<if test="SearchPojo.actiontype != null ">
   and Sa_Customer.ActionType like concat('%', #{SearchPojo.actiontype}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Sa_Customer.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Sa_Customer.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Sa_Customer.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Sa_Customer.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Sa_Customer.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and Sa_Customer.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and Sa_Customer.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and Sa_Customer.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   and Sa_Customer.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   and Sa_Customer.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Sa_Customer.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.custuid != null ">
   or Sa_Customer.CustUid like concat('%', #{SearchPojo.custuid}, '%')
</if>
<if test="SearchPojo.userid != null ">
   or Sa_Customer.Userid like concat('%', #{SearchPojo.userid}, '%')
</if>
<if test="SearchPojo.deptid != null ">
   or Sa_Customer.Deptid like concat('%', #{SearchPojo.deptid}, '%')
</if>
<if test="SearchPojo.leadsid != null ">
   or Sa_Customer.Leadsid like concat('%', #{SearchPojo.leadsid}, '%')
</if>
<if test="SearchPojo.gengroupid != null ">
   or Sa_Customer.GenGroupid like concat('%', #{SearchPojo.gengroupid}, '%')
</if>
<if test="SearchPojo.custname != null ">
   or Sa_Customer.CustName like concat('%', #{SearchPojo.custname}, '%')
</if>
<if test="SearchPojo.custtype != null ">
   or Sa_Customer.CustType like concat('%', #{SearchPojo.custtype}, '%')
</if>
<if test="SearchPojo.custclass != null ">
   or Sa_Customer.CustClass like concat('%', #{SearchPojo.custclass}, '%')
</if>
<if test="SearchPojo.telephone != null ">
   or Sa_Customer.Telephone like concat('%', #{SearchPojo.telephone}, '%')
</if>
<if test="SearchPojo.linkman != null ">
   or Sa_Customer.Linkman like concat('%', #{SearchPojo.linkman}, '%')
</if>
<if test="SearchPojo.position != null ">
   or Sa_Customer.Position like concat('%', #{SearchPojo.position}, '%')
</if>
<if test="SearchPojo.mobile != null ">
   or Sa_Customer.Mobile like concat('%', #{SearchPojo.mobile}, '%')
</if>
<if test="SearchPojo.email != null ">
   or Sa_Customer.Email like concat('%', #{SearchPojo.email}, '%')
</if>
<if test="SearchPojo.custindustry != null ">
   or Sa_Customer.CustIndustry like concat('%', #{SearchPojo.custindustry}, '%')
</if>
<if test="SearchPojo.custsource != null ">
   or Sa_Customer.CustSource like concat('%', #{SearchPojo.custsource}, '%')
</if>
<if test="SearchPojo.custadd != null ">
   or Sa_Customer.CustAdd like concat('%', #{SearchPojo.custadd}, '%')
</if>
<if test="SearchPojo.operatorid != null ">
   or Sa_Customer.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
</if>
<if test="SearchPojo.operator != null ">
   or Sa_Customer.Operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.departname != null ">
   or Sa_Customer.DepartName like concat('%', #{SearchPojo.departname}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Sa_Customer.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Sa_Customer.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Sa_Customer.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Sa_Customer.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Sa_Customer.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.principalid != null ">
   or Sa_Customer.Principalid like concat('%', #{SearchPojo.principalid}, '%')
</if>
<if test="SearchPojo.principal != null ">
   or Sa_Customer.Principal like concat('%', #{SearchPojo.principal}, '%')
</if>
<if test="SearchPojo.extendcode != null ">
   or Sa_Customer.ExtendCode like concat('%', #{SearchPojo.extendcode}, '%')
</if>
<if test="SearchPojo.city != null ">
   or Sa_Customer.City like concat('%', #{SearchPojo.city}, '%')
</if>
<if test="SearchPojo.county != null ">
   or Sa_Customer.County like concat('%', #{SearchPojo.county}, '%')
</if>
<if test="SearchPojo.street != null ">
   or Sa_Customer.Street like concat('%', #{SearchPojo.street}, '%')
</if>
<if test="SearchPojo.localadd != null ">
   or Sa_Customer.LocalAdd like concat('%', #{SearchPojo.localadd}, '%')
</if>
<if test="SearchPojo.region != null ">
   or Sa_Customer.Region like concat('%', #{SearchPojo.region}, '%')
</if>
<if test="SearchPojo.actiontype != null ">
   or Sa_Customer.ActionType like concat('%', #{SearchPojo.actiontype}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Sa_Customer.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Sa_Customer.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Sa_Customer.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Sa_Customer.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Sa_Customer.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   or Sa_Customer.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   or Sa_Customer.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   or Sa_Customer.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   or Sa_Customer.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   or Sa_Customer.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Sa_Customer.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Sa_Customer(id, CustUid, Userid, Deptid, Leadsid, GenGroupid, CustName, CustType, CustClass, Telephone, Linkman, Position, Mobile, Email, CustIndustry, CustSource, Longitude, Latitude, CustAdd, NextDate, Operatorid, Operator, DepartName, EnabledMark, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Principalid, Principal, LastFollowDate, ExtendCode, City, County, Street, LocalAdd, Region, ActionType, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{custuid}, #{userid}, #{deptid}, #{leadsid}, #{gengroupid}, #{custname}, #{custtype}, #{custclass}, #{telephone}, #{linkman}, #{position}, #{mobile}, #{email}, #{custindustry}, #{custsource}, #{longitude}, #{latitude}, #{custadd}, #{nextdate}, #{operatorid}, #{operator}, #{departname}, #{enabledmark}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{principalid}, #{principal}, #{lastfollowdate}, #{extendcode}, #{city}, #{county}, #{street}, #{localadd}, #{region}, #{actiontype}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_Customer
        <set>
            <if test="custuid != null ">
                CustUid =#{custuid},
            </if>
            <if test="userid != null ">
                Userid =#{userid},
            </if>
            <if test="deptid != null ">
                Deptid =#{deptid},
            </if>
            <if test="leadsid != null ">
                Leadsid =#{leadsid},
            </if>
            <if test="gengroupid != null ">
                GenGroupid =#{gengroupid},
            </if>
            <if test="custname != null ">
                CustName =#{custname},
            </if>
            <if test="custtype != null ">
                CustType =#{custtype},
            </if>
            <if test="custclass != null ">
                CustClass =#{custclass},
            </if>
            <if test="telephone != null ">
                Telephone =#{telephone},
            </if>
            <if test="linkman != null ">
                Linkman =#{linkman},
            </if>
            <if test="position != null ">
                Position =#{position},
            </if>
            <if test="mobile != null ">
                Mobile =#{mobile},
            </if>
            <if test="email != null ">
                Email =#{email},
            </if>
            <if test="custindustry != null ">
                CustIndustry =#{custindustry},
            </if>
            <if test="custsource != null ">
                CustSource =#{custsource},
            </if>
            <if test="longitude != null">
                Longitude =#{longitude},
            </if>
            <if test="latitude != null">
                Latitude =#{latitude},
            </if>
            <if test="custadd != null ">
                CustAdd =#{custadd},
            </if>
            <if test="nextdate != null">
                NextDate =#{nextdate},
            </if>
            <if test="operatorid != null ">
                Operatorid =#{operatorid},
            </if>
            <if test="operator != null ">
                Operator =#{operator},
            </if>
            <if test="departname != null ">
                DepartName =#{departname},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="principalid != null ">
                Principalid =#{principalid},
            </if>
            <if test="principal != null ">
                Principal =#{principal},
            </if>
            <if test="lastfollowdate != null">
                LastFollowDate =#{lastfollowdate},
            </if>
            <if test="extendcode != null ">
                ExtendCode =#{extendcode},
            </if>
            <if test="city != null ">
                City =#{city},
            </if>
            <if test="county != null ">
                County =#{county},
            </if>
            <if test="street != null ">
                Street =#{street},
            </if>
            <if test="localadd != null ">
                LocalAdd =#{localadd},
            </if>
            <if test="region != null ">
                Region =#{region},
            </if>
            <if test="actiontype != null ">
                ActionType =#{actiontype},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_Customer where id = #{key}
    </delete>
    <!-- 按编码查询实例-->
    <select id="getMaxCode" resultType="java.lang.String">
        SELECT Custuid
        FROM Sa_Customer
        ORDER BY LENGTH(Custuid) DESC, Custuid DESC
        LIMIT 1
    </select>

    <select id="getEntityByCustname" resultType="inks.service.sa.crm.domain.pojo.SaCustomerPojo">
        <include refid="selectSaCustomerVo"/>
        where CustName = #{custname}
    </select>

    <update id="syncNextDateByFollowview">
        update Sa_Customer
        set NextDate =(select MAX(NextDate) from Sa_Followview where Citeid = #{custid} and FinishMark = 0)
        where id = #{custid}
    </update>
</mapper>
