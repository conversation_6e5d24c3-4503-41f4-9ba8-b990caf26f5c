<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.crm.mapper.SaDelieryitemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.crm.domain.pojo.SaDelieryitemPojo">
        <include refid="selectSaDelieryitemVo"/>
        where Sa_DelieryItem.id = #{key} 
    </select>
    <sql id="selectSaDelieryitemVo">
         select
          id, Pid, Goodsid, ItemCode, ItemName, ItemSpec, ItemUnit, Quantity, TaxPrice, TaxAmount, Price, Amount, ItemTaxrate, TaxTotal, StdPrice, StdAmount, Rebate, FreeQty, PickQty, FinishQty, FinishClosed, RowNum, Remark, CiteUid, CiteItemid, CustPo, StateCode, StateDate, BusSQty, BusSClosed, MachType, InvoQty, InvoClosed, ReturnQty, ReturnMatQty, ReturnClosed, Salescost, VirtualItem, Location, BatchNo, MachUid, MachItemid, DisannulMark, DisannulListerid, DisannulLister, DisannulDate, BFItemid, AttributeJson, AttributeStr, MachDate, CostItemJson, CostGroupJson, SourceType, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Custom11, Custom12, Custom13, Custom14, Custom15, Custom16, Custom17, Custom18, Tenantid, Revision        from Sa_DelieryItem
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.crm.domain.pojo.SaDelieryitemPojo">
        <include refid="selectSaDelieryitemVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
              <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_DelieryItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   and Sa_DelieryItem.pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
   and Sa_DelieryItem.goodsid like concat('%', #{SearchPojo.goodsid}, '%')
</if>
<if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
   and Sa_DelieryItem.itemcode like concat('%', #{SearchPojo.itemcode}, '%')
</if>
<if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
   and Sa_DelieryItem.itemname like concat('%', #{SearchPojo.itemname}, '%')
</if>
<if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
   and Sa_DelieryItem.itemspec like concat('%', #{SearchPojo.itemspec}, '%')
</if>
<if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
   and Sa_DelieryItem.itemunit like concat('%', #{SearchPojo.itemunit}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   and Sa_DelieryItem.remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.citeuid != null and SearchPojo.citeuid != ''">
   and Sa_DelieryItem.citeuid like concat('%', #{SearchPojo.citeuid}, '%')
</if>
<if test="SearchPojo.citeitemid != null and SearchPojo.citeitemid != ''">
   and Sa_DelieryItem.citeitemid like concat('%', #{SearchPojo.citeitemid}, '%')
</if>
<if test="SearchPojo.custpo != null and SearchPojo.custpo != ''">
   and Sa_DelieryItem.custpo like concat('%', #{SearchPojo.custpo}, '%')
</if>
<if test="SearchPojo.statecode != null and SearchPojo.statecode != ''">
   and Sa_DelieryItem.statecode like concat('%', #{SearchPojo.statecode}, '%')
</if>
<if test="SearchPojo.machtype != null and SearchPojo.machtype != ''">
   and Sa_DelieryItem.machtype like concat('%', #{SearchPojo.machtype}, '%')
</if>
<if test="SearchPojo.location != null and SearchPojo.location != ''">
   and Sa_DelieryItem.location like concat('%', #{SearchPojo.location}, '%')
</if>
<if test="SearchPojo.batchno != null and SearchPojo.batchno != ''">
   and Sa_DelieryItem.batchno like concat('%', #{SearchPojo.batchno}, '%')
</if>
<if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
   and Sa_DelieryItem.machuid like concat('%', #{SearchPojo.machuid}, '%')
</if>
<if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
   and Sa_DelieryItem.machitemid like concat('%', #{SearchPojo.machitemid}, '%')
</if>
<if test="SearchPojo.disannullisterid != null and SearchPojo.disannullisterid != ''">
   and Sa_DelieryItem.disannullisterid like concat('%', #{SearchPojo.disannullisterid}, '%')
</if>
<if test="SearchPojo.disannullister != null and SearchPojo.disannullister != ''">
   and Sa_DelieryItem.disannullister like concat('%', #{SearchPojo.disannullister}, '%')
</if>
<if test="SearchPojo.attributejson != null and SearchPojo.attributejson != ''">
   and Sa_DelieryItem.attributejson like concat('%', #{SearchPojo.attributejson}, '%')
</if>
<if test="SearchPojo.attributestr != null and SearchPojo.attributestr != ''">
   and Sa_DelieryItem.attributestr like concat('%', #{SearchPojo.attributestr}, '%')
</if>
<if test="SearchPojo.costitemjson != null and SearchPojo.costitemjson != ''">
   and Sa_DelieryItem.costitemjson like concat('%', #{SearchPojo.costitemjson}, '%')
</if>
<if test="SearchPojo.costgroupjson != null and SearchPojo.costgroupjson != ''">
   and Sa_DelieryItem.costgroupjson like concat('%', #{SearchPojo.costgroupjson}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   and Sa_DelieryItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   and Sa_DelieryItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   and Sa_DelieryItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   and Sa_DelieryItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   and Sa_DelieryItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
   and Sa_DelieryItem.custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
   and Sa_DelieryItem.custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
   and Sa_DelieryItem.custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
   and Sa_DelieryItem.custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
   and Sa_DelieryItem.custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.custom11 != null and SearchPojo.custom11 != ''">
   and Sa_DelieryItem.custom11 like concat('%', #{SearchPojo.custom11}, '%')
</if>
<if test="SearchPojo.custom12 != null and SearchPojo.custom12 != ''">
   and Sa_DelieryItem.custom12 like concat('%', #{SearchPojo.custom12}, '%')
</if>
<if test="SearchPojo.custom13 != null and SearchPojo.custom13 != ''">
   and Sa_DelieryItem.custom13 like concat('%', #{SearchPojo.custom13}, '%')
</if>
<if test="SearchPojo.custom14 != null and SearchPojo.custom14 != ''">
   and Sa_DelieryItem.custom14 like concat('%', #{SearchPojo.custom14}, '%')
</if>
<if test="SearchPojo.custom15 != null and SearchPojo.custom15 != ''">
   and Sa_DelieryItem.custom15 like concat('%', #{SearchPojo.custom15}, '%')
</if>
<if test="SearchPojo.custom16 != null and SearchPojo.custom16 != ''">
   and Sa_DelieryItem.custom16 like concat('%', #{SearchPojo.custom16}, '%')
</if>
<if test="SearchPojo.custom17 != null and SearchPojo.custom17 != ''">
   and Sa_DelieryItem.custom17 like concat('%', #{SearchPojo.custom17}, '%')
</if>
<if test="SearchPojo.custom18 != null and SearchPojo.custom18 != ''">
   and Sa_DelieryItem.custom18 like concat('%', #{SearchPojo.custom18}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   or Sa_DelieryItem.Pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
   or Sa_DelieryItem.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
</if>
<if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
   or Sa_DelieryItem.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
</if>
<if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
   or Sa_DelieryItem.ItemName like concat('%', #{SearchPojo.itemname}, '%')
</if>
<if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
   or Sa_DelieryItem.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
</if>
<if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
   or Sa_DelieryItem.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   or Sa_DelieryItem.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.citeuid != null and SearchPojo.citeuid != ''">
   or Sa_DelieryItem.CiteUid like concat('%', #{SearchPojo.citeuid}, '%')
</if>
<if test="SearchPojo.citeitemid != null and SearchPojo.citeitemid != ''">
   or Sa_DelieryItem.CiteItemid like concat('%', #{SearchPojo.citeitemid}, '%')
</if>
<if test="SearchPojo.custpo != null and SearchPojo.custpo != ''">
   or Sa_DelieryItem.CustPo like concat('%', #{SearchPojo.custpo}, '%')
</if>
<if test="SearchPojo.statecode != null and SearchPojo.statecode != ''">
   or Sa_DelieryItem.StateCode like concat('%', #{SearchPojo.statecode}, '%')
</if>
<if test="SearchPojo.machtype != null and SearchPojo.machtype != ''">
   or Sa_DelieryItem.MachType like concat('%', #{SearchPojo.machtype}, '%')
</if>
<if test="SearchPojo.location != null and SearchPojo.location != ''">
   or Sa_DelieryItem.Location like concat('%', #{SearchPojo.location}, '%')
</if>
<if test="SearchPojo.batchno != null and SearchPojo.batchno != ''">
   or Sa_DelieryItem.BatchNo like concat('%', #{SearchPojo.batchno}, '%')
</if>
<if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
   or Sa_DelieryItem.MachUid like concat('%', #{SearchPojo.machuid}, '%')
</if>
<if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
   or Sa_DelieryItem.MachItemid like concat('%', #{SearchPojo.machitemid}, '%')
</if>
<if test="SearchPojo.disannullisterid != null and SearchPojo.disannullisterid != ''">
   or Sa_DelieryItem.DisannulListerid like concat('%', #{SearchPojo.disannullisterid}, '%')
</if>
<if test="SearchPojo.disannullister != null and SearchPojo.disannullister != ''">
   or Sa_DelieryItem.DisannulLister like concat('%', #{SearchPojo.disannullister}, '%')
</if>
<if test="SearchPojo.attributejson != null and SearchPojo.attributejson != ''">
   or Sa_DelieryItem.AttributeJson like concat('%', #{SearchPojo.attributejson}, '%')
</if>
<if test="SearchPojo.attributestr != null and SearchPojo.attributestr != ''">
   or Sa_DelieryItem.AttributeStr like concat('%', #{SearchPojo.attributestr}, '%')
</if>
<if test="SearchPojo.costitemjson != null and SearchPojo.costitemjson != ''">
   or Sa_DelieryItem.CostItemJson like concat('%', #{SearchPojo.costitemjson}, '%')
</if>
<if test="SearchPojo.costgroupjson != null and SearchPojo.costgroupjson != ''">
   or Sa_DelieryItem.CostGroupJson like concat('%', #{SearchPojo.costgroupjson}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   or Sa_DelieryItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   or Sa_DelieryItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   or Sa_DelieryItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   or Sa_DelieryItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   or Sa_DelieryItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
   or Sa_DelieryItem.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
   or Sa_DelieryItem.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
   or Sa_DelieryItem.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
   or Sa_DelieryItem.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
   or Sa_DelieryItem.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.custom11 != null and SearchPojo.custom11 != ''">
   or Sa_DelieryItem.Custom11 like concat('%', #{SearchPojo.custom11}, '%')
</if>
<if test="SearchPojo.custom12 != null and SearchPojo.custom12 != ''">
   or Sa_DelieryItem.Custom12 like concat('%', #{SearchPojo.custom12}, '%')
</if>
<if test="SearchPojo.custom13 != null and SearchPojo.custom13 != ''">
   or Sa_DelieryItem.Custom13 like concat('%', #{SearchPojo.custom13}, '%')
</if>
<if test="SearchPojo.custom14 != null and SearchPojo.custom14 != ''">
   or Sa_DelieryItem.Custom14 like concat('%', #{SearchPojo.custom14}, '%')
</if>
<if test="SearchPojo.custom15 != null and SearchPojo.custom15 != ''">
   or Sa_DelieryItem.Custom15 like concat('%', #{SearchPojo.custom15}, '%')
</if>
<if test="SearchPojo.custom16 != null and SearchPojo.custom16 != ''">
   or Sa_DelieryItem.Custom16 like concat('%', #{SearchPojo.custom16}, '%')
</if>
<if test="SearchPojo.custom17 != null and SearchPojo.custom17 != ''">
   or Sa_DelieryItem.Custom17 like concat('%', #{SearchPojo.custom17}, '%')
</if>
<if test="SearchPojo.custom18 != null and SearchPojo.custom18 != ''">
   or Sa_DelieryItem.Custom18 like concat('%', #{SearchPojo.custom18}, '%')
</if>
</trim>
     </sql>
     
         <!--查询List-->
    <select id="getList" resultType="inks.service.sa.crm.domain.pojo.SaDelieryitemPojo">
        select
          id, Pid, Goodsid, ItemCode, ItemName, ItemSpec, ItemUnit, Quantity, TaxPrice, TaxAmount, Price, Amount, ItemTaxrate, TaxTotal, StdPrice, StdAmount, Rebate, FreeQty, PickQty, FinishQty, FinishClosed, RowNum, Remark, CiteUid, CiteItemid, CustPo, StateCode, StateDate, BusSQty, BusSClosed, MachType, InvoQty, InvoClosed, ReturnQty, ReturnMatQty, ReturnClosed, Salescost, VirtualItem, Location, BatchNo, MachUid, MachItemid, DisannulMark, DisannulListerid, DisannulLister, DisannulDate, BFItemid, AttributeJson, AttributeStr, MachDate, CostItemJson, CostGroupJson, SourceType, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Custom11, Custom12, Custom13, Custom14, Custom15, Custom16, Custom17, Custom18, Tenantid, Revision        from Sa_DelieryItem
        where Sa_DelieryItem.Pid = #{Pid} 
        order by RowNum 
    </select>
    
    <!--新增所有列-->
    <insert id="insert" >
        insert into Sa_DelieryItem(id, Pid, Goodsid, ItemCode, ItemName, ItemSpec, ItemUnit, Quantity, TaxPrice, TaxAmount, Price, Amount, ItemTaxrate, TaxTotal, StdPrice, StdAmount, Rebate, FreeQty, PickQty, FinishQty, FinishClosed, RowNum, Remark, CiteUid, CiteItemid, CustPo, StateCode, StateDate, BusSQty, BusSClosed, MachType, InvoQty, InvoClosed, ReturnQty, ReturnMatQty, ReturnClosed, Salescost, VirtualItem, Location, BatchNo, MachUid, MachItemid, DisannulMark, DisannulListerid, DisannulLister, DisannulDate, BFItemid, AttributeJson, AttributeStr, MachDate, CostItemJson, CostGroupJson, SourceType, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Custom11, Custom12, Custom13, Custom14, Custom15, Custom16, Custom17, Custom18, Tenantid, Revision)
        values (#{id}, #{pid}, #{goodsid}, #{itemcode}, #{itemname}, #{itemspec}, #{itemunit}, #{quantity}, #{taxprice}, #{taxamount}, #{price}, #{amount}, #{itemtaxrate}, #{taxtotal}, #{stdprice}, #{stdamount}, #{rebate}, #{freeqty}, #{pickqty}, #{finishqty}, #{finishclosed}, #{rownum}, #{remark}, #{citeuid}, #{citeitemid}, #{custpo}, #{statecode}, #{statedate}, #{bussqty}, #{bussclosed}, #{machtype}, #{invoqty}, #{invoclosed}, #{returnqty}, #{returnmatqty}, #{returnclosed}, #{salescost}, #{virtualitem}, #{location}, #{batchno}, #{machuid}, #{machitemid}, #{disannulmark}, #{disannullisterid}, #{disannullister}, #{disannuldate}, #{bfitemid}, #{attributejson}, #{attributestr}, #{machdate}, #{costitemjson}, #{costgroupjson}, #{sourcetype}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{custom11}, #{custom12}, #{custom13}, #{custom14}, #{custom15}, #{custom16}, #{custom17}, #{custom18}, #{tenantid}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_DelieryItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="goodsid != null ">
                Goodsid = #{goodsid},
            </if>
            <if test="itemcode != null ">
                ItemCode = #{itemcode},
            </if>
            <if test="itemname != null ">
                ItemName = #{itemname},
            </if>
            <if test="itemspec != null ">
                ItemSpec = #{itemspec},
            </if>
            <if test="itemunit != null ">
                ItemUnit = #{itemunit},
            </if>
            <if test="quantity != null">
                Quantity = #{quantity},
            </if>
            <if test="taxprice != null">
                TaxPrice = #{taxprice},
            </if>
            <if test="taxamount != null">
                TaxAmount = #{taxamount},
            </if>
            <if test="price != null">
                Price = #{price},
            </if>
            <if test="amount != null">
                Amount = #{amount},
            </if>
            <if test="itemtaxrate != null">
                ItemTaxrate = #{itemtaxrate},
            </if>
            <if test="taxtotal != null">
                TaxTotal = #{taxtotal},
            </if>
            <if test="stdprice != null">
                StdPrice = #{stdprice},
            </if>
            <if test="stdamount != null">
                StdAmount = #{stdamount},
            </if>
            <if test="rebate != null">
                Rebate = #{rebate},
            </if>
            <if test="freeqty != null">
                FreeQty = #{freeqty},
            </if>
            <if test="pickqty != null">
                PickQty = #{pickqty},
            </if>
            <if test="finishqty != null">
                FinishQty = #{finishqty},
            </if>
            <if test="finishclosed != null">
                FinishClosed = #{finishclosed},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="citeuid != null ">
                CiteUid = #{citeuid},
            </if>
            <if test="citeitemid != null ">
                CiteItemid = #{citeitemid},
            </if>
            <if test="custpo != null ">
                CustPo = #{custpo},
            </if>
            <if test="statecode != null ">
                StateCode = #{statecode},
            </if>
            <if test="statedate != null">
                StateDate = #{statedate},
            </if>
            <if test="bussqty != null">
                BusSQty = #{bussqty},
            </if>
            <if test="bussclosed != null">
                BusSClosed = #{bussclosed},
            </if>
            <if test="machtype != null ">
                MachType = #{machtype},
            </if>
            <if test="invoqty != null">
                InvoQty = #{invoqty},
            </if>
            <if test="invoclosed != null">
                InvoClosed = #{invoclosed},
            </if>
            <if test="returnqty != null">
                ReturnQty = #{returnqty},
            </if>
            <if test="returnmatqty != null">
                ReturnMatQty = #{returnmatqty},
            </if>
            <if test="returnclosed != null">
                ReturnClosed = #{returnclosed},
            </if>
            <if test="salescost != null">
                Salescost = #{salescost},
            </if>
            <if test="virtualitem != null">
                VirtualItem = #{virtualitem},
            </if>
            <if test="location != null ">
                Location = #{location},
            </if>
            <if test="batchno != null ">
                BatchNo = #{batchno},
            </if>
            <if test="machuid != null ">
                MachUid = #{machuid},
            </if>
            <if test="machitemid != null ">
                MachItemid = #{machitemid},
            </if>
            <if test="disannulmark != null">
                DisannulMark = #{disannulmark},
            </if>
            <if test="disannullisterid != null ">
                DisannulListerid = #{disannullisterid},
            </if>
            <if test="disannullister != null ">
                DisannulLister = #{disannullister},
            </if>
            <if test="disannuldate != null">
                DisannulDate = #{disannuldate},
            </if>
            <if test="bfitemid != null">
                BFItemid = #{bfitemid},
            </if>
            <if test="attributejson != null ">
                AttributeJson = #{attributejson},
            </if>
            <if test="attributestr != null ">
                AttributeStr = #{attributestr},
            </if>
            <if test="machdate != null">
                MachDate = #{machdate},
            </if>
            <if test="costitemjson != null ">
                CostItemJson = #{costitemjson},
            </if>
            <if test="costgroupjson != null ">
                CostGroupJson = #{costgroupjson},
            </if>
            <if test="sourcetype != null">
                SourceType = #{sourcetype},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 = #{custom10},
            </if>
            <if test="custom11 != null ">
                Custom11 = #{custom11},
            </if>
            <if test="custom12 != null ">
                Custom12 = #{custom12},
            </if>
            <if test="custom13 != null ">
                Custom13 = #{custom13},
            </if>
            <if test="custom14 != null ">
                Custom14 = #{custom14},
            </if>
            <if test="custom15 != null ">
                Custom15 = #{custom15},
            </if>
            <if test="custom16 != null ">
                Custom16 = #{custom16},
            </if>
            <if test="custom17 != null ">
                Custom17 = #{custom17},
            </if>
            <if test="custom18 != null ">
                Custom18 = #{custom18},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_DelieryItem where id = #{key} 
    </delete>

</mapper>

