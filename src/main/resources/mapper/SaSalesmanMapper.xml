<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.crm.mapper.SaSalesmanMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.crm.domain.pojo.SaSalesmanPojo">
        select
          id, Userid, GenGroupid, SalesmanType, SalesmanCode, SalesmanName, EnabledMark, Email, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Deptid, Tenantid, Revision        from Sa_Salesman
        where Sa_Salesman.id = #{key}
    </select>
    <sql id="selectSaSalesmanVo">
         select
          id, Userid, GenGroupid, SalesmanType, SalesmanCode, SalesmanName, EnabledMark, Email, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Deptid, Tenantid, Revision        from Sa_Salesman
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.crm.domain.pojo.SaSalesmanPojo">
        <include refid="selectSaSalesmanVo"/>
         where 1 = 1
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_Salesman.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">
           <include refid="and"></include>
         </if>
         <if test="SearchType==1">
           <include refid="or"></include>
         </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.userid != null ">
   and Sa_Salesman.Userid like concat('%', #{SearchPojo.userid}, '%')
</if>
<if test="SearchPojo.gengroupid != null ">
   and Sa_Salesman.GenGroupid like concat('%', #{SearchPojo.gengroupid}, '%')
</if>
<if test="SearchPojo.salesmantype != null ">
   and Sa_Salesman.SalesmanType like concat('%', #{SearchPojo.salesmantype}, '%')
</if>
<if test="SearchPojo.salesmancode != null ">
   and Sa_Salesman.SalesmanCode like concat('%', #{SearchPojo.salesmancode}, '%')
</if>
<if test="SearchPojo.salesmanname != null ">
   and Sa_Salesman.SalesmanName like concat('%', #{SearchPojo.salesmanname}, '%')
</if>
<if test="SearchPojo.email != null ">
   and Sa_Salesman.Email like concat('%', #{SearchPojo.email}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Sa_Salesman.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Sa_Salesman.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Sa_Salesman.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Sa_Salesman.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Sa_Salesman.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Sa_Salesman.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Sa_Salesman.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Sa_Salesman.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Sa_Salesman.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Sa_Salesman.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and Sa_Salesman.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and Sa_Salesman.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and Sa_Salesman.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   and Sa_Salesman.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   and Sa_Salesman.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.deptid != null ">
   and Sa_Salesman.Deptid like concat('%', #{SearchPojo.deptid}, '%')
</if>
     </sql>
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.userid != null ">
   or Sa_Salesman.Userid like concat('%', #{SearchPojo.userid}, '%')
</if>
<if test="SearchPojo.gengroupid != null ">
   or Sa_Salesman.GenGroupid like concat('%', #{SearchPojo.gengroupid}, '%')
</if>
<if test="SearchPojo.salesmantype != null ">
   or Sa_Salesman.SalesmanType like concat('%', #{SearchPojo.salesmantype}, '%')
</if>
<if test="SearchPojo.salesmancode != null ">
   or Sa_Salesman.SalesmanCode like concat('%', #{SearchPojo.salesmancode}, '%')
</if>
<if test="SearchPojo.salesmanname != null ">
   or Sa_Salesman.SalesmanName like concat('%', #{SearchPojo.salesmanname}, '%')
</if>
<if test="SearchPojo.email != null ">
   or Sa_Salesman.Email like concat('%', #{SearchPojo.email}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Sa_Salesman.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Sa_Salesman.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Sa_Salesman.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Sa_Salesman.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Sa_Salesman.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Sa_Salesman.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Sa_Salesman.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Sa_Salesman.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Sa_Salesman.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Sa_Salesman.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   or Sa_Salesman.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   or Sa_Salesman.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   or Sa_Salesman.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   or Sa_Salesman.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   or Sa_Salesman.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.deptid != null ">
   or Sa_Salesman.Deptid like concat('%', #{SearchPojo.deptid}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Sa_Salesman(id, Userid, GenGroupid, SalesmanType, SalesmanCode, SalesmanName, EnabledMark, Email, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Deptid, Tenantid, Revision)
        values (#{id}, #{userid}, #{gengroupid}, #{salesmantype}, #{salesmancode}, #{salesmanname}, #{enabledmark}, #{email}, #{rownum}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{deptid}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_Salesman
        <set>
            <if test="userid != null ">
                Userid =#{userid},
            </if>
            <if test="gengroupid != null ">
                GenGroupid =#{gengroupid},
            </if>
            <if test="salesmantype != null ">
                SalesmanType =#{salesmantype},
            </if>
            <if test="salesmancode != null ">
                SalesmanCode =#{salesmancode},
            </if>
            <if test="salesmanname != null ">
                SalesmanName =#{salesmanname},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="email != null ">
                Email =#{email},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="deptid != null ">
                Deptid =#{deptid},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_Salesman where id = #{key}
    </delete>
                                                                                                                    </mapper>

