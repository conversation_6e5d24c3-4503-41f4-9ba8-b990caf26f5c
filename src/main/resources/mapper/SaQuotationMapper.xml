<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.crm.mapper.SaQuotationMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.crm.domain.pojo.SaQuotationPojo">
        <include refid="selectbillVo"/>
        where Sa_Quotation.id = #{key} 
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        select Sa_Quotation.id,
               Sa_Quotation.RefNo,
               Sa_Quotation.BillType,
               Sa_Quotation.BillTitle,
               Sa_Quotation.BillDate,
               Sa_Quotation.Probability,
               Sa_Quotation.Groupid,
               Sa_Quotation.Businessid,
               Sa_Quotation.Customer,
               Sa_Quotation.CustAddress,
               Sa_Quotation.CustLinkman,
               Sa_Quotation.CustTel,
               Sa_Quotation.CustFax,
               Sa_Quotation.TaxMark,
               Sa_Quotation.Periods,
               Sa_Quotation.ValidityDate,
               Sa_Quotation.Currency,
               Sa_Quotation.Delivery,
               Sa_Quotation.Payment,
               Sa_Quotation.WorkStage,
               Sa_Quotation.Operatorid,
               Sa_Quotation.Operator,
               Sa_Quotation.BillClause,
               Sa_Quotation.BillTaxAmount,
               Sa_Quotation.BillAmount,
               Sa_Quotation.BillTaxTotal,
               Sa_Quotation.Summary,
               Sa_Quotation.CreateBy,
               Sa_Quotation.CreateByid,
               Sa_Quotation.CreateDate,
               Sa_Quotation.Lister,
               Sa_Quotation.Listerid,
               Sa_Quotation.ModifyDate,
               Sa_Quotation.Assessor,
               Sa_Quotation.Assessorid,
               Sa_Quotation.AssessDate,
               Sa_Quotation.OaFlowMark,
               Sa_Quotation.StateCode,
               Sa_Quotation.StateDate,
               Sa_Quotation.Principalid,
               Sa_Quotation.Principal,
               Sa_Quotation.LastFollowDate,
               Sa_Quotation.Custom1,
               Sa_Quotation.Custom2,
               Sa_Quotation.Custom3,
               Sa_Quotation.Custom4,
               Sa_Quotation.Custom5,
               Sa_Quotation.Custom6,
               Sa_Quotation.Custom7,
               Sa_Quotation.Custom8,
               Sa_Quotation.Custom9,
               Sa_Quotation.Custom10,
               Sa_Quotation.Deptid,
               Sa_Quotation.Tenantid,
               Sa_Quotation.TenantName,
               Sa_Quotation.Revision,
               Sa_Customer.CustName,
               Sa_Customer.CustName AS GroupName,
               Sa_Business.BillTitle as BusinessTitle
        from Sa_Quotation
                 LEFT JOIN Sa_Customer ON Sa_Quotation.Groupid = Sa_Customer.id
                 Left join Sa_Business on Sa_Quotation.Businessid = Sa_Business.id
    </sql>
    <sql id="selectdetailVo">
        select Sa_QuotationItem.id,
               Sa_QuotationItem.Pid,
               Sa_QuotationItem.Goodsid,
               Sa_QuotationItem.ItemCode,
               Sa_QuotationItem.ItemName,
               Sa_QuotationItem.ItemSpec,
               Sa_QuotationItem.ItemUnit,
               Sa_QuotationItem.Quantity,
               Sa_QuotationItem.Price,
               Sa_QuotationItem.Amount,
               Sa_QuotationItem.ItemTaxrate,
               Sa_QuotationItem.TaxTotal,
               Sa_QuotationItem.TaxPrice,
               Sa_QuotationItem.TaxAmount,
               Sa_QuotationItem.Remark,
               Sa_QuotationItem.RowNum,
               Sa_QuotationItem.Custom1,
               Sa_QuotationItem.Custom2,
               Sa_QuotationItem.Custom3,
               Sa_QuotationItem.Custom4,
               Sa_QuotationItem.Custom5,
               Sa_QuotationItem.Custom6,
               Sa_QuotationItem.Custom7,
               Sa_QuotationItem.Custom8,
               Sa_QuotationItem.Custom9,
               Sa_QuotationItem.Custom10,
               Sa_QuotationItem.Tenantid,
               Sa_QuotationItem.Revision,
               Sa_Quotation.RefNo,
               Sa_Quotation.BillType,
               Sa_Quotation.BillDate,
               Sa_Quotation.BillTitle,
               Sa_Customer.CustName,
               Sa_Customer.CustName AS GroupName,
               Sa_Business.BillTitle as BusinessTitle
        from Sa_QuotationItem
                 LEFT JOIN Sa_Quotation ON Sa_QuotationItem.Pid = Sa_Quotation.id
                 LEFT JOIN Sa_Customer ON Sa_Quotation.Groupid = Sa_Customer.id
                 Left join Sa_Business on Sa_Quotation.Businessid = Sa_Business.id
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.crm.domain.pojo.SaQuotationitemdetailPojo">
        <include refid="selectdetailVo"/>
         where 1 = 1 
       <if test="filterstr != null ">
            ${filterstr}
        </if> 
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_Quotation.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.refno != null ">
   and Sa_Quotation.refno like concat('%', #{SearchPojo.refno}, '%')
</if>
<if test="SearchPojo.billtype != null ">
   and Sa_Quotation.billtype like concat('%', #{SearchPojo.billtype}, '%')
</if>
<if test="SearchPojo.billtitle != null ">
   and Sa_Quotation.billtitle like concat('%', #{SearchPojo.billtitle}, '%')
</if>
<if test="SearchPojo.probability != null ">
   and Sa_Quotation.probability like concat('%', #{SearchPojo.probability}, '%')
</if>
<if test="SearchPojo.groupid != null ">
   and Sa_Quotation.groupid like concat('%', #{SearchPojo.groupid}, '%')
</if>
<if test="SearchPojo.customer != null ">
   and Sa_Quotation.customer like concat('%', #{SearchPojo.customer}, '%')
</if>
<if test="SearchPojo.custaddress != null ">
   and Sa_Quotation.custaddress like concat('%', #{SearchPojo.custaddress}, '%')
</if>
<if test="SearchPojo.custlinkman != null ">
   and Sa_Quotation.custlinkman like concat('%', #{SearchPojo.custlinkman}, '%')
</if>
<if test="SearchPojo.custtel != null ">
   and Sa_Quotation.custtel like concat('%', #{SearchPojo.custtel}, '%')
</if>
<if test="SearchPojo.custfax != null ">
   and Sa_Quotation.custfax like concat('%', #{SearchPojo.custfax}, '%')
</if>
<if test="SearchPojo.periods != null ">
   and Sa_Quotation.periods like concat('%', #{SearchPojo.periods}, '%')
</if>
<if test="SearchPojo.validitydate != null ">
   and Sa_Quotation.validitydate like concat('%', #{SearchPojo.validitydate}, '%')
</if>
<if test="SearchPojo.currency != null ">
   and Sa_Quotation.currency like concat('%', #{SearchPojo.currency}, '%')
</if>
<if test="SearchPojo.delivery != null ">
   and Sa_Quotation.delivery like concat('%', #{SearchPojo.delivery}, '%')
</if>
<if test="SearchPojo.payment != null ">
   and Sa_Quotation.payment like concat('%', #{SearchPojo.payment}, '%')
</if>
<if test="SearchPojo.workstage != null ">
   and Sa_Quotation.workstage like concat('%', #{SearchPojo.workstage}, '%')
</if>
<if test="SearchPojo.operatorid != null ">
   and Sa_Quotation.operatorid like concat('%', #{SearchPojo.operatorid}, '%')
</if>
<if test="SearchPojo.operator != null ">
   and Sa_Quotation.operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.billclause != null ">
   and Sa_Quotation.billclause like concat('%', #{SearchPojo.billclause}, '%')
</if>
<if test="SearchPojo.summary != null ">
   and Sa_Quotation.summary like concat('%', #{SearchPojo.summary}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Sa_Quotation.createby like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Sa_Quotation.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Sa_Quotation.lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Sa_Quotation.listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   and Sa_Quotation.assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   and Sa_Quotation.assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.statecode != null ">
   and Sa_Quotation.statecode like concat('%', #{SearchPojo.statecode}, '%')
</if>
<if test="SearchPojo.principalid != null ">
   and Sa_Quotation.principalid like concat('%', #{SearchPojo.principalid}, '%')
</if>
<if test="SearchPojo.principal != null ">
   and Sa_Quotation.principal like concat('%', #{SearchPojo.principal}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Sa_Quotation.custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Sa_Quotation.custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Sa_Quotation.custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Sa_Quotation.custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Sa_Quotation.custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and Sa_Quotation.custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and Sa_Quotation.custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and Sa_Quotation.custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   and Sa_Quotation.custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   and Sa_Quotation.custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.deptid != null ">
   and Sa_Quotation.deptid like concat('%', #{SearchPojo.deptid}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Sa_Quotation.tenantname like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.refno != null ">
   or Sa_Quotation.RefNo like concat('%', #{SearchPojo.refno}, '%')
</if>
      <if test="SearchPojo.custname != null ">
          or Sa_Customer.CustName like concat('%', #{SearchPojo.custname}, '%')
      </if>
<if test="SearchPojo.billtype != null ">
   or Sa_Quotation.BillType like concat('%', #{SearchPojo.billtype}, '%')
</if>
<if test="SearchPojo.billtitle != null ">
   or Sa_Quotation.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
</if>
<if test="SearchPojo.probability != null ">
   or Sa_Quotation.Probability like concat('%', #{SearchPojo.probability}, '%')
</if>
<if test="SearchPojo.groupid != null ">
   or Sa_Quotation.Groupid like concat('%', #{SearchPojo.groupid}, '%')
</if>
<if test="SearchPojo.customer != null ">
   or Sa_Quotation.Customer like concat('%', #{SearchPojo.customer}, '%')
</if>
<if test="SearchPojo.custaddress != null ">
   or Sa_Quotation.CustAddress like concat('%', #{SearchPojo.custaddress}, '%')
</if>
<if test="SearchPojo.custlinkman != null ">
   or Sa_Quotation.CustLinkman like concat('%', #{SearchPojo.custlinkman}, '%')
</if>
<if test="SearchPojo.custtel != null ">
   or Sa_Quotation.CustTel like concat('%', #{SearchPojo.custtel}, '%')
</if>
<if test="SearchPojo.custfax != null ">
   or Sa_Quotation.CustFax like concat('%', #{SearchPojo.custfax}, '%')
</if>
<if test="SearchPojo.periods != null ">
   or Sa_Quotation.Periods like concat('%', #{SearchPojo.periods}, '%')
</if>
<if test="SearchPojo.validitydate != null ">
   or Sa_Quotation.ValidityDate like concat('%', #{SearchPojo.validitydate}, '%')
</if>
<if test="SearchPojo.currency != null ">
   or Sa_Quotation.Currency like concat('%', #{SearchPojo.currency}, '%')
</if>
<if test="SearchPojo.delivery != null ">
   or Sa_Quotation.Delivery like concat('%', #{SearchPojo.delivery}, '%')
</if>
<if test="SearchPojo.payment != null ">
   or Sa_Quotation.Payment like concat('%', #{SearchPojo.payment}, '%')
</if>
<if test="SearchPojo.workstage != null ">
   or Sa_Quotation.WorkStage like concat('%', #{SearchPojo.workstage}, '%')
</if>
<if test="SearchPojo.operatorid != null ">
   or Sa_Quotation.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
</if>
<if test="SearchPojo.operator != null ">
   or Sa_Quotation.Operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.billclause != null ">
   or Sa_Quotation.BillClause like concat('%', #{SearchPojo.billclause}, '%')
</if>
<if test="SearchPojo.summary != null ">
   or Sa_Quotation.Summary like concat('%', #{SearchPojo.summary}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Sa_Quotation.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Sa_Quotation.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Sa_Quotation.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Sa_Quotation.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   or Sa_Quotation.Assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   or Sa_Quotation.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.statecode != null ">
   or Sa_Quotation.StateCode like concat('%', #{SearchPojo.statecode}, '%')
</if>
<if test="SearchPojo.principalid != null ">
   or Sa_Quotation.Principalid like concat('%', #{SearchPojo.principalid}, '%')
</if>
<if test="SearchPojo.principal != null ">
   or Sa_Quotation.Principal like concat('%', #{SearchPojo.principal}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Sa_Quotation.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Sa_Quotation.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Sa_Quotation.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Sa_Quotation.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Sa_Quotation.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   or Sa_Quotation.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   or Sa_Quotation.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   or Sa_Quotation.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   or Sa_Quotation.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   or Sa_Quotation.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.deptid != null ">
   or Sa_Quotation.Deptid like concat('%', #{SearchPojo.deptid}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Sa_Quotation.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
     
         <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.crm.domain.pojo.SaQuotationPojo">
        <include refid="selectbillVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_Quotation.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="thand"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="thor"></include>        
         </if>
        </if> 
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="thand">
<if test="SearchPojo.refno != null ">
   and Sa_Quotation.RefNo like concat('%', #{SearchPojo.refno}, '%')
</if>
<if test="SearchPojo.billtype != null ">
   and Sa_Quotation.BillType like concat('%', #{SearchPojo.billtype}, '%')
</if>
<if test="SearchPojo.billtitle != null ">
   and Sa_Quotation.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
</if>
<if test="SearchPojo.probability != null ">
   and Sa_Quotation.Probability like concat('%', #{SearchPojo.probability}, '%')
</if>
<if test="SearchPojo.groupid != null ">
   and Sa_Quotation.Groupid like concat('%', #{SearchPojo.groupid}, '%')
</if>
<if test="SearchPojo.customer != null ">
   and Sa_Quotation.Customer like concat('%', #{SearchPojo.customer}, '%')
</if>
<if test="SearchPojo.custaddress != null ">
   and Sa_Quotation.CustAddress like concat('%', #{SearchPojo.custaddress}, '%')
</if>
<if test="SearchPojo.custlinkman != null ">
   and Sa_Quotation.CustLinkman like concat('%', #{SearchPojo.custlinkman}, '%')
</if>
<if test="SearchPojo.custtel != null ">
   and Sa_Quotation.CustTel like concat('%', #{SearchPojo.custtel}, '%')
</if>
<if test="SearchPojo.custfax != null ">
   and Sa_Quotation.CustFax like concat('%', #{SearchPojo.custfax}, '%')
</if>
<if test="SearchPojo.periods != null ">
   and Sa_Quotation.Periods like concat('%', #{SearchPojo.periods}, '%')
</if>
<if test="SearchPojo.validitydate != null ">
   and Sa_Quotation.ValidityDate like concat('%', #{SearchPojo.validitydate}, '%')
</if>
<if test="SearchPojo.currency != null ">
   and Sa_Quotation.Currency like concat('%', #{SearchPojo.currency}, '%')
</if>
<if test="SearchPojo.delivery != null ">
   and Sa_Quotation.Delivery like concat('%', #{SearchPojo.delivery}, '%')
</if>
<if test="SearchPojo.payment != null ">
   and Sa_Quotation.Payment like concat('%', #{SearchPojo.payment}, '%')
</if>
<if test="SearchPojo.workstage != null ">
   and Sa_Quotation.WorkStage like concat('%', #{SearchPojo.workstage}, '%')
</if>
<if test="SearchPojo.operatorid != null ">
   and Sa_Quotation.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
</if>
<if test="SearchPojo.operator != null ">
   and Sa_Quotation.Operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.billclause != null ">
   and Sa_Quotation.BillClause like concat('%', #{SearchPojo.billclause}, '%')
</if>
<if test="SearchPojo.summary != null ">
   and Sa_Quotation.Summary like concat('%', #{SearchPojo.summary}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Sa_Quotation.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Sa_Quotation.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Sa_Quotation.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Sa_Quotation.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   and Sa_Quotation.Assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   and Sa_Quotation.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.statecode != null ">
   and Sa_Quotation.StateCode like concat('%', #{SearchPojo.statecode}, '%')
</if>
<if test="SearchPojo.principalid != null ">
   and Sa_Quotation.Principalid like concat('%', #{SearchPojo.principalid}, '%')
</if>
<if test="SearchPojo.principal != null ">
   and Sa_Quotation.Principal like concat('%', #{SearchPojo.principal}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Sa_Quotation.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Sa_Quotation.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Sa_Quotation.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Sa_Quotation.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Sa_Quotation.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and Sa_Quotation.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and Sa_Quotation.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and Sa_Quotation.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   and Sa_Quotation.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   and Sa_Quotation.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.deptid != null ">
   and Sa_Quotation.Deptid like concat('%', #{SearchPojo.deptid}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Sa_Quotation.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="thor">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.refno != null ">
   or Sa_Quotation.RefNo like concat('%', #{SearchPojo.refno}, '%')
</if>
      <if test="SearchPojo.custname != null ">
          or Sa_Customer.CustName like concat('%', #{SearchPojo.custname}, '%')
      </if>
<if test="SearchPojo.billtype != null ">
   or Sa_Quotation.BillType like concat('%', #{SearchPojo.billtype}, '%')
</if>
<if test="SearchPojo.billtitle != null ">
   or Sa_Quotation.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
</if>
<if test="SearchPojo.probability != null ">
   or Sa_Quotation.Probability like concat('%', #{SearchPojo.probability}, '%')
</if>
<if test="SearchPojo.groupid != null ">
   or Sa_Quotation.Groupid like concat('%', #{SearchPojo.groupid}, '%')
</if>
<if test="SearchPojo.customer != null ">
   or Sa_Quotation.Customer like concat('%', #{SearchPojo.customer}, '%')
</if>
<if test="SearchPojo.custaddress != null ">
   or Sa_Quotation.CustAddress like concat('%', #{SearchPojo.custaddress}, '%')
</if>
<if test="SearchPojo.custlinkman != null ">
   or Sa_Quotation.CustLinkman like concat('%', #{SearchPojo.custlinkman}, '%')
</if>
<if test="SearchPojo.custtel != null ">
   or Sa_Quotation.CustTel like concat('%', #{SearchPojo.custtel}, '%')
</if>
<if test="SearchPojo.custfax != null ">
   or Sa_Quotation.CustFax like concat('%', #{SearchPojo.custfax}, '%')
</if>
<if test="SearchPojo.periods != null ">
   or Sa_Quotation.Periods like concat('%', #{SearchPojo.periods}, '%')
</if>
<if test="SearchPojo.validitydate != null ">
   or Sa_Quotation.ValidityDate like concat('%', #{SearchPojo.validitydate}, '%')
</if>
<if test="SearchPojo.currency != null ">
   or Sa_Quotation.Currency like concat('%', #{SearchPojo.currency}, '%')
</if>
<if test="SearchPojo.delivery != null ">
   or Sa_Quotation.Delivery like concat('%', #{SearchPojo.delivery}, '%')
</if>
<if test="SearchPojo.payment != null ">
   or Sa_Quotation.Payment like concat('%', #{SearchPojo.payment}, '%')
</if>
<if test="SearchPojo.workstage != null ">
   or Sa_Quotation.WorkStage like concat('%', #{SearchPojo.workstage}, '%')
</if>
<if test="SearchPojo.operatorid != null ">
   or Sa_Quotation.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
</if>
<if test="SearchPojo.operator != null ">
   or Sa_Quotation.Operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.billclause != null ">
   or Sa_Quotation.BillClause like concat('%', #{SearchPojo.billclause}, '%')
</if>
<if test="SearchPojo.summary != null ">
   or Sa_Quotation.Summary like concat('%', #{SearchPojo.summary}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Sa_Quotation.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Sa_Quotation.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Sa_Quotation.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Sa_Quotation.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   or Sa_Quotation.Assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   or Sa_Quotation.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.statecode != null ">
   or Sa_Quotation.StateCode like concat('%', #{SearchPojo.statecode}, '%')
</if>
<if test="SearchPojo.principalid != null ">
   or Sa_Quotation.Principalid like concat('%', #{SearchPojo.principalid}, '%')
</if>
<if test="SearchPojo.principal != null ">
   or Sa_Quotation.Principal like concat('%', #{SearchPojo.principal}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Sa_Quotation.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Sa_Quotation.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Sa_Quotation.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Sa_Quotation.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Sa_Quotation.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   or Sa_Quotation.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   or Sa_Quotation.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   or Sa_Quotation.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   or Sa_Quotation.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   or Sa_Quotation.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.deptid != null ">
   or Sa_Quotation.Deptid like concat('%', #{SearchPojo.deptid}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Sa_Quotation.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
     
    <!--新增所有列-->
    <insert id="insert" >
        insert into Sa_Quotation(id, RefNo, BillType, BillTitle, BillDate, Probability, Groupid, Businessid, Customer, CustAddress, CustLinkman, CustTel, CustFax, TaxMark, Periods, ValidityDate, Currency, Delivery, Payment, WorkStage, Operatorid, Operator, BillClause, BillTaxAmount, BillAmount, BillTaxTotal, Summary, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Assessor, Assessorid, AssessDate, OaFlowMark, StateCode, StateDate, Principalid, Principal, LastFollowDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Deptid, Tenantid, TenantName, Revision)
        values (#{id}, #{refno}, #{billtype}, #{billtitle}, #{billdate}, #{probability}, #{groupid}, #{businessid}, #{customer}, #{custaddress}, #{custlinkman}, #{custtel}, #{custfax}, #{taxmark}, #{periods}, #{validitydate}, #{currency}, #{delivery}, #{payment}, #{workstage}, #{operatorid}, #{operator}, #{billclause}, #{billtaxamount}, #{billamount}, #{billtaxtotal}, #{summary}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{assessor}, #{assessorid}, #{assessdate}, #{oaflowmark}, #{statecode}, #{statedate}, #{principalid}, #{principal}, #{lastfollowdate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{deptid}, #{tenantid}, #{tenantname}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_Quotation
        <set>
            <if test="refno != null ">
                RefNo =#{refno},
            </if>
            <if test="billtype != null ">
                BillType =#{billtype},
            </if>
            <if test="billtitle != null ">
                BillTitle =#{billtitle},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="probability != null ">
                Probability =#{probability},
            </if>
            <if test="groupid != null ">
                Groupid =#{groupid},
            </if>
            <if test="businessid != null ">
                Businessid =#{businessid},
            </if>
            <if test="customer != null ">
                Customer =#{customer},
            </if>
            <if test="custaddress != null ">
                CustAddress =#{custaddress},
            </if>
            <if test="custlinkman != null ">
                CustLinkman =#{custlinkman},
            </if>
            <if test="custtel != null ">
                CustTel =#{custtel},
            </if>
            <if test="custfax != null ">
                CustFax =#{custfax},
            </if>
            <if test="taxmark != null">
                TaxMark =#{taxmark},
            </if>
            <if test="periods != null ">
                Periods =#{periods},
            </if>
            <if test="validitydate != null ">
                ValidityDate =#{validitydate},
            </if>
            <if test="currency != null ">
                Currency =#{currency},
            </if>
            <if test="delivery != null ">
                Delivery =#{delivery},
            </if>
            <if test="payment != null ">
                Payment =#{payment},
            </if>
            <if test="workstage != null ">
                WorkStage =#{workstage},
            </if>
            <if test="operatorid != null ">
                Operatorid =#{operatorid},
            </if>
            <if test="operator != null ">
                Operator =#{operator},
            </if>
            <if test="billclause != null ">
                BillClause =#{billclause},
            </if>
            <if test="billtaxamount != null">
                BillTaxAmount =#{billtaxamount},
            </if>
            <if test="billamount != null">
                BillAmount =#{billamount},
            </if>
            <if test="billtaxtotal != null">
                BillTaxTotal =#{billtaxtotal},
            </if>
            <if test="summary != null ">
                Summary =#{summary},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="assessor != null ">
                Assessor =#{assessor},
            </if>
            <if test="assessorid != null ">
                Assessorid =#{assessorid},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="oaflowmark != null">
                OaFlowMark =#{oaflowmark},
            </if>
            <if test="statecode != null ">
                StateCode =#{statecode},
            </if>
            <if test="statedate != null">
                StateDate =#{statedate},
            </if>
            <if test="principalid != null ">
                Principalid =#{principalid},
            </if>
            <if test="principal != null ">
                Principal =#{principal},
            </if>
            <if test="lastfollowdate != null">
                LastFollowDate =#{lastfollowdate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="deptid != null ">
                Deptid =#{deptid},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_Quotation where id = #{key} 
    </delete>
                                                                                                                                        <!--通过主键审核数据-->
    <update id="approval">
        update Sa_Quotation SET
            Assessor = #{assessor},
            Assessorid = #{assessorid},
            AssessDate = #{assessdate},
            Revision=Revision+1
        where id = #{id}
    </update>
                                                                                <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String" parameterType="inks.service.sa.crm.domain.pojo.SaQuotationPojo">
        select
          id
        from Sa_QuotationItem
        where Pid = #{id}
        <if test="item !=null and item.size()>0">
         and id not in
        <foreach collection="item" open="(" close=")" separator="," item="item">
            <if test="item.id != null">
                #{item.id}
            </if>
            <if test="item.id == null">
                 ''
            </if>
        </foreach>
         </if>
    </select>

    <update id="updateOaflowmark">
        update Sa_Quotation
        SET OaFlowMark = #{oaflowmark}
        where id = #{id}
    </update>
</mapper>

