<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.crm.mapper.S07MBIR1Mapper">
    <!--    查询本月[总共的]新增客户数S07M03B1，新增商机数S07M05B1，新增线索数S07M02B1，新增跟踪记录S07M10B1，新增合同数S07M06B1-->
    <select id="getNewCountThisMonth" resultType="java.util.Map">
        SELECT (SELECT COUNT(*)
                FROM Sa_Customer
                WHERE CreateDate BETWEEN #{DateRange.StartDate} AND #{DateRange.EndDate} ${filterstr}) AS customer,
               (SELECT COUNT(*)
                FROM Sa_Business
                WHERE CreateDate BETWEEN #{DateRange.StartDate} AND #{DateRange.EndDate} ${filterstr}) AS business,
               (SELECT COUNT(*)
                FROM Sa_Leads
                WHERE CreateDate BETWEEN #{DateRange.StartDate} AND #{DateRange.EndDate} ${filterstr}) AS leads,
               (SELECT COUNT(*)
                FROM Sa_Followview
                WHERE CreateDate BETWEEN #{DateRange.StartDate} AND #{DateRange.EndDate} ${filterstr}) AS followview,
               (SELECT COUNT(*)
                FROM Sa_Contract
                WHERE CreateDate BETWEEN #{DateRange.StartDate} AND #{DateRange.EndDate} ${filterstr}) AS contract,
               (SELECT COUNT(*)
                FROM Sa_Quotation
                WHERE CreateDate BETWEEN #{DateRange.StartDate} AND #{DateRange.EndDate} ${filterstr}) AS quotation
    </select>
    <!--    <select id="getNewCountThisMonth" resultType="java.util.Map">-->
    <!--        SELECT (SELECT COUNT(*)-->
    <!--        FROM Sa_Customer-->
    <!--        WHERE CreateDate BETWEEN #{DateRange.StartDate} AND #{DateRange.EndDate} ${filterstr}) AS customer,-->
    <!--        (SELECT COUNT(*)-->
    <!--        FROM Sa_Business-->
    <!--        WHERE CreateDate BETWEEN #{DateRange.StartDate} AND #{DateRange.EndDate} ${filterstr}) AS business,-->
    <!--        (SELECT SUM(IFNULL(BillTaxAmount, 0))-->
    <!--        FROM Sa_Business-->
    <!--        WHERE CreateDate BETWEEN #{DateRange.StartDate} AND #{DateRange.EndDate} ${filterstr}) AS businessamt,-->
    <!--        (SELECT COUNT(*)-->
    <!--        FROM Sa_Leads-->
    <!--        WHERE CreateDate BETWEEN #{DateRange.StartDate} AND #{DateRange.EndDate} ${filterstr}) AS leads,-->
    <!--        (SELECT COUNT(*)-->
    <!--        FROM Sa_Followview-->
    <!--        WHERE CreateDate BETWEEN #{DateRange.StartDate} AND #{DateRange.EndDate} ${filterstr}) AS followview,-->
    <!--        (SELECT COUNT(*)-->
    <!--        FROM Sa_Contract-->
    <!--        WHERE CreateDate BETWEEN #{DateRange.StartDate} AND #{DateRange.EndDate} ${filterstr}) AS contract,-->
    <!--        (SELECT SUM(IFNULL(BillTaxAmount, 0))-->
    <!--        FROM Sa_Contract-->
    <!--        WHERE CreateDate BETWEEN #{DateRange.StartDate} AND #{DateRange.EndDate} ${filterstr}) AS contractamt,-->
    <!--        (SELECT COUNT(*)-->
    <!--        FROM Sa_Quotation-->
    <!--        WHERE CreateDate BETWEEN #{DateRange.StartDate} AND #{DateRange.EndDate} ${filterstr}) AS quotation,-->
    <!--        (SELECT SUM(IFNULL(BillTaxAmount, 0))-->
    <!--        FROM Sa_Quotation-->
    <!--        WHERE CreateDate BETWEEN #{DateRange.StartDate} AND #{DateRange.EndDate} ${filterstr}) AS quotationamt-->
    <!--    </select>-->


    <!--    查询本月[每天的]新增客户数S07M03B1，新增商机数S07M05B1，新增线索数S07M02B1，新增跟踪记录S07M10B1，新增合同数S07M06B1-->
    <select id="countPublicLeads" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM Sa_Leads
        WHERE Sa_Leads.Principalid = ''
          and Sa_Leads.groupid = ''
    </select>


    <!--    查询本月[每天新增的]新增客户数S07M03B1，新增商机数S07M05B1，新增线索数S07M02B1，新增跟踪记录S07M10B1，新增合同数S07M06B1-->
    <select id="getNewCountEveryDay" resultType="java.util.Map">
        SELECT DATE(c.CreateDate)    AS createdate,
               COUNT(DISTINCT c.Id)  AS customer,
               COUNT(DISTINCT l.Id)  AS leads,
               COUNT(DISTINCT b.Id)  AS business,
               COUNT(DISTINCT f.Id)  AS followview,
               COUNT(DISTINCT ct.Id) AS contract
        FROM (SELECT CreateDate, Id
              FROM Sa_Customer
              WHERE CreateDate BETWEEN DATE_FORMAT(NOW(), '%Y-%m-01') AND LAST_DAY(NOW()) ${filterstr}) c
                 LEFT JOIN
             (SELECT CreateDate, Id
              FROM Sa_Leads
              WHERE CreateDate BETWEEN DATE_FORMAT(NOW(), '%Y-%m-01') AND LAST_DAY(NOW()) ${filterstr}) l
             ON DATE(c.CreateDate) = DATE(l.CreateDate)
                 LEFT JOIN
             (SELECT CreateDate, Id
              FROM Sa_Business
              WHERE CreateDate BETWEEN DATE_FORMAT(NOW(), '%Y-%m-01') AND LAST_DAY(NOW()) ${filterstr}) b
             ON DATE(c.CreateDate) = DATE(b.CreateDate)
                 LEFT JOIN
             (SELECT CreateDate, Id
              FROM Sa_Followview
              WHERE CreateDate BETWEEN DATE_FORMAT(NOW(), '%Y-%m-01') AND LAST_DAY(NOW()) ${filterstr}) f
             ON DATE(c.CreateDate) = DATE(f.CreateDate)
                 LEFT JOIN
             (SELECT CreateDate, Id
              FROM Sa_Contract
              WHERE CreateDate BETWEEN DATE_FORMAT(NOW(), '%Y-%m-01') AND LAST_DAY(NOW()) ${filterstr}) ct
             ON DATE(c.CreateDate) = DATE(ct.CreateDate)
        GROUP BY DATE(c.CreateDate)
    </select>

    <select id="getCustomerType" resultType="java.util.Map">
        SELECT count(*) AS count, custtype
        FROM Sa_Customer
        where 1 = 1 ${qpfilter}
        GROUP BY CustType
    </select>


    <select id="getCustomerClass" resultType="java.util.Map">
        SELECT count(*) AS count, custclass
        FROM Sa_Customer
        GROUP BY custclass
    </select>

    <select id="getLeadIdsByPrincipalid" resultType="java.lang.String">
        select id
        from Sa_Leads
        where Principalid = #{userid}
          and CreateDate between #{queryParam.DateRange.StartDate} and #{queryParam.DateRange.EndDate}
    </select>

    <select id="getAllLeadIdsByPrincipalid" resultType="java.lang.String">
        select id
        from Sa_Leads
        where Principalid = #{userid}
    </select>
    <select id="getCustomerIdsByUserid" resultType="java.lang.String">
        select id
        from Sa_Customer where Leadsid in
        <foreach collection="lesdids" item="leadsid" open="(" separator="," close=")">
            #{leadsid}
        </foreach>
        and CreateDate between #{queryParam.DateRange.StartDate} and #{queryParam.DateRange.EndDate}
    </select>

    <select id="getAllCustomerIdsByUserid" resultType="java.lang.String">
        select id
        from Sa_Customer where Leadsid in
        <foreach collection="lesdids" item="leadsid" open="(" separator="," close=")">
            #{leadsid}
        </foreach>
    </select>

    <select id="getNewCountAndAmtThisMonth" resultType="java.util.Map">
        SELECT
        <if test="groupids == null or groupids.size() == 0">
            0   AS businesscount,
            0.0 AS businessamt,
            0   AS quotationcount,
            0.0 AS quotationamt,
            0   AS contractcount,
            0.0 AS contractamt
        </if>
        <if test="groupids != null and groupids.size() > 0">
            (SELECT COUNT(*)
            FROM Sa_Business
            WHERE CreateDate BETWEEN #{queryParam.DateRange.StartDate} AND #{queryParam.DateRange.EndDate}
            AND Sa_Business.Groupid IN

            <foreach collection="groupids" item="groupid" open="(" separator="," close=")">
                #{groupid}

            </foreach>

            ) AS businesscount,
            (SELECT ROUND(IFNULL(SUM(BillTaxAmount), 0), 4)
             FROM Sa_Business
            WHERE CreateDate BETWEEN #{queryParam.DateRange.StartDate} AND #{queryParam.DateRange.EndDate}
              AND Sa_Business.Groupid IN
            <foreach collection="groupids" item="groupid" open="(" separator="," close=")">
                #{groupid}
            </foreach>
            ) AS businessamt,

            (SELECT COUNT(*)
             FROM Sa_Quotation
            WHERE CreateDate BETWEEN #{queryParam.DateRange.StartDate} AND #{queryParam.DateRange.EndDate}
              AND Groupid IN
            <foreach collection="groupids" item="groupid" open="(" separator="," close=")">
                #{groupid}
            </foreach>
            ) AS quotationcount,

            (SELECT ROUND(IFNULL(SUM(BillTaxAmount), 0), 4)
             FROM Sa_Quotation
            WHERE CreateDate BETWEEN #{queryParam.DateRange.StartDate} AND #{queryParam.DateRange.EndDate}
              AND Groupid IN
            <foreach collection="groupids" item="groupid" open="(" separator="," close=")">
                #{groupid}
            </foreach>
            ) AS quotationamt,

            (SELECT COUNT(*)
             FROM Sa_Contract
            WHERE CreateDate BETWEEN #{queryParam.DateRange.StartDate} AND #{queryParam.DateRange.EndDate}
              AND Groupid IN
            <foreach collection="groupids" item="groupid" open="(" separator="," close=")">
                #{groupid}
            </foreach>
            ) AS contractcount,

            (SELECT ROUND(IFNULL(SUM(BillTaxAmount), 0), 4)
             FROM Sa_Contract
            WHERE CreateDate BETWEEN #{queryParam.DateRange.StartDate} AND #{queryParam.DateRange.EndDate}
              AND Groupid IN
            <foreach collection="groupids" item="groupid" open="(" separator="," close=")">
                #{groupid}
            </foreach>
            ) AS contractamt
        </if>
    </select>

    <!--    //1、第一列为状态;状态，就前端根据你返回的数据是否有回款id、发票id、报价id以及合同id判断-->
    <!--    //2、商机的内容:商机refno，客户编码，客户名称，商机时间，负责人;-->
    <!--    //3、相关信息:回访计划时间，样品需求单号，报价单号(最新)，报价金额(sum)，合同单号(最新)，合同金额(sum)，开票金额(sum)，开票结余(计算)，回款计划(sum)，回款金额(sum)，回款结余(计算)-->
    <select id="getBusinessInfo" resultType="java.util.Map">
        -- 最新样品需求计划
        WITH LatestSmpPlan AS (SELECT Sa_SmpPlan.*,
                                      ROW_NUMBER() OVER (PARTITION BY Sa_SmpPlan.Businessid ORDER BY Sa_SmpPlan.BillDate DESC) AS rn
                               FROM Sa_SmpPlan),
             -- 样品需求计划汇总
             SmpPlanSummary AS (SELECT Businessid,
                                       COUNT(*) AS TotalSmpPlanCount
                                FROM Sa_SmpPlan
                                GROUP BY Businessid),
             -- 最新报价单
             LatestQuotation AS (SELECT Sa_Quotation.*,
                                        ROW_NUMBER() OVER (PARTITION BY Sa_Quotation.Businessid ORDER BY Sa_Quotation.BillDate DESC) AS rn
                                 FROM Sa_Quotation),
             -- 报价单汇总
             QuotationSummary AS (SELECT Businessid,
                                         COUNT(*)           AS TotalQuotationCount,
                                         SUM(BillTaxAmount) AS TotalQuotationAmount
                                  FROM Sa_Quotation
                                  GROUP BY Businessid),
             -- 最新合同
             LatestContract AS (SELECT Sa_Contract.*,
                                       ROW_NUMBER() OVER (PARTITION BY Sa_Contract.Businessid ORDER BY Sa_Contract.BillDate DESC) AS rn
                                FROM Sa_Contract),
             -- 合同汇总
             ContractSummary AS (SELECT Businessid,
                                        SUM(Amount) AS TotalContractAmount
                                 FROM Sa_Contract
                                 GROUP BY Businessid),
             -- 发票汇总
             InvoiceSummary AS (SELECT Sa_Invoice.Businessid,
                                       SUM(Sa_Invoice.Amount) AS TotalInvoiceAmount
                                FROM Sa_Invoice
                                GROUP BY Sa_Invoice.Businessid),
             -- 回款计划汇总
             ReceiptPlanSummary AS (SELECT Sa_ReceiptPlan.Businessid,
                                           SUM(Sa_ReceiptPlan.Amount) AS TotalReceiptPlanAmount
                                    FROM Sa_ReceiptPlan
                                    GROUP BY Sa_ReceiptPlan.Businessid),
             -- 回款汇总
             ReceiptSummary AS (SELECT Sa_Receipt.Businessid,
                                       SUM(Sa_Receipt.Amount) AS TotalReceiptAmount
                                FROM Sa_Receipt
                                GROUP BY Sa_Receipt.Businessid)
        SELECT Sa_Business.id,
               Sa_Business.refno,                                                -- 商机编号
               Sa_Business.billtitle,                                            -- 商机标题
               Sa_Business.billdate,                                             -- 商机日期
               Sa_Business.operator,                                             -- 负责人
               Sa_Customer.custuid,                                              -- 客户编码
               Sa_Customer.custname,                                             -- 客户名称
               l_smp.RefNo                             AS latestsmpplanrefno,    -- 样品需求单号（最新）
               COALESCE(ss.TotalSmpPlanCount, 0)       AS totalsmpplancount,     -- 样品需求单个数
               l_quo.RefNo                             AS latestquotationrefno,  -- 报价单号（最新）
               COALESCE(qs.TotalQuotationCount, 0)     AS totalquotationcount,   -- 报价单个数
               COALESCE(qs.TotalQuotationAmount, 0)    AS totalquotationamount,  -- 报价金额（汇总）
               l_con.RefNo                             AS latestcontractrefno,   -- 合同单号（最新）
               COALESCE(cs.TotalContractAmount, 0)     AS totalcontractamount,   -- 合同金额（汇总）
               COALESCE(ins.TotalInvoiceAmount, 0)     AS totalinvoiceamount,    -- 开票金额（汇总）
               COALESCE(rs.TotalReceiptAmount, 0)      AS totalreceivedamount,   -- 回款金额（汇总）
               COALESCE(rps.TotalReceiptPlanAmount, 0) AS totalreceiptplanamount -- 回款计划金额（汇总）
        FROM Sa_Business
                 LEFT JOIN Sa_Customer ON Sa_Business.Groupid = Sa_Customer.ID
            -- 关联最新样品需求
                 LEFT JOIN LatestSmpPlan l_smp ON Sa_Business.id = l_smp.Businessid AND l_smp.rn = 1
            -- 关联样品需求汇总
                 LEFT JOIN SmpPlanSummary ss ON Sa_Business.id = ss.Businessid
            -- 关联最新报价单
                 LEFT JOIN LatestQuotation l_quo ON Sa_Business.id = l_quo.Businessid AND l_quo.rn = 1
            -- 关联报价单汇总
                 LEFT JOIN QuotationSummary qs ON Sa_Business.id = qs.Businessid
            -- 关联最新合同
                 LEFT JOIN LatestContract l_con ON Sa_Business.id = l_con.Businessid AND l_con.rn = 1
            -- 关联合同汇总
                 LEFT JOIN ContractSummary cs ON Sa_Business.id = cs.Businessid
            -- 关联发票汇总
                 LEFT JOIN InvoiceSummary ins ON Sa_Business.id = ins.Businessid
            -- 关联回款计划汇总
                 LEFT JOIN ReceiptPlanSummary rps ON Sa_Business.id = rps.Businessid
            -- 关联回款汇总
                 LEFT JOIN ReceiptSummary rs ON Sa_Business.id = rs.Businessid
        WHERE 1 = 1
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_Business.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        order by ${orderBy}
    </select>
    
    <!-- 查询指定客户的：
累计合同(个) 累计合同额（元）累计回款额（元）
未开票(元) 已开票额(元) -->
    <select id="getContractStatistics" resultType="java.util.Map">
        SELECT
        -- 累计合同额
        COALESCE((
        SELECT SUM(Amount)
        FROM Sa_Contract
        WHERE Groupid = #{groupid}
        ), 0) AS contractamount,

        -- 累计合同数
        (
        SELECT COUNT(*)
        FROM Sa_Contract
        WHERE Groupid = #{groupid}
        ) AS contractcount,

        -- 累计回款额
        COALESCE((
        SELECT SUM(Amount)
        FROM Sa_Receipt
        WHERE Groupid = #{groupid}
        ), 0) AS receiptamount,

        -- 已开票额
        COALESCE((
        SELECT SUM(Amount)
        FROM Sa_Invoice
        WHERE Groupid = #{groupid}
        ), 0) AS invoiceamount
    </select>

</mapper>

