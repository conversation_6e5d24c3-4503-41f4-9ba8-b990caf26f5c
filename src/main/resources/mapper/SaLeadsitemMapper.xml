<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.crm.mapper.SaLeadsitemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.crm.domain.pojo.SaLeadsitemPojo">
        select
          id, Pid, ItemType, ItemContent, PhotoUrl1, PhotoUrl2, PhotoUrl3, PhotoName1, PhotoName2, PhotoName3, RowNum, NextDate, Remark, Lister, CreateDate, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision        from Sa_LeadsItem
        where Sa_LeadsItem.id = #{key} 
    </select>
    <sql id="selectSaLeadsitemVo">
         select
          id, Pid, ItemType, ItemContent, PhotoUrl1, PhotoUrl2, PhotoUrl3, PhotoName1, PhotoName2, PhotoName3, RowNum, NextDate, Remark, Lister, CreateDate, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision        from Sa_LeadsItem
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.crm.domain.pojo.SaLeadsitemPojo">
        <include refid="selectSaLeadsitemVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
              <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_LeadsItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   and Sa_LeadsItem.pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.itemtype != null and SearchPojo.itemtype != ''">
   and Sa_LeadsItem.itemtype like concat('%', #{SearchPojo.itemtype}, '%')
</if>
<if test="SearchPojo.itemcontent != null and SearchPojo.itemcontent != ''">
   and Sa_LeadsItem.itemcontent like concat('%', #{SearchPojo.itemcontent}, '%')
</if>
<if test="SearchPojo.photourl1 != null and SearchPojo.photourl1 != ''">
   and Sa_LeadsItem.photourl1 like concat('%', #{SearchPojo.photourl1}, '%')
</if>
<if test="SearchPojo.photourl2 != null and SearchPojo.photourl2 != ''">
   and Sa_LeadsItem.photourl2 like concat('%', #{SearchPojo.photourl2}, '%')
</if>
<if test="SearchPojo.photourl3 != null and SearchPojo.photourl3 != ''">
   and Sa_LeadsItem.photourl3 like concat('%', #{SearchPojo.photourl3}, '%')
</if>
<if test="SearchPojo.photoname1 != null and SearchPojo.photoname1 != ''">
   and Sa_LeadsItem.photoname1 like concat('%', #{SearchPojo.photoname1}, '%')
</if>
<if test="SearchPojo.photoname2 != null and SearchPojo.photoname2 != ''">
   and Sa_LeadsItem.photoname2 like concat('%', #{SearchPojo.photoname2}, '%')
</if>
<if test="SearchPojo.photoname3 != null and SearchPojo.photoname3 != ''">
   and Sa_LeadsItem.photoname3 like concat('%', #{SearchPojo.photoname3}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   and Sa_LeadsItem.remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.lister != null and SearchPojo.lister != ''">
   and Sa_LeadsItem.lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   and Sa_LeadsItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   and Sa_LeadsItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   and Sa_LeadsItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   and Sa_LeadsItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   and Sa_LeadsItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
   and Sa_LeadsItem.custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
   and Sa_LeadsItem.custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
   and Sa_LeadsItem.custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
   and Sa_LeadsItem.custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
   and Sa_LeadsItem.custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null and SearchPojo.tenantname != ''">
   and Sa_LeadsItem.tenantname like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   or Sa_LeadsItem.Pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.itemtype != null and SearchPojo.itemtype != ''">
   or Sa_LeadsItem.ItemType like concat('%', #{SearchPojo.itemtype}, '%')
</if>
<if test="SearchPojo.itemcontent != null and SearchPojo.itemcontent != ''">
   or Sa_LeadsItem.ItemContent like concat('%', #{SearchPojo.itemcontent}, '%')
</if>
<if test="SearchPojo.photourl1 != null and SearchPojo.photourl1 != ''">
   or Sa_LeadsItem.PhotoUrl1 like concat('%', #{SearchPojo.photourl1}, '%')
</if>
<if test="SearchPojo.photourl2 != null and SearchPojo.photourl2 != ''">
   or Sa_LeadsItem.PhotoUrl2 like concat('%', #{SearchPojo.photourl2}, '%')
</if>
<if test="SearchPojo.photourl3 != null and SearchPojo.photourl3 != ''">
   or Sa_LeadsItem.PhotoUrl3 like concat('%', #{SearchPojo.photourl3}, '%')
</if>
<if test="SearchPojo.photoname1 != null and SearchPojo.photoname1 != ''">
   or Sa_LeadsItem.PhotoName1 like concat('%', #{SearchPojo.photoname1}, '%')
</if>
<if test="SearchPojo.photoname2 != null and SearchPojo.photoname2 != ''">
   or Sa_LeadsItem.PhotoName2 like concat('%', #{SearchPojo.photoname2}, '%')
</if>
<if test="SearchPojo.photoname3 != null and SearchPojo.photoname3 != ''">
   or Sa_LeadsItem.PhotoName3 like concat('%', #{SearchPojo.photoname3}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   or Sa_LeadsItem.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.lister != null and SearchPojo.lister != ''">
   or Sa_LeadsItem.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   or Sa_LeadsItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   or Sa_LeadsItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   or Sa_LeadsItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   or Sa_LeadsItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   or Sa_LeadsItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
   or Sa_LeadsItem.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
   or Sa_LeadsItem.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
   or Sa_LeadsItem.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
   or Sa_LeadsItem.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
   or Sa_LeadsItem.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null and SearchPojo.tenantname != ''">
   or Sa_LeadsItem.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
     
         <!--查询List-->
    <select id="getList" resultType="inks.service.sa.crm.domain.pojo.SaLeadsitemPojo">
        select
          id, Pid, ItemType, ItemContent, PhotoUrl1, PhotoUrl2, PhotoUrl3, PhotoName1, PhotoName2, PhotoName3, RowNum, NextDate, Remark, Lister, CreateDate, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision        from Sa_LeadsItem
        where Sa_LeadsItem.Pid = #{Pid} 
        order by RowNum 
    </select>
    
    <!--新增所有列-->
    <insert id="insert" >
        insert into Sa_LeadsItem(id, Pid, ItemType, ItemContent, PhotoUrl1, PhotoUrl2, PhotoUrl3, PhotoName1, PhotoName2, PhotoName3, RowNum, NextDate, Remark, Lister, CreateDate, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{pid}, #{itemtype}, #{itemcontent}, #{photourl1}, #{photourl2}, #{photourl3}, #{photoname1}, #{photoname2}, #{photoname3}, #{rownum}, #{nextdate}, #{remark}, #{lister}, #{createdate}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_LeadsItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="itemtype != null ">
                ItemType = #{itemtype},
            </if>
            <if test="itemcontent != null ">
                ItemContent = #{itemcontent},
            </if>
            <if test="photourl1 != null ">
                PhotoUrl1 = #{photourl1},
            </if>
            <if test="photourl2 != null ">
                PhotoUrl2 = #{photourl2},
            </if>
            <if test="photourl3 != null ">
                PhotoUrl3 = #{photourl3},
            </if>
            <if test="photoname1 != null ">
                PhotoName1 = #{photoname1},
            </if>
            <if test="photoname2 != null ">
                PhotoName2 = #{photoname2},
            </if>
            <if test="photoname3 != null ">
                PhotoName3 = #{photoname3},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="nextdate != null">
                NextDate = #{nextdate},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="lister != null ">
                Lister = #{lister},
            </if>
            <if test="createdate != null">
                CreateDate = #{createdate},
            </if>
            <if test="modifydate != null">
                ModifyDate = #{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 = #{custom10},
            </if>
            <if test="tenantname != null ">
                TenantName = #{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_LeadsItem where id = #{key} 
    </delete>

</mapper>

