<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.crm.mapper.SaDemandresearchMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.crm.domain.pojo.SaDemandresearchPojo">
        <include refid="selectbillVo"/>
        where Sa_DemandResearch.id = #{key} 
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
         select
id, RefNo, BillType, BillTitle, BillDate, Groupid, GroupName, Description, RelatedService, ExpectedDate, Assessor, Assessorid, AssessDate, RowNum, Remark, CreateBy, CreateByid, <PERSON>reate<PERSON><PERSON>, <PERSON>er, <PERSON><PERSON><PERSON>, <PERSON>difyDate, <PERSON>anti<PERSON>, TenantName, Revision        from Sa_DemandResearch
    </sql>
    <sql id="selectdetailVo">
        select Sa_DemandResearchItem.id,
               Sa_DemandResearchItem.Pid,
               Sa_DemandResearchItem.DemandDictid,
               Sa_DemandResearchItem.Description,
               Sa_DemandResearchItem.MainMark,
               Sa_DemandResearchItem.Level,
               Sa_DemandResearchItem.RequirementDate,
               Sa_DemandResearchItem.Difficulty,
               Sa_DemandResearchItem.Solution,
               Sa_DemandResearchItem.RowNum,
               Sa_DemandResearchItem.Remark,
               Sa_DemandResearchItem.Tenantid,
               Sa_DemandResearchItem.Revision,
               Sa_DemandResearch.RefNo,
               Sa_DemandResearch.BillType,
               Sa_DemandResearch.BillTitle,
               Sa_DemandResearch.BillDate,
               Sa_DemandResearch.Groupid,
               Sa_DemandResearch.GroupName,
               Sa_DemandResearch.CreateBy,
               Sa_DemandResearch.Lister,
               Sa_DemandResearch.Assessor,
               Sa_Customer.CustName as groupname
        from Sa_DemandResearchItem
        LEFT JOIN Sa_DemandResearch on Sa_DemandResearch.id = Sa_DemandResearchItem.Pid
        LEFT JOIN Sa_Customer on Sa_DemandResearch.Groupid = Sa_Customer.id
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.crm.domain.pojo.SaDemandresearchitemdetailPojo">
        <include refid="selectdetailVo"/>
         where 1 = 1 
       <if test="filterstr != null ">
            ${filterstr}
        </if> 
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_DemandResearch.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.refno != null ">
   and Sa_DemandResearch.refno like concat('%', #{SearchPojo.refno}, '%')
</if>
<if test="SearchPojo.billtype != null ">
   and Sa_DemandResearch.billtype like concat('%', #{SearchPojo.billtype}, '%')
</if>
<if test="SearchPojo.billtitle != null ">
   and Sa_DemandResearch.billtitle like concat('%', #{SearchPojo.billtitle}, '%')
</if>
<if test="SearchPojo.groupid != null ">
   and Sa_DemandResearch.groupid like concat('%', #{SearchPojo.groupid}, '%')
</if>
<if test="SearchPojo.groupname != null ">
   and Sa_DemandResearch.groupname like concat('%', #{SearchPojo.groupname}, '%')
</if>
<if test="SearchPojo.description != null ">
   and Sa_DemandResearch.description like concat('%', #{SearchPojo.description}, '%')
</if>
<if test="SearchPojo.relatedservice != null ">
   and Sa_DemandResearch.relatedservice like concat('%', #{SearchPojo.relatedservice}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   and Sa_DemandResearch.assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   and Sa_DemandResearch.assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Sa_DemandResearch.remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Sa_DemandResearch.createby like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Sa_DemandResearch.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Sa_DemandResearch.lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Sa_DemandResearch.listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Sa_DemandResearch.tenantname like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.refno != null ">
   or Sa_DemandResearch.RefNo like concat('%', #{SearchPojo.refno}, '%')
</if>
<if test="SearchPojo.billtype != null ">
   or Sa_DemandResearch.BillType like concat('%', #{SearchPojo.billtype}, '%')
</if>
<if test="SearchPojo.billtitle != null ">
   or Sa_DemandResearch.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
</if>
<if test="SearchPojo.groupid != null ">
   or Sa_DemandResearch.Groupid like concat('%', #{SearchPojo.groupid}, '%')
</if>
<if test="SearchPojo.groupname != null ">
   or Sa_DemandResearch.GroupName like concat('%', #{SearchPojo.groupname}, '%')
</if>
<if test="SearchPojo.description != null ">
   or Sa_DemandResearch.Description like concat('%', #{SearchPojo.description}, '%')
</if>
<if test="SearchPojo.relatedservice != null ">
   or Sa_DemandResearch.RelatedService like concat('%', #{SearchPojo.relatedservice}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   or Sa_DemandResearch.Assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   or Sa_DemandResearch.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Sa_DemandResearch.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Sa_DemandResearch.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Sa_DemandResearch.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Sa_DemandResearch.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Sa_DemandResearch.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Sa_DemandResearch.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
     
         <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.crm.domain.pojo.SaDemandresearchPojo">
        <include refid="selectbillVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_DemandResearch.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="thand"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="thor"></include>        
         </if>
        </if> 
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="thand">
<if test="SearchPojo.refno != null ">
   and Sa_DemandResearch.RefNo like concat('%', #{SearchPojo.refno}, '%')
</if>
<if test="SearchPojo.billtype != null ">
   and Sa_DemandResearch.BillType like concat('%', #{SearchPojo.billtype}, '%')
</if>
<if test="SearchPojo.billtitle != null ">
   and Sa_DemandResearch.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
</if>
<if test="SearchPojo.groupid != null ">
   and Sa_DemandResearch.Groupid like concat('%', #{SearchPojo.groupid}, '%')
</if>
<if test="SearchPojo.groupname != null ">
   and Sa_DemandResearch.GroupName like concat('%', #{SearchPojo.groupname}, '%')
</if>
<if test="SearchPojo.description != null ">
   and Sa_DemandResearch.Description like concat('%', #{SearchPojo.description}, '%')
</if>
<if test="SearchPojo.relatedservice != null ">
   and Sa_DemandResearch.RelatedService like concat('%', #{SearchPojo.relatedservice}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   and Sa_DemandResearch.Assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   and Sa_DemandResearch.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Sa_DemandResearch.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Sa_DemandResearch.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Sa_DemandResearch.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Sa_DemandResearch.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Sa_DemandResearch.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Sa_DemandResearch.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="thor">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.refno != null ">
   or Sa_DemandResearch.RefNo like concat('%', #{SearchPojo.refno}, '%')
</if>
<if test="SearchPojo.billtype != null ">
   or Sa_DemandResearch.BillType like concat('%', #{SearchPojo.billtype}, '%')
</if>
<if test="SearchPojo.billtitle != null ">
   or Sa_DemandResearch.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
</if>
<if test="SearchPojo.groupid != null ">
   or Sa_DemandResearch.Groupid like concat('%', #{SearchPojo.groupid}, '%')
</if>
<if test="SearchPojo.groupname != null ">
   or Sa_DemandResearch.GroupName like concat('%', #{SearchPojo.groupname}, '%')
</if>
<if test="SearchPojo.description != null ">
   or Sa_DemandResearch.Description like concat('%', #{SearchPojo.description}, '%')
</if>
<if test="SearchPojo.relatedservice != null ">
   or Sa_DemandResearch.RelatedService like concat('%', #{SearchPojo.relatedservice}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   or Sa_DemandResearch.Assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   or Sa_DemandResearch.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Sa_DemandResearch.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Sa_DemandResearch.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Sa_DemandResearch.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Sa_DemandResearch.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Sa_DemandResearch.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Sa_DemandResearch.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
     
    <!--新增所有列-->
    <insert id="insert" >
        insert into Sa_DemandResearch(id, RefNo, BillType, BillTitle, BillDate, Groupid, GroupName, Description, RelatedService, ExpectedDate, Assessor, Assessorid, AssessDate, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Tenantid, TenantName, Revision)
        values (#{id}, #{refno}, #{billtype}, #{billtitle}, #{billdate}, #{groupid}, #{groupname}, #{description}, #{relatedservice}, #{expecteddate}, #{assessor}, #{assessorid}, #{assessdate}, #{rownum}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{tenantid}, #{tenantname}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_DemandResearch
        <set>
            <if test="refno != null ">
                RefNo =#{refno},
            </if>
            <if test="billtype != null ">
                BillType =#{billtype},
            </if>
            <if test="billtitle != null ">
                BillTitle =#{billtitle},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="groupid != null ">
                Groupid =#{groupid},
            </if>
            <if test="groupname != null ">
                GroupName =#{groupname},
            </if>
            <if test="description != null ">
                Description =#{description},
            </if>
            <if test="relatedservice != null ">
                RelatedService =#{relatedservice},
            </if>
            <if test="expecteddate != null">
                ExpectedDate =#{expecteddate},
            </if>
            <if test="assessor != null ">
                Assessor =#{assessor},
            </if>
            <if test="assessorid != null ">
                Assessorid =#{assessorid},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_DemandResearch where id = #{key} 
    </delete>
    <!--通过主键审核数据-->
    <update id="approval">
        update Sa_DemandResearch SET
            Assessor = #{assessor},
            Assessorid = #{assessorid},
            AssessDate = #{assessdate},
            Revision=Revision+1
        where id = #{id}
    </update>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String" parameterType="inks.service.sa.crm.domain.pojo.SaDemandresearchPojo">
        select
          id
        from Sa_DemandResearchItem
        where Pid = #{id}
        <if test="item !=null and item.size()>0">
         and id not in
        <foreach collection="item" open="(" close=")" separator="," item="item">
            <if test="item.id != null">
                #{item.id}
            </if>
            <if test="item.id == null">
                 ''
            </if>
        </foreach>
         </if>
    </select>

</mapper>

