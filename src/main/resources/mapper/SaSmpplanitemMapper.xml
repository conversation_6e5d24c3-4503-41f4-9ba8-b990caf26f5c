<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.crm.mapper.SaSmpplanitemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.crm.domain.pojo.SaSmpplanitemPojo">
        <include refid="selectSaSmpplanitemVo"/>
        where Sa_SmpPlanItem.id = #{key} 
    </select>
    <sql id="selectSaSmpplanitemVo">
        select id,
               Pid,
               Goodsid,
               ItemCode,
               ItemName,
               ItemSpec,
               ItemUnit,
               Quantity,
               Price,
               Amount,
               StartDate,
               PlanDate,
               StartQty,
               FinishQty,
               MrbQty,
               StartSecQty,
               EnabledMark,
               Closed,
               Remark,
               StateCode,
               StateDate,
               <PERSON><PERSON><PERSON>,
               <PERSON>er,
               <PERSON><PERSON><PERSON>,
               <PERSON><PERSON><PERSON><PERSON>,
               <PERSON><PERSON>,
               <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
               Attri<PERSON>eS<PERSON>,
               StartRate,
               FinishRate,
               WkWpid,
               WkWpCode,
               WkWpName,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Custom9,
               Custom10,
               Tenantid,
               Revision
        from Sa_SmpPlanItem
    </sql>
         <!--查询List-->
    <select id="getList" resultType="inks.service.sa.crm.domain.pojo.SaSmpplanitemPojo">
        <include refid="selectSaSmpplanitemVo"/>
        where Sa_SmpPlanItem.Pid = #{Pid}
        order by RowNum 
    </select>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.crm.domain.pojo.SaSmpplanitemPojo">
        <include refid="selectSaSmpplanitemVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
              <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_SmpPlanItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   and Sa_SmpPlanItem.pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
   and Sa_SmpPlanItem.goodsid like concat('%', #{SearchPojo.goodsid}, '%')
</if>
<if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
   and Sa_SmpPlanItem.itemcode like concat('%', #{SearchPojo.itemcode}, '%')
</if>
<if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
   and Sa_SmpPlanItem.itemname like concat('%', #{SearchPojo.itemname}, '%')
</if>
<if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
   and Sa_SmpPlanItem.itemspec like concat('%', #{SearchPojo.itemspec}, '%')
</if>
<if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
   and Sa_SmpPlanItem.itemunit like concat('%', #{SearchPojo.itemunit}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   and Sa_SmpPlanItem.remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.statecode != null and SearchPojo.statecode != ''">
   and Sa_SmpPlanItem.statecode like concat('%', #{SearchPojo.statecode}, '%')
</if>
<if test="SearchPojo.customer != null and SearchPojo.customer != ''">
   and Sa_SmpPlanItem.customer like concat('%', #{SearchPojo.customer}, '%')
</if>
<if test="SearchPojo.custpo != null and SearchPojo.custpo != ''">
   and Sa_SmpPlanItem.custpo like concat('%', #{SearchPojo.custpo}, '%')
</if>
<if test="SearchPojo.mrpuid != null and SearchPojo.mrpuid != ''">
   and Sa_SmpPlanItem.mrpuid like concat('%', #{SearchPojo.mrpuid}, '%')
</if>
<if test="SearchPojo.mrpid != null and SearchPojo.mrpid != ''">
   and Sa_SmpPlanItem.mrpid like concat('%', #{SearchPojo.mrpid}, '%')
</if>
<if test="SearchPojo.attributejson != null and SearchPojo.attributejson != ''">
   and Sa_SmpPlanItem.attributejson like concat('%', #{SearchPojo.attributejson}, '%')
</if>
<if test="SearchPojo.wkwpid != null and SearchPojo.wkwpid != ''">
   and Sa_SmpPlanItem.wkwpid like concat('%', #{SearchPojo.wkwpid}, '%')
</if>
<if test="SearchPojo.wkwpcode != null and SearchPojo.wkwpcode != ''">
   and Sa_SmpPlanItem.wkwpcode like concat('%', #{SearchPojo.wkwpcode}, '%')
</if>
<if test="SearchPojo.wkwpname != null and SearchPojo.wkwpname != ''">
   and Sa_SmpPlanItem.wkwpname like concat('%', #{SearchPojo.wkwpname}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   and Sa_SmpPlanItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   and Sa_SmpPlanItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   and Sa_SmpPlanItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   and Sa_SmpPlanItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   and Sa_SmpPlanItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
   and Sa_SmpPlanItem.custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
   and Sa_SmpPlanItem.custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
   and Sa_SmpPlanItem.custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
   and Sa_SmpPlanItem.custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
   and Sa_SmpPlanItem.custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   or Sa_SmpPlanItem.Pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
   or Sa_SmpPlanItem.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
</if>
<if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
   or Sa_SmpPlanItem.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
</if>
<if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
   or Sa_SmpPlanItem.ItemName like concat('%', #{SearchPojo.itemname}, '%')
</if>
<if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
   or Sa_SmpPlanItem.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
</if>
<if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
   or Sa_SmpPlanItem.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   or Sa_SmpPlanItem.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.statecode != null and SearchPojo.statecode != ''">
   or Sa_SmpPlanItem.StateCode like concat('%', #{SearchPojo.statecode}, '%')
</if>
<if test="SearchPojo.customer != null and SearchPojo.customer != ''">
   or Sa_SmpPlanItem.Customer like concat('%', #{SearchPojo.customer}, '%')
</if>
<if test="SearchPojo.custpo != null and SearchPojo.custpo != ''">
   or Sa_SmpPlanItem.CustPO like concat('%', #{SearchPojo.custpo}, '%')
</if>
<if test="SearchPojo.mrpuid != null and SearchPojo.mrpuid != ''">
   or Sa_SmpPlanItem.MrpUid like concat('%', #{SearchPojo.mrpuid}, '%')
</if>
<if test="SearchPojo.mrpid != null and SearchPojo.mrpid != ''">
   or Sa_SmpPlanItem.Mrpid like concat('%', #{SearchPojo.mrpid}, '%')
</if>
<if test="SearchPojo.attributejson != null and SearchPojo.attributejson != ''">
   or Sa_SmpPlanItem.AttributeJson like concat('%', #{SearchPojo.attributejson}, '%')
</if>
<if test="SearchPojo.wkwpid != null and SearchPojo.wkwpid != ''">
   or Sa_SmpPlanItem.WkWpid like concat('%', #{SearchPojo.wkwpid}, '%')
</if>
<if test="SearchPojo.wkwpcode != null and SearchPojo.wkwpcode != ''">
   or Sa_SmpPlanItem.WkWpCode like concat('%', #{SearchPojo.wkwpcode}, '%')
</if>
<if test="SearchPojo.wkwpname != null and SearchPojo.wkwpname != ''">
   or Sa_SmpPlanItem.WkWpName like concat('%', #{SearchPojo.wkwpname}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   or Sa_SmpPlanItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   or Sa_SmpPlanItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   or Sa_SmpPlanItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   or Sa_SmpPlanItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   or Sa_SmpPlanItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
   or Sa_SmpPlanItem.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
   or Sa_SmpPlanItem.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
   or Sa_SmpPlanItem.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
   or Sa_SmpPlanItem.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
   or Sa_SmpPlanItem.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
</trim>
     </sql>

    
    <!--新增所有列-->
    <insert id="insert" >
        insert into Sa_SmpPlanItem(id, Pid, Goodsid, ItemCode, ItemName, ItemSpec, ItemUnit, Quantity, Price, Amount, StartDate, PlanDate, StartQty, FinishQty, MrbQty, StartSecQty, EnabledMark, Closed, Remark, StateCode, StateDate, RowNum, Customer, CustPO, MrpUid, Mrpid, AttributeJson, AttributeStr, StartRate, FinishRate, WkWpid, WkWpCode, WkWpName, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision)
        values (#{id}, #{pid}, #{goodsid}, #{itemcode}, #{itemname}, #{itemspec}, #{itemunit}, #{quantity}, #{price}, #{amount}, #{startdate}, #{plandate}, #{startqty}, #{finishqty}, #{mrbqty}, #{startsecqty}, #{enabledmark}, #{closed}, #{remark}, #{statecode}, #{statedate}, #{rownum}, #{customer}, #{custpo}, #{mrpuid}, #{mrpid}, #{attributejson}, #{attributestr}, #{startrate}, #{finishrate}, #{wkwpid}, #{wkwpcode}, #{wkwpname}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_SmpPlanItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="goodsid != null ">
                Goodsid = #{goodsid},
            </if>
            <if test="itemcode != null ">
                ItemCode = #{itemcode},
            </if>
            <if test="itemname != null ">
                ItemName = #{itemname},
            </if>
            <if test="itemspec != null ">
                ItemSpec = #{itemspec},
            </if>
            <if test="itemunit != null ">
                ItemUnit = #{itemunit},
            </if>
            <if test="quantity != null">
                Quantity = #{quantity},
            </if>
            <if test="price != null">
                Price = #{price},
            </if>
            <if test="amount != null">
                Amount = #{amount},
            </if>
            <if test="startdate != null">
                StartDate = #{startdate},
            </if>
            <if test="plandate != null">
                PlanDate = #{plandate},
            </if>
            <if test="startqty != null">
                StartQty = #{startqty},
            </if>
            <if test="finishqty != null">
                FinishQty = #{finishqty},
            </if>
            <if test="mrbqty != null">
                MrbQty = #{mrbqty},
            </if>
            <if test="startsecqty != null">
                StartSecQty = #{startsecqty},
            </if>
            <if test="enabledmark != null">
                EnabledMark = #{enabledmark},
            </if>
            <if test="closed != null">
                Closed = #{closed},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="statecode != null ">
                StateCode = #{statecode},
            </if>
            <if test="statedate != null">
                StateDate = #{statedate},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="customer != null ">
                Customer = #{customer},
            </if>
            <if test="custpo != null ">
                CustPO = #{custpo},
            </if>
            <if test="mrpuid != null ">
                MrpUid = #{mrpuid},
            </if>
            <if test="mrpid != null ">
                Mrpid = #{mrpid},
            </if>
            <if test="attributejson != null ">
                AttributeJson = #{attributejson},
            </if>
            <if test="attributestr != null ">
                AttributeStr = #{attributestr},
            </if>
            <if test="startrate != null">
                StartRate = #{startrate},
            </if>
            <if test="finishrate != null">
                FinishRate = #{finishrate},
            </if>
            <if test="wkwpid != null ">
                WkWpid = #{wkwpid},
            </if>
            <if test="wkwpcode != null ">
                WkWpCode = #{wkwpcode},
            </if>
            <if test="wkwpname != null ">
                WkWpName = #{wkwpname},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 = #{custom10},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_SmpPlanItem where id = #{key} 
    </delete>

</mapper>

