<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.crm.mapper.SaBusinessMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.crm.domain.pojo.SaBusinessPojo">
        <include refid="selectbillVo"/>
        where Sa_Business.id = #{key}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        select Sa_Business.id,
               Sa_Business.RefNo,
               Sa_Business.BillType,
               Sa_Business.BillTitle,
               Sa_Business.BillDate,
               Sa_Business.BillAmount,
               Sa_Business.BillTaxAmount,
               Sa_Business.BillTaxTotal,
               Sa_Business.ContractAmount,
               Sa_Business.Groupid,
               Sa_Business.Address,
               Sa_Business.Linkman,
               Sa_Business.Tel,
               Sa_Business.Fax,
               Sa_Business.Reporter,
               Sa_Business.Source,
               Sa_Business.BusinessType,
               Sa_Business.SalesType,
               Sa_Business.SalesProgress,
               Sa_Business.EstimatedAmt,
               Sa_Business.EstimatedTime,
               Sa_Business.Marketing,
               Sa_Business.Status,
               Sa_Business.WorkStage,
               Sa_Business.Stageid,
               Sa_Business.StageText,
               Sa_Business.StageResult,
               Sa_Business.Operatorid,
               Sa_Business.Operator,
               Sa_Business.Summary,
               Sa_Business.CreateBy,
               Sa_Business.CreateByid,
               Sa_Business.CreateDate,
               Sa_Business.Lister,
               Sa_Business.Listerid,
               Sa_Business.ModifyDate,
               Sa_Business.Assessor,
               Sa_Business.Assessorid,
               Sa_Business.AssessDate,
               Sa_Business.OaFlowMark,
               Sa_Business.StateCode,
               Sa_Business.StateDate,
               Sa_Business.Custom1,
               Sa_Business.Custom2,
               Sa_Business.Custom3,
               Sa_Business.Custom4,
               Sa_Business.Custom5,
               Sa_Business.Custom6,
               Sa_Business.Custom7,
               Sa_Business.Custom8,
               Sa_Business.Custom9,
               Sa_Business.Custom10,
               Sa_Business.Deptid,
               Sa_Business.Tenantid,
               Sa_Business.TenantName,
               Sa_Business.Revision,
               Sa_Customer.CustName,
               Sa_Customer.CustName AS GroupName,
               Sa_BusinessStage.StageName
        from Sa_Business
                 Left Join Sa_Customer on Sa_Business.Groupid = Sa_Customer.id
                 Left Join Sa_BusinessStage on Sa_Business.Stageid = Sa_BusinessStage.id
    </sql>
    <sql id="selectdetailVo">
        select Sa_BusinessItem.*,
               Sa_Business.RefNo,
               Sa_Business.BillType,
               Sa_Business.BillTitle,
               Sa_Business.BillDate,
               Sa_Customer.CustName,
               Sa_Customer.CustName AS GroupName
        from Sa_Business
                 Left Join Sa_Customer on Sa_Business.Groupid = Sa_Customer.id
                 Left Join Sa_BusinessItem on Sa_Business.id = Sa_BusinessItem.Pid
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.crm.domain.pojo.SaBusinessitemdetailPojo">
        <include refid="selectdetailVo"/>
         where 1 = 1 
       <if test="filterstr != null ">
            ${filterstr}
        </if> 
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_Business.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.refno != null ">
   and Sa_Business.refno like concat('%', #{SearchPojo.refno}, '%')
</if>
<if test="SearchPojo.billtype != null ">
   and Sa_Business.billtype like concat('%', #{SearchPojo.billtype}, '%')
</if>
<if test="SearchPojo.billtitle != null ">
   and Sa_Business.billtitle like concat('%', #{SearchPojo.billtitle}, '%')
</if>
<if test="SearchPojo.groupid != null ">
   and Sa_Business.groupid like concat('%', #{SearchPojo.groupid}, '%')
</if>
<if test="SearchPojo.address != null ">
   and Sa_Business.address like concat('%', #{SearchPojo.address}, '%')
</if>
<if test="SearchPojo.linkman != null ">
   and Sa_Business.linkman like concat('%', #{SearchPojo.linkman}, '%')
</if>
<if test="SearchPojo.tel != null ">
   and Sa_Business.tel like concat('%', #{SearchPojo.tel}, '%')
</if>
<if test="SearchPojo.fax != null ">
   and Sa_Business.fax like concat('%', #{SearchPojo.fax}, '%')
</if>
<if test="SearchPojo.reporter != null ">
   and Sa_Business.reporter like concat('%', #{SearchPojo.reporter}, '%')
</if>
<if test="SearchPojo.source != null ">
   and Sa_Business.source like concat('%', #{SearchPojo.source}, '%')
</if>
<if test="SearchPojo.businesstype != null ">
   and Sa_Business.businesstype like concat('%', #{SearchPojo.businesstype}, '%')
</if>
<if test="SearchPojo.salestype != null ">
   and Sa_Business.salestype like concat('%', #{SearchPojo.salestype}, '%')
</if>
<if test="SearchPojo.salesprogress != null ">
   and Sa_Business.salesprogress like concat('%', #{SearchPojo.salesprogress}, '%')
</if>
<if test="SearchPojo.estimatedamt != null ">
   and Sa_Business.estimatedamt like concat('%', #{SearchPojo.estimatedamt}, '%')
</if>
<if test="SearchPojo.marketing != null ">
   and Sa_Business.marketing like concat('%', #{SearchPojo.marketing}, '%')
</if>
<if test="SearchPojo.status != null ">
   and Sa_Business.status like concat('%', #{SearchPojo.status}, '%')
</if>
<if test="SearchPojo.workstage != null ">
   and Sa_Business.workstage like concat('%', #{SearchPojo.workstage}, '%')
</if>
<if test="SearchPojo.stageid != null ">
   and Sa_Business.stageid like concat('%', #{SearchPojo.stageid}, '%')
</if>
<if test="SearchPojo.stagetext != null ">
   and Sa_Business.stagetext like concat('%', #{SearchPojo.stagetext}, '%')
</if>
<if test="SearchPojo.operatorid != null ">
   and Sa_Business.operatorid like concat('%', #{SearchPojo.operatorid}, '%')
</if>
<if test="SearchPojo.operator != null ">
   and Sa_Business.operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.summary != null ">
   and Sa_Business.summary like concat('%', #{SearchPojo.summary}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Sa_Business.createby like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Sa_Business.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Sa_Business.lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Sa_Business.listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   and Sa_Business.assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   and Sa_Business.assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.statecode != null ">
   and Sa_Business.statecode like concat('%', #{SearchPojo.statecode}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Sa_Business.custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Sa_Business.custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Sa_Business.custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Sa_Business.custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Sa_Business.custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and Sa_Business.custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and Sa_Business.custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and Sa_Business.custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   and Sa_Business.custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   and Sa_Business.custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.deptid != null ">
   and Sa_Business.deptid like concat('%', #{SearchPojo.deptid}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Sa_Business.tenantname like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.refno != null ">
   or Sa_Business.RefNo like concat('%', #{SearchPojo.refno}, '%')
</if>
      <if test="SearchPojo.custname != null ">
          or Sa_Customer.CustName like concat('%', #{SearchPojo.custname}, '%')
      </if>
<if test="SearchPojo.billtype != null ">
   or Sa_Business.BillType like concat('%', #{SearchPojo.billtype}, '%')
</if>
<if test="SearchPojo.billtitle != null ">
   or Sa_Business.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
</if>
<if test="SearchPojo.groupid != null ">
   or Sa_Business.Groupid like concat('%', #{SearchPojo.groupid}, '%')
</if>
<if test="SearchPojo.address != null ">
   or Sa_Business.Address like concat('%', #{SearchPojo.address}, '%')
</if>
<if test="SearchPojo.linkman != null ">
   or Sa_Business.Linkman like concat('%', #{SearchPojo.linkman}, '%')
</if>
<if test="SearchPojo.tel != null ">
   or Sa_Business.Tel like concat('%', #{SearchPojo.tel}, '%')
</if>
<if test="SearchPojo.fax != null ">
   or Sa_Business.Fax like concat('%', #{SearchPojo.fax}, '%')
</if>
<if test="SearchPojo.reporter != null ">
   or Sa_Business.Reporter like concat('%', #{SearchPojo.reporter}, '%')
</if>
<if test="SearchPojo.source != null ">
   or Sa_Business.Source like concat('%', #{SearchPojo.source}, '%')
</if>
<if test="SearchPojo.businesstype != null ">
   or Sa_Business.BusinessType like concat('%', #{SearchPojo.businesstype}, '%')
</if>
<if test="SearchPojo.salestype != null ">
   or Sa_Business.SalesType like concat('%', #{SearchPojo.salestype}, '%')
</if>
<if test="SearchPojo.salesprogress != null ">
   or Sa_Business.SalesProgress like concat('%', #{SearchPojo.salesprogress}, '%')
</if>
<if test="SearchPojo.estimatedamt != null ">
   or Sa_Business.EstimatedAmt like concat('%', #{SearchPojo.estimatedamt}, '%')
</if>
<if test="SearchPojo.marketing != null ">
   or Sa_Business.Marketing like concat('%', #{SearchPojo.marketing}, '%')
</if>
<if test="SearchPojo.status != null ">
   or Sa_Business.Status like concat('%', #{SearchPojo.status}, '%')
</if>
<if test="SearchPojo.workstage != null ">
   or Sa_Business.WorkStage like concat('%', #{SearchPojo.workstage}, '%')
</if>
<if test="SearchPojo.stageid != null ">
   or Sa_Business.Stageid like concat('%', #{SearchPojo.stageid}, '%')
</if>
<if test="SearchPojo.stagetext != null ">
   or Sa_Business.StageText like concat('%', #{SearchPojo.stagetext}, '%')
</if>
<if test="SearchPojo.operatorid != null ">
   or Sa_Business.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
</if>
<if test="SearchPojo.operator != null ">
   or Sa_Business.Operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.summary != null ">
   or Sa_Business.Summary like concat('%', #{SearchPojo.summary}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Sa_Business.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Sa_Business.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Sa_Business.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Sa_Business.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   or Sa_Business.Assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   or Sa_Business.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.statecode != null ">
   or Sa_Business.StateCode like concat('%', #{SearchPojo.statecode}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Sa_Business.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Sa_Business.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Sa_Business.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Sa_Business.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Sa_Business.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   or Sa_Business.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   or Sa_Business.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   or Sa_Business.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   or Sa_Business.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   or Sa_Business.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.deptid != null ">
   or Sa_Business.Deptid like concat('%', #{SearchPojo.deptid}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Sa_Business.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
     
         <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.crm.domain.pojo.SaBusinessPojo">
        <include refid="selectbillVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_Business.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="thand"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="thor"></include>        
         </if>
        </if> 
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="thand">
<if test="SearchPojo.refno != null ">
   and Sa_Business.RefNo like concat('%', #{SearchPojo.refno}, '%')
</if>
<if test="SearchPojo.billtype != null ">
   and Sa_Business.BillType like concat('%', #{SearchPojo.billtype}, '%')
</if>
<if test="SearchPojo.billtitle != null ">
   and Sa_Business.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
</if>
<if test="SearchPojo.groupid != null ">
   and Sa_Business.Groupid like concat('%', #{SearchPojo.groupid}, '%')
</if>
<if test="SearchPojo.address != null ">
   and Sa_Business.Address like concat('%', #{SearchPojo.address}, '%')
</if>
<if test="SearchPojo.linkman != null ">
   and Sa_Business.Linkman like concat('%', #{SearchPojo.linkman}, '%')
</if>
<if test="SearchPojo.tel != null ">
   and Sa_Business.Tel like concat('%', #{SearchPojo.tel}, '%')
</if>
<if test="SearchPojo.fax != null ">
   and Sa_Business.Fax like concat('%', #{SearchPojo.fax}, '%')
</if>
<if test="SearchPojo.reporter != null ">
   and Sa_Business.Reporter like concat('%', #{SearchPojo.reporter}, '%')
</if>
<if test="SearchPojo.source != null ">
   and Sa_Business.Source like concat('%', #{SearchPojo.source}, '%')
</if>
<if test="SearchPojo.businesstype != null ">
   and Sa_Business.BusinessType like concat('%', #{SearchPojo.businesstype}, '%')
</if>
<if test="SearchPojo.salestype != null ">
   and Sa_Business.SalesType like concat('%', #{SearchPojo.salestype}, '%')
</if>
<if test="SearchPojo.salesprogress != null ">
   and Sa_Business.SalesProgress like concat('%', #{SearchPojo.salesprogress}, '%')
</if>
<if test="SearchPojo.estimatedamt != null ">
   and Sa_Business.EstimatedAmt like concat('%', #{SearchPojo.estimatedamt}, '%')
</if>
<if test="SearchPojo.marketing != null ">
   and Sa_Business.Marketing like concat('%', #{SearchPojo.marketing}, '%')
</if>
<if test="SearchPojo.status != null ">
   and Sa_Business.Status like concat('%', #{SearchPojo.status}, '%')
</if>
<if test="SearchPojo.workstage != null ">
   and Sa_Business.WorkStage like concat('%', #{SearchPojo.workstage}, '%')
</if>
<if test="SearchPojo.stageid != null ">
   and Sa_Business.Stageid like concat('%', #{SearchPojo.stageid}, '%')
</if>
<if test="SearchPojo.stagetext != null ">
   and Sa_Business.StageText like concat('%', #{SearchPojo.stagetext}, '%')
</if>
<if test="SearchPojo.operatorid != null ">
   and Sa_Business.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
</if>
<if test="SearchPojo.operator != null ">
   and Sa_Business.Operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.summary != null ">
   and Sa_Business.Summary like concat('%', #{SearchPojo.summary}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Sa_Business.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Sa_Business.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Sa_Business.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Sa_Business.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   and Sa_Business.Assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   and Sa_Business.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.statecode != null ">
   and Sa_Business.StateCode like concat('%', #{SearchPojo.statecode}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Sa_Business.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Sa_Business.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Sa_Business.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Sa_Business.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Sa_Business.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and Sa_Business.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and Sa_Business.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and Sa_Business.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   and Sa_Business.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   and Sa_Business.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.deptid != null ">
   and Sa_Business.Deptid like concat('%', #{SearchPojo.deptid}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Sa_Business.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="thor">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.refno != null ">
   or Sa_Business.RefNo like concat('%', #{SearchPojo.refno}, '%')
</if>
<if test="SearchPojo.billtype != null ">
   or Sa_Business.BillType like concat('%', #{SearchPojo.billtype}, '%')
</if>
<if test="SearchPojo.billtitle != null ">
   or Sa_Business.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
</if>
<if test="SearchPojo.groupid != null ">
   or Sa_Business.Groupid like concat('%', #{SearchPojo.groupid}, '%')
</if>
<if test="SearchPojo.address != null ">
   or Sa_Business.Address like concat('%', #{SearchPojo.address}, '%')
</if>
<if test="SearchPojo.linkman != null ">
   or Sa_Business.Linkman like concat('%', #{SearchPojo.linkman}, '%')
</if>
<if test="SearchPojo.tel != null ">
   or Sa_Business.Tel like concat('%', #{SearchPojo.tel}, '%')
</if>
<if test="SearchPojo.fax != null ">
   or Sa_Business.Fax like concat('%', #{SearchPojo.fax}, '%')
</if>
<if test="SearchPojo.reporter != null ">
   or Sa_Business.Reporter like concat('%', #{SearchPojo.reporter}, '%')
</if>
<if test="SearchPojo.source != null ">
   or Sa_Business.Source like concat('%', #{SearchPojo.source}, '%')
</if>
<if test="SearchPojo.businesstype != null ">
   or Sa_Business.BusinessType like concat('%', #{SearchPojo.businesstype}, '%')
</if>
<if test="SearchPojo.salestype != null ">
   or Sa_Business.SalesType like concat('%', #{SearchPojo.salestype}, '%')
</if>
<if test="SearchPojo.salesprogress != null ">
   or Sa_Business.SalesProgress like concat('%', #{SearchPojo.salesprogress}, '%')
</if>
<if test="SearchPojo.estimatedamt != null ">
   or Sa_Business.EstimatedAmt like concat('%', #{SearchPojo.estimatedamt}, '%')
</if>
<if test="SearchPojo.marketing != null ">
   or Sa_Business.Marketing like concat('%', #{SearchPojo.marketing}, '%')
</if>
<if test="SearchPojo.status != null ">
   or Sa_Business.Status like concat('%', #{SearchPojo.status}, '%')
</if>
<if test="SearchPojo.workstage != null ">
   or Sa_Business.WorkStage like concat('%', #{SearchPojo.workstage}, '%')
</if>
<if test="SearchPojo.stageid != null ">
   or Sa_Business.Stageid like concat('%', #{SearchPojo.stageid}, '%')
</if>
<if test="SearchPojo.stagetext != null ">
   or Sa_Business.StageText like concat('%', #{SearchPojo.stagetext}, '%')
</if>
<if test="SearchPojo.operatorid != null ">
   or Sa_Business.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
</if>
<if test="SearchPojo.operator != null ">
   or Sa_Business.Operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.summary != null ">
   or Sa_Business.Summary like concat('%', #{SearchPojo.summary}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Sa_Business.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Sa_Business.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Sa_Business.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Sa_Business.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   or Sa_Business.Assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   or Sa_Business.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.statecode != null ">
   or Sa_Business.StateCode like concat('%', #{SearchPojo.statecode}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Sa_Business.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Sa_Business.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Sa_Business.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Sa_Business.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Sa_Business.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   or Sa_Business.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   or Sa_Business.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   or Sa_Business.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   or Sa_Business.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   or Sa_Business.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.deptid != null ">
   or Sa_Business.Deptid like concat('%', #{SearchPojo.deptid}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Sa_Business.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
     
    <!--新增所有列-->
    <insert id="insert" >
        insert into Sa_Business(id, RefNo, BillType, BillTitle, BillDate, BillTaxAmount, BillAmount, BillTaxTotal, ContractAmount, Groupid, Address, Linkman, Tel, Fax, Reporter, Source, BusinessType, SalesType, SalesProgress, EstimatedAmt, EstimatedTime, Marketing, Status, WorkStage, Stageid, StageText, StageResult, Operatorid, Operator, Summary, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Assessor, Assessorid, AssessDate, OaFlowMark, StateCode, StateDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Deptid, Tenantid, TenantName, Revision)
        values (#{id}, #{refno}, #{billtype}, #{billtitle}, #{billdate}, #{billtaxamount}, #{billamount}, #{billtaxtotal}, #{contractamount}, #{groupid}, #{address}, #{linkman}, #{tel}, #{fax}, #{reporter}, #{source}, #{businesstype}, #{salestype}, #{salesprogress}, #{estimatedamt}, #{estimatedtime}, #{marketing}, #{status}, #{workstage}, #{stageid}, #{stagetext}, #{stageresult}, #{operatorid}, #{operator}, #{summary}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{assessor}, #{assessorid}, #{assessdate}, #{oaflowmark}, #{statecode}, #{statedate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{deptid}, #{tenantid}, #{tenantname}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_Business
        <set>
            <if test="refno != null ">
                RefNo =#{refno},
            </if>
            <if test="billtype != null ">
                BillType =#{billtype},
            </if>
            <if test="billtitle != null ">
                BillTitle =#{billtitle},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="billtaxamount != null">
                BillTaxAmount =#{billtaxamount},
            </if>
            <if test="billamount != null">
                BillAmount =#{billamount},
            </if>
            <if test="billtaxtotal != null">
                BillTaxTotal =#{billtaxtotal},
            </if>
            <if test="contractamount != null">
                ContractAmount =#{contractamount},
            </if>
            <if test="groupid != null ">
                Groupid =#{groupid},
            </if>
            <if test="address != null ">
                Address =#{address},
            </if>
            <if test="linkman != null ">
                Linkman =#{linkman},
            </if>
            <if test="tel != null ">
                Tel =#{tel},
            </if>
            <if test="fax != null ">
                Fax =#{fax},
            </if>
            <if test="reporter != null ">
                Reporter =#{reporter},
            </if>
            <if test="source != null ">
                Source =#{source},
            </if>
            <if test="businesstype != null ">
                BusinessType =#{businesstype},
            </if>
            <if test="salestype != null ">
                SalesType =#{salestype},
            </if>
            <if test="salesprogress != null ">
                SalesProgress =#{salesprogress},
            </if>
            <if test="estimatedamt != null ">
                EstimatedAmt =#{estimatedamt},
            </if>
            <if test="estimatedtime != null">
                EstimatedTime =#{estimatedtime},
            </if>
            <if test="marketing != null ">
                Marketing =#{marketing},
            </if>
            <if test="status != null ">
                Status =#{status},
            </if>
            <if test="workstage != null ">
                WorkStage =#{workstage},
            </if>
            <if test="stageid != null ">
                Stageid =#{stageid},
            </if>
            <if test="stagetext != null ">
                StageText =#{stagetext},
            </if>
            <if test="stageresult != null">
                StageResult =#{stageresult},
            </if>
            <if test="operatorid != null ">
                Operatorid =#{operatorid},
            </if>
            <if test="operator != null ">
                Operator =#{operator},
            </if>
            <if test="summary != null ">
                Summary =#{summary},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="assessor != null ">
                Assessor =#{assessor},
            </if>
            <if test="assessorid != null ">
                Assessorid =#{assessorid},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="oaflowmark != null">
                OaFlowMark =#{oaflowmark},
            </if>
            <if test="statecode != null ">
                StateCode =#{statecode},
            </if>
            <if test="statedate != null">
                StateDate =#{statedate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="deptid != null ">
                Deptid =#{deptid},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_Business where id = #{key} 
    </delete>
                                                                                                                            <!--通过主键审核数据-->
    <update id="approval">
        update Sa_Business SET
            Assessor = #{assessor},
            Assessorid = #{assessorid},
            AssessDate = #{assessdate},
            Revision=Revision+1
        where id = #{id}
    </update>
                                                                                <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String" parameterType="inks.service.sa.crm.domain.pojo.SaBusinessPojo">
        select
          id
        from Sa_BusinessItem
        where Pid = #{id}
        <if test="item !=null and item.size()>0">
         and id not in
        <foreach collection="item" open="(" close=")" separator="," item="item">
            <if test="item.id != null">
                #{item.id}
            </if>
            <if test="item.id == null">
                 ''
            </if>
        </foreach>
         </if>
    </select>

    <select id="getDelStageIds" resultType="java.lang.String">
        select
          id
        from Sa_BusinessStage
        where Pid = #{id}
        <if test="stage !=null and stage.size()>0">
         and id not in
        <foreach collection="stage" open="(" close=")" separator="," item="stage">
            <if test="stage.id != null">
                #{stage.id}
            </if>
            <if test="stage.id == null">
                 ''
            </if>
        </foreach>
         </if>
    </select>

    <update id="updateOaflowmark">
        update Sa_Business
        SET OaFlowMark = #{oaflowmark}
        where id = #{id}
    </update>
</mapper>

