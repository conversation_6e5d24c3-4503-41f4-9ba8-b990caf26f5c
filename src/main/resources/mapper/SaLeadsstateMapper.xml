<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.crm.mapper.SaLeadsstateMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.crm.domain.pojo.SaLeadsstatePojo">
        select
          id, StateName, StateCode, StateColor, StateIcon, RowNum, CreateByid, CreateBy, CreateDate, Listerid, Lister, ModifyDate, ModuleCode, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, Deptid, TenantName, Revision        from Sa_LeadsState
        where Sa_LeadsState.id = #{key}
    </select>
    <sql id="selectSaLeadsstateVo">
         select
          id, StateName, StateCode, StateColor, StateIcon, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ModuleCode, Custom1, Custom2, Custom3, Custom4, Custom5, <PERSON>anti<PERSON>, Deptid, TenantName, Revision        from Sa_LeadsState
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.crm.domain.pojo.SaLeadsstatePojo">
        <include refid="selectSaLeadsstateVo"/>
         where 1 = 1
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_LeadsState.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">
           <include refid="and"></include>
         </if>
         <if test="SearchType==1">
           <include refid="or"></include>
         </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.statename != null ">
   and Sa_LeadsState.StateName like concat('%', #{SearchPojo.statename}, '%')
</if>
<if test="SearchPojo.statecode != null ">
   and Sa_LeadsState.StateCode like concat('%', #{SearchPojo.statecode}, '%')
</if>
<if test="SearchPojo.statecolor != null ">
   and Sa_LeadsState.StateColor like concat('%', #{SearchPojo.statecolor}, '%')
</if>
<if test="SearchPojo.stateicon != null ">
   and Sa_LeadsState.StateIcon like concat('%', #{SearchPojo.stateicon}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Sa_LeadsState.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Sa_LeadsState.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Sa_LeadsState.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Sa_LeadsState.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.modulecode != null ">
   and Sa_LeadsState.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Sa_LeadsState.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Sa_LeadsState.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Sa_LeadsState.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Sa_LeadsState.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Sa_LeadsState.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.deptid != null ">
   and Sa_LeadsState.Deptid like concat('%', #{SearchPojo.deptid}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Sa_LeadsState.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.statename != null ">
   or Sa_LeadsState.StateName like concat('%', #{SearchPojo.statename}, '%')
</if>
<if test="SearchPojo.statecode != null ">
   or Sa_LeadsState.StateCode like concat('%', #{SearchPojo.statecode}, '%')
</if>
<if test="SearchPojo.statecolor != null ">
   or Sa_LeadsState.StateColor like concat('%', #{SearchPojo.statecolor}, '%')
</if>
<if test="SearchPojo.stateicon != null ">
   or Sa_LeadsState.StateIcon like concat('%', #{SearchPojo.stateicon}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Sa_LeadsState.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Sa_LeadsState.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Sa_LeadsState.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Sa_LeadsState.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.modulecode != null ">
   or Sa_LeadsState.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Sa_LeadsState.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Sa_LeadsState.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Sa_LeadsState.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Sa_LeadsState.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Sa_LeadsState.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.deptid != null ">
   or Sa_LeadsState.Deptid like concat('%', #{SearchPojo.deptid}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Sa_LeadsState.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Sa_LeadsState(id, StateName, StateCode, StateColor, StateIcon, RowNum, CreateByid, CreateBy, CreateDate, Listerid, Lister, ModifyDate, ModuleCode, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, Deptid, TenantName, Revision)
        values (#{id}, #{statename}, #{statecode}, #{statecolor}, #{stateicon}, #{rownum}, #{createbyid}, #{createby}, #{createdate}, #{listerid}, #{lister}, #{modifydate}, #{modulecode}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{tenantid}, #{deptid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_LeadsState
        <set>
            <if test="statename != null ">
                StateName =#{statename},
            </if>
            <if test="statecode != null ">
                StateCode =#{statecode},
            </if>
            <if test="statecolor != null ">
                StateColor =#{statecolor},
            </if>
            <if test="stateicon != null ">
                StateIcon =#{stateicon},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="modulecode != null ">
                ModuleCode =#{modulecode},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="deptid != null ">
                Deptid =#{deptid},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_LeadsState where id = #{key}
    </delete>
                                                                                        </mapper>

